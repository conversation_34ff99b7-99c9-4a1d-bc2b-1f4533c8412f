# EeyoreHumanEvaluation Backend Service

This is the backend service for the Eeyore Human Evaluation system, providing APIs for signature creation, validation, and search functionality.

## 🚀 Quick Start

### Prerequisites
- Java 11+
- Brazil CLI
- AWS credentials configured

## 🔧 Configuration

The service uses environment variables for configuration form definition.yaml. Key variables include:

### Required Environment Variables

| Variable | Description | Example                 |
|----------|-------------|-------------------------|
| `AWS_REGION` | AWS region for deployment | `us-east-1`             |
| `AWS_ACCOUNT_ID` | AWS account ID | `************`          |
| `SERVICE_STAGE_NAME` | Service stage (alpha/beta/gamma/prod) | `alpha`                 |
| `APPLICATION_NAME` | Application name | `HumanEval`             |
| `IS_ONEPOD` | Whether running in OnePod environment | `false`                 |
| `ALLOWED_ORIGINS` | JSON array of allowed CORS origins | `["https://example.com"]` |

### Optional Environment Variables

| Variable | Description | Default | Usage |
|----------|-------------|---------|-------|
| `RUNNING_IN_DEVELOPER_ACCOUNT` | Whether running in a personal developer account | `false` | Set to `true` for personal stack deployment |

## 🏗️ Deployment

### Personal Developer Stack Deployment

For personal development stacks, you need to configure the environment properly:

1. **Set the developer account flag in definition.yaml or in your local environment:**
   ```bash
   export RUNNING_IN_DEVELOPER_ACCOUNT=true
   export THREAT_STORE_ENDPOINT=<your endpoint>
   ```

2. **Deploy using cdk package:**
   > More instructions on cdk deployment in `EeyoreHumanEvaluationCDK` repository.

### Environment-Specific Deployment

The service automatically configures external service connections based on the stage:

- **ALPHA Stage:** Uses test/mock services, no real external service connections
- **BETA/GAMMA/PROD Stages:** Uses real external service clients with proper authentication

### External Service Configuration

The service integrates with several external services:

#### Woozle ThreatStore
- **Personal Accounts:** Uses `STSAssumeRoleSessionCredentialsProvider` with role session duration
- **Shared Environments:** Uses woozle access role credentials with 60-minute session duration
- **Environment Variables:**
  - `THREAT_STORE_ROLE_ARN` (optional override)
  - `THREAT_STORE_QUALIFIER` (optional override)

## 🧪 Testing

### Running All Tests
```bash
brazil-build test
```

### Test Coverage
The project maintains ≥70% test coverage. Coverage reports are generated during the build process.

### Mock Services
For development and unit testing, the service includes mock implementations:
- `TestWoozleServiceModule` - Mock Woozle ThreatStore service
- `TestEriskayClientModule` - Mock Eriskay client

## 📁 Project Structure

```
src/
├── main/kotlin/com/amazon/eeyore/humaneval/
│   ├── activity/           # API endpoint implementations
│   ├── config/            # Configuration classes
│   ├── guice/             # Dependency injection modules
│   ├── service/           # Business logic services
│   └── HumanEval.kt       # Main application entry point
└── test/kotlin/           # Test files
```

## 🔍 Key Features

### Search Signatures API
- Search signatures by status (LIVE, SHADOW, LIVE_REFERRAL, etc.)
- Pagination support with `nextToken` and `maxResults`
- Includes lineage and threat classification information
- Proper authorization and error handling

### Signature Creation
- Batch signature creation with validation
- Integration with Woozle ThreatStore
- Comprehensive error handling and reporting

### Authentication & Authorization
- Integration with Brass authentication
- Resource-based authorization checks
- Support for both service and user authentication

## 🛠️ Development

### Code Quality
The project enforces code quality through:
- **Detekt** - Static code analysis
- **Ktlint** - Code formatting
- **Test Coverage** - Minimum 70% coverage requirement

## 🚨 Important Notes

### Environment Variable Security
- **Never commit sensitive environment variables to code**
- Use proper AWS IAM roles and policies for authentication
- Ensure `RUNNING_IN_DEVELOPER_ACCOUNT` is set correctly for your deployment target

### Deployment Safety
- **Personal Development:** Set `RUNNING_IN_DEVELOPER_ACCOUNT=true`
- **Shared Environments:** Set `RUNNING_IN_DEVELOPER_ACCOUNT=false` or leave unset
- Always verify your environment configuration before deployment

### External Service Access
- Personal accounts use cross-account role assumption
- Shared environments use service-specific credentials
- Mock services are used in ALPHA stage for testing

## 📚 Additional Resources

- [Brazil Build System](https://builderhub.corp.amazon.com/docs/brazil-cli/)
- [Coral Service Framework](https://builderhub.corp.amazon.com/docs/coral/)


## 📞 Support

For questions or issues, please reach out to the AdRisk Intelligence team.
