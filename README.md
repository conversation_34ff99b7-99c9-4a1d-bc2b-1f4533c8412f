# What?
This is the ui package for the Eeyore Human evaluation. It's a typical React app with TypeScript, although note that we're not using modern React (it moves so fast).

This website is running on:
- [Dev - only for integration tests, no real data on it](https://alpha.he.eeyore.advertising.amazon.dev)
- [Beta - shows beta traffic](https://beta.he.eeyore.advertising.amazon.dev)
- [Gamma - shows gamma traffic](https://gamma.he.eeyore.advertising.amazon.dev)
- [Prod - shows prod traffic](https://prod.he.eeyore.advertising.amazon.dev)

# Prerequisites
* Install [toolbox](https://w.amazon.com/index.php/BuilderToolbox/GettingStarted#Install_Toolbox)
* Install socat (for mac: `brew install socat`, `sudo yum install socat` on dev-desktop)

# Authentication
This site makes use of the [MidwayIdentityCredentialProvider](https://code.amazon.com/packages/MidwayIdentityCredentialProvider) - which is a [Axios](https://github.com/axios/axios) wrapped client that you can use to call an API Gateway endpoint. The client exchanges your Midway identity for Cognito credentials - thus ensuring you have full AuthN all the way to API Gateway.

# Deployment & Packaging
The setup is a standard CDK pipeline, with this website deployed in a pipeline via CloudFormation. The website is deployed to S3, which has CloudFront in front of it and that's where the website is actually "hosted". On package builder, we run `brazil-build release` which in turn runs `npm run build`. The `build` step then just makes a production version of your app by bundling everything together. (JS, CSS etc)

Note: CloudFront caches the file on it's edge cache for a day, so changes might not be available after a deployment. We attempt to force an invalidation during deployment, and this *should* work, however it's been observed not to.

# Local Development
There are two ways to develop locally, either run it to use the api deployed on the dev account, or to use the api server running locally in RDE

## Use the deployed API
This gives you to confirm that authentication is also working, but in order to use Midway Auth - we must be using the website on a domain that ends with either `.amazon.com` or `.a2z.com`. `localhost.a2z.com` resolves to `127.0.0.1`, so we can use this. If it doesn't, you need to edit your hosts file and add the line `127.0.0.1 localhost.a2z.com`.

* run `brazil-build install`
* run `brazil-build start`
* goto [https://localhost.a2z.com](https://localhost.a2z.com) - (You will get a cert exception - this is a **RARE**
  moment in life when it is OK to bypass this warning. )


**Note:** if you're using VSCode, it will attempt to spawn and/or forward you to https://localhost.a2z.com:3443. You must not use this 3443 port otherwise the authentication **will not work**.

## Use the api running in RDE

**WARNING** I've been unable to get this step to work cjmc@ - TODO fix

* open another terminal > cd EeyoreHumanEvaluationLambda
* run `rde workflow run` to build the rde stack (needed after each change in the backend api package)
* run `rde workflow run start-backend` to run the backend server.

**Note: **
We need to tell the Midway JavaScript Client some details. Normally, the client fetches this data
from `https://${host}/settings.json`. When deployed in a pipeline, the BONESConstructs resource will set up
the `settings.json` file automatically. Locally however, we want the JS client to use different settings. In
your `public` folder - you'll see a file call `local-settings.json`. It currently is configured for the beta account and
us-east-1 region.

## eslint and prettier

We use eslint to enforce some coding guidelines. Run `brazil-build lint` to see any violations. To format the code, we
use prettier. Run `brazil-build format` to automatically format all code files.

Both tools are run on release to ensure that the linting rules are followed and that all files are correctly formatted.
