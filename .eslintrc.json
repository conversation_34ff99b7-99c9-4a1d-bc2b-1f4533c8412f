{
  "extends": [
    "eslint:recommended",
    "react-app",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended"
    // disabled for now, requires a lot of additional types
    // "plugin:@typescript-eslint/recommended-requiring-type-checking",
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": [
      "./tsconfig.json"
    ]
  },
  "plugins": [
    "react",
    "@typescript-eslint"
  ],
  "rules": {
    "no-inferrable-types": 0,
    "react/jsx-boolean-value": [
      2,
      "never"
    ],
    "quotes": [
      2,
      "double",
      {
        "avoidEscape": true
      }
    ],
    "semi": 0,
    "@typescript-eslint/semi": 2,
    "no-return-await": 2,
    "prefer-const": 2,
    "require-await": 1,
    "no-console": 1
  },
  "ignorePatterns": [
    "src/**/*.test.ts",
    "src/**/*.test.tsx",
    "src/setupTests.ts",
    "config/**/*.js",
    "node_modules/**/*.ts",
    "node_modules/**/*.tsx",
    "coverage/lcov-report/*.js"
  ]
}