{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es2016", "es2017.object", "es2017.string"], "declaration": true, "outDir": "./dist/lib", "declarationDir": "./dist/types", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": true, "typeRoots": ["./node_modules/@types"]}, "exclude": ["cdk.out", "build", "node_modules", "dist"]}