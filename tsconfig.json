{"compilerOptions": {"rootDir": "src", "outDir": "build/dist", "module": "esnext", "target": "es6", "downlevelIteration": true, "lib": ["es6", "dom"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "allowJs": true, "checkJs": true, "alwaysStrict": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noImplicitAny": false}, "exclude": ["node_modules/", "build", "scripts", "acceptance-tests", "webpack", "jest", "src/setupTests.ts", "src/**/*.js", "src/**/*.test.*"], "include": ["src"]}