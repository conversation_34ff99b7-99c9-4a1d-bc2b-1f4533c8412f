version: 2016-11-18
stack:
  name: HumanEvalStack
  ec2MetadataURI: configuration/rde_ec2_metadata.yaml
  # Please see the following guide on how to configure credentials: https://builderhub.corp.amazon.com/docs/rde/user-guide/howto.html#use-aws-credentials-on-macos
  # You can use RDE placeholders https://builderhub.corp.amazon.com/docs/rde/user-guide/placeholders.html to customize account settings
  # and the rest of the definition file.

  devAccount:
    type: Conduit
    roleArn: arn:aws:iam::${AWS_PERSONAL_ACCOUNT_ID}:role/IibsAdminAccess-DO-NOT-DELETE

  applications:
    EeyoreHumanEvaluation:
      type: container
      environment:
        variables:
          AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/role/IibsAdminAccess-DO-NOT-DELETE"
          ECS_CONTAINER_METADATA_URI: "http://*************/v3"
          AWS_REGION: "us-east-1" #UPDATE ME IF REGION IS DIFFERENT
          SERVICE_STAGE_NAME: "alpha"
          IS_ONEPOD: "false"
          APPLICATION_NAME: "HumanEval"
          AWS_ACCOUNT_ID: "${AWS_PERSONAL_ACCOUNT_ID}"
          ALLOWED_ORIGINS: "[\"https://dev-${USER}.he.eeyore.advertising.amazon.dev\"]"
          RUNNING_IN_DEVELOPER_ACCOUNT: "false"

      versionSet: EeyoreHumanEvaluation/mainline
      codeURI: ../../
      packages:
        EeyoreHumanEvaluation:
          buildTarget: [release, -PlogConfig=log4j2-local.xml]

          isApplicationTarget: true

        EeyoreHumanEvaluationImageBuild:
          buildTarget: release
          isBatsParameter: true
      mountPoints:
        - sourceURI: mounts/apollo-shim-static-replacements
          destinationURI: /opt/apollo-shim-static-replacements
        - sourceURI: mounts/logs/service
          destinationURI: /apollo/var/env/EeyoreHumanEvaluation/output/logs

      networkMappings:
        - name: eeyore-human-eval.localhost
          internalPort: 8080
          externalPort: 8080
          exposedPort: 127.0.0.1:8080
          protocol: http
        - name: secure.eeyore-human-eval.localhost
          internalPort: 8443
          externalPort: 8443
          exposedPort: 127.0.0.1:8443
          protocol: tcp

steps:
  build:
    type: build
  container-build:
    type: build
    arguments:
      container: true
  deploy:
    type: deploy
  container-smoke-test:
    type: custom
    arguments:
      command: sleep 1 && docker ps -a --format "{{.Status}}" -f name=HumanEval | grep -qvi exited
  ec2md-smoke-test: # sends a simple curl command to check that the EC2 Metadata endpoint is available inside the container application
    type: stackExec
    arguments:
      applications:
        - Eriskay
      command: |
        TOKEN=$(curl --silent --fail --retry 3 -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
        [ ! -z "$TOKEN" ] && curl --silent --retry 3 -H "X-aws-ec2-metadata-token: $TOKEN" -v http://***************/latest/meta-data/
  ecs-metadata-smoke-test:
    # sends a simple curl command to check that the ECS Metadata endpoint is available inside the container application
    type: stackExec
    arguments:
      applications:
        - Eriskay
      command: curl --silent --fail http://*************/v3
  exposed-port-smoke-test:
    type: custom
    arguments:
      command: curl localhost:8080/deep_ping
  internal-port-smoke-test: # sends a simple curl command to check that Coral stack is listening on port 8080 inside the container
    type: stackExec
    arguments:
      applications:
        - Eriskay
      command: curl --silent --fail localhost:8080/deep_ping
  internal-dns-smoke-test: # sends a simple curl command to check that rde correctly exposed the coral server inside the Personal Stack network
    type: stackExec
    arguments:
      applications:
        - Eriskay
      command: curl --silent --fail my.service.endpoint.com:8080/ping
workflows:
  default:
    - build
    - deploy
  validate:
    - container-smoke-test
    - ec2md-smoke-test
    - ecs-metadata-smoke-test
    - exposed-port-smoke-test
    - internal-port-smoke-test
    - internal-dns-smoke-test
