import io.gitlab.arturbosch.detekt.Detekt
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion

plugins {
    java
    jacoco
    checkstyle
    id("brazil-gradle")
    id("brazil-gradle-java-presets")
    id("brazil-gradle-kotlin-presets")
    id("kotlintrails")
    id("kotlin")
    id("kotlin-kapt")
    id("org.jlleitschuh.gradle.ktlint")
    id("io.gitlab.arturbosch.detekt")
    id("brazil-generate-wrapper") apply (false)
    id("brazil-validate-classpath")
}

brazilGradle {
    configureBasicDependencies()
}

kapt {
    correctErrorTypes = true
    includeCompileClasspath = false
}

val coverageExclusions = listOf(
    // Exclude the main class because it can't be unit tested
    "**/com/amazon/eeyore/humaneval/HumanEvalKt*.class",

    // Exclude Guice config modules which make external calls or cannot be mocked
    "**/com/amazon/eeyore/humaneval/guice/AwsModule.class",
    "**/com/amazon/eeyore/humaneval/guice/EriskayClientModule.class",
    "**/com/amazon/eeyore/humaneval/guice/WoozleClientModule.class",
    "**/com/amazon/eeyore/humaneval/guice/ServiceModule.class",
    "**/com/amazon/eeyore/humaneval/guice/ActivityModule.class",
    "**/com/amazon/eeyore/humaneval/guice/CoralServerModule.class",
    "**/com/amazon/eeyore/humaneval/guice/HumanEvalEnvironmentVariableServiceConfiguration.class",
    "**/com/amazon/eeyore/humaneval/guice/HumanEvalServerModule.class",
    "**/com/amazon/eeyore/humaneval/service/ThreatStoreService.class",
    "**/humaneval/config/**.class",
)

val codeCoverageFiles = sourceSets.main.get().output.asFileTree.matching {
    exclude(coverageExclusions)
}
tasks.withType<JacocoReport> {
    classDirectories.setFrom(codeCoverageFiles)
}

tasks {
    jacocoTestCoverageVerification {
        violationRules {
            rule { limit { minimum = BigDecimal.valueOf(0.70) } }
            classDirectories.setFrom(codeCoverageFiles)
        }
    }
    check {
        dependsOn(jacocoTestCoverageVerification)
    }
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
        apiVersion.set(KotlinVersion.KOTLIN_1_9)
        languageVersion.set(KotlinVersion.KOTLIN_1_9)
    }
}

configure<com.amazon.brazil.gradle.validate.java.ValidateJavaClasspathExtension> {
    configurations.set(listOf("compileClasspath", "runtimeClasspath"))
    failOnConflicts.set(true)
}

configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
    reporters {
        reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.PLAIN)
        reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.CHECKSTYLE)
    }
    ignoreFailures.set(false)
}

tasks.withType<Detekt>().configureEach {
    buildUponDefaultConfig = true
    ignoreFailures = false
    config.setFrom("detekt.yml")
}

tasks.withType<Test> {
    // Provides coral config path, allows checking of the creation of client code (without actually contacting it)
    environment("CORAL_CONFIG_PATH", brazilGradle.path("run.coralconfig"))
    useJUnitPlatform()
}

val configurationFiles = copySpec {
    from("${brazilGradle.path("package-src-root")}/configuration") {
        include("ApolloCmd/**/*")
        include("aws_code_deploy/**/*")
        include("aws_lambda/**")
        include("bin/**/*")
        include("brazil-config/**/*")
        include("log-configuration/**/*")
        include("monitoring/**/*")
        include("rde_ec2_metadata.yaml")
    }
}

val copyConfigurationToBuild = tasks.register<Copy>("copyConfigurationToBuild") {
    into("${brazilGradle.buildDir}")
    with(configurationFiles)
}

val copyConfigurationToBuildPrivate = tasks.register<Copy>("copyConfigurationToBuildPrivate") {
    from(brazilGradle.path("run.configfarm.brazil-config")) {
        include("brazil-config/**/*")
    }
    from(brazilGradle.path("run.configfarm.certs")) {
        include("certs/**/*")
    }
    into("${brazilGradle.buildDir}/private")
    with(configurationFiles)

    project.mkdir("${brazilGradle.buildDir}/private/var/tmp")
}

dependencies {
    brazilGradle.tool("CoralGenerator").forEach {
        compileOnly(it)
        "kapt"(it)
        "kaptTest"(it)
        testCompileOnly(it)
    }
    brazilGradle.build("CoralService").forEach {
        compileOnly(it)
        "kapt"(it)
        "kaptTest"(it)
        testCompileOnly(it)
    }
    brazilGradle.tool("DetektCli").forEach {
        configurations["detekt"](it)
        configurations["detektPlugins"](it)
    }

    (brazilGradle.run() - brazilGradle.build()).forEach { runtimeOnly(it) }
}

/*
 Generate the Apollo script to start your service.
 Note: When modifying values here remember to also update the server target.
*/
val apolloScript = tasks.register<com.amazon.brazil.gradle.launcher.GenerateWrapperTask>("apolloScript") {
    val logConfig = properties["logConfig"] ?: "log4j2-container.xml"
    println("Using logConfig \"$logConfig\"")
    target("${brazilGradle.buildDir}/bin/run-service.sh")
    main("com.amazon.eeyore.humaneval.HumanEvalKt")

    jvmArgs("-XX:MaxRAMPercentage=90.0")
    jvmArgs("-XX:MaxGCPauseMillis=100")
    jvmArgs("-XX:+PerfDisableSharedMem")
    // Kill on OOM (logscan for PMAdmin.log will trigger an alarm)
    jvmArgs("-XX:+ExitOnOutOfMemoryError")
    jvmArgs("-XX:+ErrorFileToStderr")

    environment("CORAL_CONFIG_PATH", "\${ENVROOT}/coral-config", false)

    systemProperty("sun.net.inetaddr.negative.ttl", "1")
    systemProperty("javax.net.ssl.trustStore", "\${ENVROOT}/certs/InternalAndExternalTrustStore.jks", false)
    systemProperty("javax.net.ssl.trustStorePassword", "amazon")
    systemProperty("log4j.configurationFile", "file:\${ENVROOT}/log-configuration/$logConfig", false)
    systemProperty("java.util.logging.manager", "org.apache.logging.log4j.jul.LogManager")
    systemProperty("Log4jContextSelector", "org.apache.logging.log4j.core.async.AsyncLoggerContextSelector")
    systemProperty("root", "\${ENVROOT}", false)

    args("--root=\${ENVROOT}", false)
    args("--domain=\${DOMAIN}", false)
    args("--realm=\${REALM}", false)
}

tasks.named("build").configure {
    dependsOn(copyConfigurationToBuild, apolloScript)
}

tasks.named("coverageReportSummary").configure {
    dependsOn(copyConfigurationToBuild)
}
