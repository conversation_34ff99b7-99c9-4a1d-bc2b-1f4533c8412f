{"parserOptions": {"project": "tsconfig.json"}, "plugins": ["filenames"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "prettier"], "rules": {"quotes": "off", "@typescript-eslint/quotes": "off", "@typescript-eslint/no-non-null-assertion": "off", "semi": "off", "@typescript-eslint/semi": "error", "comma-dangle": "error", "@typescript-eslint/prefer-readonly": "error", "@typescript-eslint/array-type": ["error", {"default": "array", "readonly": "generic"}], "filenames/match-regex": ["error", "^[a-z]+[a-z0-9-]*[a-z0-9]+$"], "@typescript-eslint/ban-types": ["error", {"types": {"Function": false}, "extendDefaults": true}]}, "ignorePatterns": ["*.d.ts", "*.generated.ts"]}