import {DeploymentStack, DeploymentStackProps} from "@amzn/pipelines";
import {App} from "aws-cdk-lib";
import {
    AccountPrincipal,
    CompositePrincipal,
    Effect,
    ManagedPolicy,
    PolicyStatement,
    Role,
    ServicePrincipal
} from "aws-cdk-lib/aws-iam";
import {HostedZone} from "aws-cdk-lib/aws-route53";

interface RootHostedZoneStackProps extends DeploymentStackProps {
    /**
     * Name of root zone (e.g. eeyore.advertising.amazon.dev).
     */
    readonly rootHostedZoneName: string;
    /**
     * Role which other accounts can use to delegate zones.
     */
    readonly dnsUpdateRoleName: string;
    /**
     * List of accounts which can assume the role to update DNS.
     */
    readonly accountsAllowedToUpdate: string[];
}

/**
 * Hosts the root zone for DNS management. This DNS Zone is delegated to us by the SuperNova program, which owns the
 * a2z.com root domain, and manages advertising.a2z.com.
 *
 * Using the SuperNova tool, we delegated eeyore.advertising.a2z.com to this stack - which only exists in a single
 * prod account. To set-up subdomains for each stage/region, we delegate to the corresponding stack. Each stack can
 * assume the DNS Updater role, and in there the BONES Cross-Account DNS Delegator sets up the correct NS records.
 */
export class RootHostedZoneStack extends DeploymentStack {
    public readonly hostedZone: HostedZone;

    constructor(app: App, id: string, props: RootHostedZoneStackProps) {
        super(app, id, props);

        new Role(this, "SuperNovaRole", {
            roleName: "Nova-DO-NOT-DELETE",
            assumedBy: new ServicePrincipal("nova.aws.internal"),
            managedPolicies: [
                ManagedPolicy.fromAwsManagedPolicyName("AmazonRoute53FullAccess"),
                ManagedPolicy.fromAwsManagedPolicyName("SecurityAudit")
            ]
        });

        this.hostedZone = new HostedZone(this, `RootHostedZone`, {
            zoneName: props.rootHostedZoneName
        });

        const dnsUpdateRole = new Role(this, "DNSUpdateRole", {
            roleName: props.dnsUpdateRoleName,
            assumedBy: new CompositePrincipal(...props.accountsAllowedToUpdate.map(a => new AccountPrincipal(a)))
        });
        dnsUpdateRole.addToPolicy(
            new PolicyStatement({
                actions: ["route53:GetHostedZone", "route53:ChangeResourceRecordSets"],
                effect: Effect.ALLOW,
                resources: [this.hostedZone.hostedZoneArn]
            })
        );
        dnsUpdateRole.addToPolicy(
            new PolicyStatement({
                actions: ["route53:ListHostedZonesByName"],
                effect: Effect.ALLOW,
                resources: ["*"]
            })
        );
    }
}
