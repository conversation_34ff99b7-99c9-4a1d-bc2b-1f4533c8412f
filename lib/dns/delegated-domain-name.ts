import {Stack} from "aws-cdk-lib";
import {CrossAccountZoneDelegationRecord, HostedZone, IHostedZone} from "aws-cdk-lib/aws-route53";
import {Role} from "aws-cdk-lib/aws-iam";
import {Construct} from "constructs";

export interface DelegatedHostedZoneProps {
    /**
     * The domain name under which the stack will be available.
     */
    readonly domainNameToDelegate: string;
    /**
     * ID of root hosted zone under which this delegated domain name is created.
     * This is for example the ID of the eeyore.advertising.amazon.dev zone.
     */
    readonly rootHostedZoneID?: string;
    /**
     * Role to use to update the root hosted zone.
     */
    readonly rootHostedZoneIAMRole?: string;
}

// This construct takes a few parts of a domain name and generates an R53 HostedZone for it. If a role and root zone
// are supplied, then we will do the delegation automatically.
// For your dev stack, you'll need to do it manually. Unlike Tigger, we don't generate a certificate, because BONES will
// do that for us.
// https://code.amazon.com/packages/DnsDelegationCdkConstructs/blobs/mainline-2.0/--/README.md
export class DelegatedDomainName extends Construct {
    public readonly hostedZone: IHostedZone;
    public readonly crossAccountDelegationRecord: CrossAccountZoneDelegationRecord | undefined;

    constructor(parent: Stack, name: string, props: DelegatedHostedZoneProps) {
        super(parent, name);

        this.hostedZone = new HostedZone(this, `RootHostedZone`, {
            zoneName: props.domainNameToDelegate
        });

        if (props.rootHostedZoneID && props.rootHostedZoneIAMRole) {
            this.crossAccountDelegationRecord = new CrossAccountZoneDelegationRecord(
                this,
                "CrossAccountZoneDelegation",
                {
                    delegatedZone: this.hostedZone,
                    parentHostedZoneId: props.rootHostedZoneID,
                    delegationRole: Role.fromRoleArn(this, "DNSDelegateRole", props.rootHostedZoneIAMRole)
                }
            );
        } else {
            this.crossAccountDelegationRecord = undefined;
        }
    }
}
