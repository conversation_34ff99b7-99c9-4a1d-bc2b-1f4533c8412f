import {StageName} from "../stage";

type StageToBrassConfig = {
    [stageName in StageName]: {
        endpoints: string[];
        publicCoralQualifiers: string[];
        allegianceCoralQualifiers: string[];
    };
};

// See: https://w.amazon.com/bin/view/BRASS/Onboarding/AWSAuth/#HAWSAuthEndpoints
export const stageToBrassConfig: StageToBrassConfig = {
    [StageName.Alpha]: {
        endpoints: [],
        publicCoralQualifiers: [],
        allegianceCoralQualifiers: []
    },
    [StageName.Beta]: {
        endpoints: ["/svc1/BrassService-AWSAuth/gamma/us-east-1"],
        publicCoralQualifiers: ["AWSAuth.Gamma_PUBLIC"],
        allegianceCoralQualifiers: ["AWSAuth.Gamma"]
    },
    [StageName.Gamma]: {
        endpoints: ["/svc1/BrassService-AWSAuth/prod/us-east-1"],
        publicCoralQualifiers: ["AWSAuth.IAD_PUBLIC"],
        allegianceCoralQualifiers: ["AWSAuth.IAD"]
    },
    [StageName.Prod]: {
        endpoints: ["/svc1/BrassService-AWSAuth/prod/us-east-1"],
        publicCoralQualifiers: ["AWSAuth.IAD_PUBLIC"],
        allegianceCoralQualifiers: ["AWSAuth.IAD"]
    },
    [StageName.DNS]: {
        endpoints: [],
        publicCoralQualifiers: [],
        allegianceCoralQualifiers: []
    }
};
