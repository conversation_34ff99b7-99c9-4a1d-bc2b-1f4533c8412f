import {ServiceEnvironmentStage} from "@amzn/superstar-provisioner-cdk";
import {StageName} from "../stage";
import {stageToBrassConfig} from "./brass-stage-mappings";

type StageToSuperStarStageMappings = {
    [stageName in StageName]: {
        superStarStage: ServiceEnvironmentStage;
        superStarEndpoints: string[];
    };
};

export const stageToSuperStarStageMappings: StageToSuperStarStageMappings = {
    [StageName.Alpha]: {
        superStarStage: ServiceEnvironmentStage.ALPHA,
        superStarEndpoints: stageToBrassConfig[StageName.Alpha].endpoints
    },
    [StageName.Beta]: {
        superStarStage: ServiceEnvironmentStage.BETA,
        superStarEndpoints: stageToBrassConfig[StageName.Beta].endpoints
    },
    [StageName.Gamma]: {
        superStarStage: ServiceEnvironmentStage.GAMMA,
        superStarEndpoints: stageToBrassConfig[StageName.Gamma].endpoints
    },
    [StageName.Prod]: {
        superStarStage: ServiceEnvironmentStage.PROD,
        superStarEndpoints: stageToBrassConfig[StageName.Prod].endpoints
    },
    [StageName.DNS]: {
        superStarStage: ServiceEnvironmentStage.PROD,
        superStarEndpoints: []
    }
};
