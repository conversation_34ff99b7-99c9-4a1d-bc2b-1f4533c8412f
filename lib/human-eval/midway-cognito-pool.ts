import {MidwayIdentityProvider} from "@amzn/cdk-bones";
import {CfnIdentityPool, CfnIdentityPoolRoleAttachment} from "aws-cdk-lib/aws-cognito";
import {FederatedPrincipal, PolicyStatement, Role} from "aws-cdk-lib/aws-iam";
import {Construct} from "constructs";

export interface MidwayCognitoPoolProps {
    readonly websiteOriginName: string;
    readonly region: string;
    readonly accountID: string;
    readonly apiGatewayID: string;
    readonly apiGatewayStage: string;
}

export class MidwayCognitoPool extends Construct {
    readonly cognitoAuthenticatedRole: Role;
    readonly cognitoPool: CfnIdentityPool;

    constructor(scope: Construct, id: string, props: MidwayCognitoPoolProps) {
        super(scope, id);

        const midwayIDP = new MidwayIdentityProvider(this, "MidwayIDP", {
            allowedOrigins: ["localhost.a2z.com", props.websiteOriginName]
        });

        this.cognitoPool = new CfnIdentityPool(this, "IdentityPool", {
            allowUnauthenticatedIdentities: false,
            openIdConnectProviderArns: [midwayIDP.identityProviderId.toString()]
        });

        this.cognitoAuthenticatedRole = new Role(this, "AuthedRole", {
            assumedBy: new FederatedPrincipal(
                "cognito-identity.amazonaws.com",
                {
                    StringEquals: {
                        "cognito-identity.amazonaws.com:aud": this.cognitoPool.ref
                    },
                    "ForAnyValue:StringLike": {
                        "cognito-identity.amazonaws.com:amr": "authenticated"
                    }
                },
                "sts:AssumeRoleWithWebIdentity"
            )
        });

        new CfnIdentityPoolRoleAttachment(this, "RoleAttachment", {
            identityPoolId: this.cognitoPool.ref,
            roles: {
                authenticated: this.cognitoAuthenticatedRole.roleArn
            }
        });

        this.cognitoAuthenticatedRole.addToPolicy(
            new PolicyStatement({
                actions: ["execute-api:Invoke"],
                resources: [
                    `arn:aws:execute-api:${props.region}:${props.accountID}:${props.apiGatewayID}/${props.apiGatewayStage}/*`
                ]
            })
        );
    }
}
