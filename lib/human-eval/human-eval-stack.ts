import {StaticWebsite} from "@amzn/cdk-bones";
import {IArtifactCopyConfiguration} from "@amzn/cdk-bones/lib/static_website";
import {CloudFrontSigner} from "@amzn/cloudfront-signer";
import {SecureIVpc} from "@amzn/motecdk/mote-ec2";
import {SecureApplicationListener} from "@amzn/motecdk/mote-elasticloadbalancingv2";
import {BrazilPackage, DeploymentStack, LambdaAsset} from "@amzn/pipelines";
import {Stack} from "aws-cdk-lib";
import {Effect, PolicyStatement} from "aws-cdk-lib/aws-iam";
import {Bucket} from "aws-cdk-lib/aws-s3";
import {Construct} from "constructs";
import {HumanEvalDeploymentStackProps} from "../common/human-eval-deployment-stack-props";
import {DelegatedDomainName, DelegatedHostedZoneProps} from "../dns/delegated-domain-name";
import {CloudAuthPolicy} from "../service-stack/cloud-auth-policy";
import {HumanEvalWebapp} from "./human-eval-webapp";
import {MidwayCognitoPool} from "./midway-cognito-pool";

export interface HumanEvalStackProps extends HumanEvalDeploymentStackProps {
    /**
     * Configuration of DNS, containing the hosted zone where this stack will create its own root hosted zone. The
     * CloudFront distribution with the website will be hosted at the root whilst the API will be on the api subdomain.
     */
    readonly delegatedHostedZoneProps: DelegatedHostedZoneProps;
    /**
     * The VPC where the service cluster runs. Required to create a VPC link between the service and the API Gateway.
     */
    readonly clusterVpc: SecureIVpc;
    /**
     * The Application Listener where the API Gateway will forward requests.
     */
    readonly applicationListener: SecureApplicationListener;
    /**
     * A ManagedPolicy which allows the service to communicate with CloudAuth. It does not imply that the service is
     * protected by CloudAuth.
     */
    readonly cloudAuthPolicy: CloudAuthPolicy;
}

/**
 * This stack is responsible for the Internet-facing parts of Eeyore Human Evaluation, specifically the CloudFront
 * distribution hosting the ReactJS website and the API Gateway for handling backend service requests.
 *
 * It also handles the creation of DNS records in a hosted zone to point to these resources, and certificates required
 * to securely connect to them.
 */
export class HumanEvalStack extends DeploymentStack {
    constructor(scope: Construct, id: string, props: HumanEvalStackProps) {
        super(scope, id, props);

        const delegatedDomainName = new DelegatedDomainName(
            this,
            "DelegatedDomainName",
            props.delegatedHostedZoneProps
        );

        // Create the API Gateway
        const apiGateway = new HumanEvalWebapp(this, "WebApp", {
            stageName: props.serviceStageName,
            hostedZone: delegatedDomainName.hostedZone,
            clusterVpc: props.clusterVpc,
            applicationListener: props.applicationListener
        });
        // Ensure the R53 HZ is created before the API Gateway so delegation works.
        if (delegatedDomainName.crossAccountDelegationRecord) {
            apiGateway.node.addDependency(delegatedDomainName.crossAccountDelegationRecord);
        }

        // Create a Midway Cognito pool, used to ensure that the CloudFront distribution is protected by Midway.
        const midwayCognitoPool = new MidwayCognitoPool(this, "MidwayCognitoPool", {
            accountID: this.account,
            region: this.region,
            websiteOriginName: props.delegatedHostedZoneProps.domainNameToDelegate,
            apiGatewayStage: "$default",
            apiGatewayID: apiGateway.apiGatewayID
        });

        // Create the CloudFront Distribution hosting the assets from the Website package
        this.createCloudFrontDistribution(
            props.delegatedHostedZoneProps.domainNameToDelegate,
            apiGateway.apiEndpoint,
            midwayCognitoPool.cognitoPool.ref
        );
    }

    private createCloudFrontDistribution(domainName: string, apiEndpoint: string, cognitoPoolID: string) {
        new StaticWebsite(this, "StaticWebsite", {
            dns: {
                hostedZoneName: domainName
            },
            website: {
                artifactCopyConfiguration: this.makeArtifactCopyConfiguration(apiEndpoint, cognitoPoolID),
                cloudFrontSigner: new CloudFrontSigner({
                    authorizerBindleId: "amzn1.bindle.resource.enmy5zrbrhvolc2l5waa"
                }),
                invalidationPaths: ["/"]
            }
        });
    }

    private makeArtifactCopyConfiguration(apiEndpoint: string, cognitoPoolID: string): IArtifactCopyConfiguration {
        const websitePackage = BrazilPackage.fromString("EeyoreHumanEvaluationLambdaWebsite-1.1");
        const componentName = "EeyoreHEWebsite";
        const stack = Stack.of(this) as DeploymentStack;
        const assets = LambdaAsset.fromBrazil({
            brazilPackage: websitePackage,
            componentName
        });
        const artifactLocation = assets.bind(stack).s3Location;
        if (!artifactLocation) {
            throw new Error("S3 Location for artifact is undefined.");
        }

        return {
            sourceBucket: Bucket.fromBucketName(this, "ArtifactSourceBucket", artifactLocation.bucketName),
            sourceKey: artifactLocation.objectKey,
            zipSubfolder: "packaging_additional_published_artifacts",
            additionalPolicyStatements: [
                // Overly permissive, but we don't easily have a way to know here what the key arn actually is...
                // This is because while we know the alias arn, you can't grant kms:Decrypt via alias arn, only key arn
                new PolicyStatement({
                    effect: Effect.ALLOW,
                    actions: ["kms:Decrypt"],
                    resources: ["*"]
                })
            ],
            settings: {
                apiEndpoint,
                cognitoPoolId: cognitoPoolID,
                region: this.region
            }
        };
    }
}
