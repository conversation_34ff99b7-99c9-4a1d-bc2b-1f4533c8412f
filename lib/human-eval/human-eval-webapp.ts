import {SecureIVpc} from "@amzn/motecdk/mote-ec2";
import {SecureApplicationListener} from "@amzn/motecdk/mote-elasticloadbalancingv2";
import {SecureManagedPolicy, SecureRole, SecureServicePrincipal} from "@amzn/motecdk/mote-iam";
import {SecureLogGroup} from "@amzn/motecdk/mote-logs";
import {AccessLogFormat, CfnAccount} from "aws-cdk-lib/aws-apigateway";
import {
    ApiMapping,
    CfnStage,
    DomainName,
    EndpointType,
    HttpApi,
    HttpMethod,
    HttpRoute,
    HttpRouteKey,
    MappingValue,
    ParameterMapping,
    VpcLink
} from "aws-cdk-lib/aws-apigatewayv2";
import {HttpIamAuthorizer} from "aws-cdk-lib/aws-apigatewayv2-authorizers";
import {HttpAlbIntegration} from "aws-cdk-lib/aws-apigatewayv2-integrations";
import {Certificate, CertificateValidation} from "aws-cdk-lib/aws-certificatemanager";
import {SecurityGroup} from "aws-cdk-lib/aws-ec2";
import {ApplicationListener} from "aws-cdk-lib/aws-elasticloadbalancingv2";
import {IHostedZone, RecordSet, RecordTarget, RecordType} from "aws-cdk-lib/aws-route53";
import {ApiGatewayv2DomainProperties} from "aws-cdk-lib/aws-route53-targets";
import {Construct} from "constructs";
import {StageName} from "../stage";

export interface HumanEvalWebappProps {
    /**
     * The stage to which the stack is being deployed.
     */
    readonly stageName: StageName;
    /**
     * The hosted zone where the API will be hosted as the api subdomain.
     */
    readonly hostedZone: IHostedZone;
    /**
     * The VPC in which the ECS Cluster runs.
     */
    readonly clusterVpc: SecureIVpc;
    /**
     * The Application Listener where the API Gateway will forward requests.
     */
    readonly applicationListener: SecureApplicationListener;
}

export class HumanEvalWebapp extends Construct {
    readonly apiGatewayID: string;
    readonly apiEndpoint: string;

    constructor(scope: Construct, id: string, props: HumanEvalWebappProps) {
        super(scope, id);

        this.apiEndpoint = `api.${props.hostedZone.zoneName}`;

        const albVpcLinkSecurityGroup = new SecurityGroup(this, "HumanEvalWebAppVpcLinkSecurityGroup", {
            vpc: props.clusterVpc,
            allowAllOutbound: true
        });
        const albVpcLink = new VpcLink(this, "HumanEvalWebAppVpcLink", {
            vpc: props.clusterVpc,
            securityGroups: [albVpcLinkSecurityGroup]
        });

        const apiGateway = this.createAPIGateway(albVpcLink, props.applicationListener);
        this.apiGatewayID = apiGateway.apiId;

        this.createDomainNameForAPIGateway(props.hostedZone, apiGateway);

        this.setupApiLogs(apiGateway);
    }

    private createAPIGateway(vpcLink: VpcLink, applicationListener: ApplicationListener): HttpApi {
        const apiGateway = new HttpApi(this, "APIGateway", {});

        const integration = new HttpAlbIntegration("HTTP-API-Integration", applicationListener, {
            method: HttpMethod.ANY,
            vpcLink,
            parameterMapping: new ParameterMapping()
                .appendHeader(
                    "x-eeyore-cognito-configuration-provider",
                    MappingValue.contextVariable("identity.cognitoAuthenticationProvider")
                )
                .appendHeader("x-eeyore-user-arn", MappingValue.contextVariable("identity.userArn"))
        });

        // For routing, we allow OPTIONS requests without any authorization, because they never return any data and
        // we can't sign them in the front-end. All other supported methods (GET, POST) require the use of an
        // HttpIamAuthorizer.
        new HttpRoute(this, "APIGatewayOptionsProxyRoute", {
            httpApi: apiGateway,
            routeKey: HttpRouteKey.with("/{proxy+}", HttpMethod.OPTIONS),
            integration
        });
        const iamAuthorizer = new HttpIamAuthorizer();
        new HttpRoute(this, "APIGatewayGetProxyRoute", {
            httpApi: apiGateway,
            routeKey: HttpRouteKey.with("/{proxy+}", HttpMethod.GET),
            integration,
            authorizer: iamAuthorizer
        });
        new HttpRoute(this, "APIGatewayPostProxyRoute", {
            httpApi: apiGateway,
            routeKey: HttpRouteKey.with("/{proxy+}", HttpMethod.POST),
            integration,
            authorizer: iamAuthorizer
        });

        return apiGateway;
    }

    // Enables logging on an API Gateway. We create the log group and assign it to the stage, but we also need to
    // enable logging at an account level to allow it to create log streams.
    private setupApiLogs(api: HttpApi) {
        const role = new SecureRole(this, "CloudwatchLoggingRole", {
            assumedBy: new SecureServicePrincipal("apigateway.amazonaws.com"),
            managedPolicies: [
                SecureManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonAPIGatewayPushToCloudWatchLogs")
            ]
        });
        const cfnAccount = new CfnAccount(this, "Account", {
            cloudWatchRoleArn: role.roleArn
        });
        api.node.addDependency(cfnAccount);

        const logGroup = new SecureLogGroup(this, "HumanEvalAPIAccessLog", {});
        const stage = api.defaultStage!.node.defaultChild as CfnStage;
        stage.accessLogSettings = {
            destinationArn: logGroup.logGroupArn,
            format: AccessLogFormat.jsonWithStandardFields({
                caller: true,
                httpMethod: true,
                ip: true,
                protocol: true,
                requestTime: true,
                resourcePath: true,
                responseLength: true,
                status: true,
                user: true
            }).toString()
        };

        logGroup.grantWrite(new SecureServicePrincipal("apigateway.amazonaws.com"));
    }

    private createDomainNameForAPIGateway(hostedZone: IHostedZone, api: HttpApi) {
        const apiCertificate = new Certificate(this, "HumanEvalAPIGCert", {
            domainName: `*.${hostedZone.zoneName}`,
            subjectAlternativeNames: [hostedZone.zoneName, this.apiEndpoint],
            validation: CertificateValidation.fromDns(hostedZone)
        });

        const apigDomain = new DomainName(this, "APIGDNS", {
            domainName: this.apiEndpoint,
            certificate: apiCertificate,
            endpointType: EndpointType.REGIONAL
        });
        new ApiMapping(this, "APIGMapping", {
            domainName: apigDomain,
            stage: api.defaultStage,
            api
        });

        [RecordType.A, RecordType.AAAA].forEach(recordType => {
            new RecordSet(this, `APIGRecord${recordType}`, {
                recordType,
                zone: hostedZone,
                recordName: this.apiEndpoint,
                target: RecordTarget.fromAlias(
                    new ApiGatewayv2DomainProperties(apigDomain.regionalDomainName, apigDomain.regionalHostedZoneId)
                )
            });
        });
    }
}
