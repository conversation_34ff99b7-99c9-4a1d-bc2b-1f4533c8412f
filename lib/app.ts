#!/usr/bin/env node
import {App} from "aws-cdk-lib";
import {BootstrapStack, SoftwareType} from "@amzn/pipelines";
import {EcsClusterStack} from "./ecs-cluster-stack/ecs-cluster-stack";
import {Pipeline} from "./pipeline";
import {HumanEvalStack} from "./human-eval/human-eval-stack";
import {ServiceStack} from "./service-stack/service-stack";
import {StageName} from "./stage";
import {VpcStack} from "./vpc-stack/vpc-stack";

// Set up your CDK App
const app = new App();

// If the dev account ID setup, we simply set up the stack and stop.
const devAccountId = process.env.AWS_PERSONAL_ACCOUNT_ID;
if (devAccountId) {
    const username = process.env.USER!;
    const websiteDomainName = `dev-${username}.he.eeyore.advertising.amazon.dev`;

    const personalBootstrapStack = BootstrapStack.personalBootstrap(app, {
        account: devAccountId,
        region: "us-east-1",
        disambiguator: `Eeyore-HE-CDK-${username}`
    });

    const deploymentProps = {
        env: personalBootstrapStack.deploymentEnvironment,
        softwareType: SoftwareType.INFRASTRUCTURE,
        serviceStageName: StageName.Alpha
    };

    const vpcStack = new VpcStack(app, "HumanEval-VpcStack", {
        ...deploymentProps,
        // We use an ALB so we need at least two AZs
        maxAzs: 2
    });

    const ecsClusterStack = new EcsClusterStack(app, "HumanEval-EcsClusterStack", {
        ...deploymentProps,
        vpc: vpcStack.vpc,
        stageHasOnePod: false
    });

    const serviceStack = new ServiceStack(app, "HumanEval-ServiceStack", {
        ...deploymentProps,
        vpc: vpcStack.vpc,
        ecsCluster: ecsClusterStack.cluster,
        applicationLoadBalancerSecurityGroup: ecsClusterStack.applicationLoadBalancerSecurityGroup,
        loadBalancerTargetGroups: [ecsClusterStack.targetGroupFleet],
        allowedOrigins: ["https://localhost.a2z.com", `https://${websiteDomainName}`],
        cloudAuthPolicy: vpcStack.cloudAuthPolicy
    });

    new HumanEvalStack(app, "HumanEvalStack-Dev", {
        ...deploymentProps,
        delegatedHostedZoneProps: {
            domainNameToDelegate: websiteDomainName
        },
        clusterVpc: vpcStack.vpc,
        applicationListener: ecsClusterStack.applicationListener,
        cloudAuthPolicy: vpcStack.cloudAuthPolicy
    });
}
// When the dev account ID isn't set, build the entire pipeline.
else {
    new Pipeline(app, "EeyoreHECDKPipeline");
}
