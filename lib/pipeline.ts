import {
    BakeTimeCloudWatchApprovalWorkflowStep,
    BrazilPackage,
    CodeReviewVerificationApprovalWorkflowStep,
    DeploymentGroupStage,
    DeploymentPipeline,
    GordianKnotScannerApprovalWorkflowStep,
    Platform,
    ScanProfile,
    SoftwareType,
    TimeWindowBlocker
} from "@amzn/pipelines";
import {App} from "aws-cdk-lib";
import {dnsUpdateRoleName, rootHostedZoneID, rootHostedZoneName} from "./constants";
import {RootHostedZoneStack} from "./dns/root-hosted-zone-stack";
import {EcsClusterStack} from "./ecs-cluster-stack/ecs-cluster-stack";
import {HumanEvalStack} from "./human-eval/human-eval-stack";
import {ServiceStack} from "./service-stack/service-stack";
import {Region, StageConfig, StageName} from "./stage";
import {VpcStack} from "./vpc-stack/vpc-stack";

const applicationAccount = "************";

export class Pipeline extends DeploymentPipeline {
    constructor(app: App, id: string) {
        super(app, id, {
            account: applicationAccount,
            pipelineName: "EeyoreHumanEvaluationCDK",
            versionSet: "EeyoreHumanEvaluation/mainline",
            versionSetPlatform: Platform.AL2_X86_64,
            trackingVersionSet: "live",
            bindleGuid: "amzn1.bindle.resource.hxmoz3j5gpg3xj3wouua",
            description: "A CDK Managed pipeline for Eeyore' Human Evaluation",
            pipelineId: "3515904",
            selfMutate: true
        });

        this.addPackagesToAutobuild();

        // Add the code review workflow to the VersionSet stage - this is required by Dogma for secure applications.
        this.versionSetStage
            .addApprovalWorkflow("Code Verification Workflow")
            .addStep(new CodeReviewVerificationApprovalWorkflowStep())
            .addStep(
                new GordianKnotScannerApprovalWorkflowStep({
                    platform: Platform.AL2_X86_64,
                    scanProfileName: ScanProfile.ASSERT_HIGH
                })
            );

        // DNS Stage
        const dnsStage = this.addStage("DNS", {isProd: true});
        this.createDnsStack(app, dnsStage, Region.US_EAST_1);

        // Dev
        const devStage = this.addStage("dev", {isProd: false});
        this.createHumanEvalStack(app, devStage, StageName.Alpha, Region.US_EAST_1);
        devStage.addApprovalWorkflow("Dev Approval").addStep(
            new BakeTimeCloudWatchApprovalWorkflowStep({
                name: "Fixed Wait Time",
                duration: 15 // Minutes
            })
        );

        // Beta
        const betaStage = this.addStage("beta", {isProd: false});
        this.createHumanEvalStack(app, betaStage, StageName.Beta, Region.US_EAST_1);
        betaStage.addApprovalWorkflow("Beta Approval").addStep(
            new BakeTimeCloudWatchApprovalWorkflowStep({
                name: "Fixed Wait Time",
                duration: 30 // Minutes
            })
        );

        // Gamma
        const gammaStage = this.addStage("gamma", {isProd: false});
        this.createHumanEvalStack(app, gammaStage, StageName.Gamma, Region.US_EAST_1);
        gammaStage.addApprovalWorkflow("Gamma Approval").addStep(
            new BakeTimeCloudWatchApprovalWorkflowStep({
                name: "Fixed Wait Time",
                duration: 30 // Minutes
            })
        );

        // Prod
        const prodStage = this.addStage("prod", {isProd: true});
        prodStage.addInboundTimeWindowBlocker(new TimeWindowBlocker("Tigger - UK Office Hours"));
        prodStage.addInboundTimeWindowBlocker(TimeWindowBlocker.BLOCKED_AND_RESTRICTED_DAYS_GLOBAL);
        this.createHumanEvalStack(app, prodStage, StageName.Prod, Region.US_EAST_1);
    }

    private createHumanEvalStack(
        app: App,
        stage: DeploymentGroupStage,
        stageName: StageName,
        region: Region
    ): HumanEvalStack {
        const deploymentGroup = stage.addDeploymentGroup({name: `HumanEvalStack-${stageName}`});
        const stageConfig = StageConfig.stageFor(stageName, region);
        if (!stageConfig) {
            throw new Error(`Stage ${stageName} - ${region} not found.`);
        }

        const dnsStage = StageConfig.stageFor(StageName.DNS, Region.US_EAST_1)!;
        const domainName = `${stageConfig.name}.he.${rootHostedZoneName}`;
        const deploymentProps = {
            env: this.deploymentEnvironmentFor(stageConfig.accountID, region),
            softwareType: SoftwareType.INFRASTRUCTURE,
            serviceStageName: stageConfig.name
        };

        const vpcStack = new VpcStack(app, `HumanEval-VpcStack-${stageName}-${region}`, deploymentProps);

        const ecsClusterStack = new EcsClusterStack(app, `HumanEval-EcsClusterStuck-${stageName}-${region}`, {
            ...deploymentProps,
            vpc: vpcStack.vpc,
            domainName,
            stageHasOnePod: false
        });

        const serviceStack = new ServiceStack(app, `HumanEval-ServiceStack-${stageName}-${region}`, {
            ...deploymentProps,
            vpc: vpcStack.vpc,
            ecsCluster: ecsClusterStack.cluster,
            applicationLoadBalancerSecurityGroup: ecsClusterStack.applicationLoadBalancerSecurityGroup,
            loadBalancerTargetGroups: [ecsClusterStack.targetGroupFleet],
            allowedOrigins: [`https://${domainName}`, `https://localhost.a2z.com`],
            cloudAuthPolicy: vpcStack.cloudAuthPolicy
        });

        const webStack = new HumanEvalStack(app, `HumanEvalStack-${stageName}`, {
            ...deploymentProps,
            delegatedHostedZoneProps: {
                domainNameToDelegate: domainName,
                rootHostedZoneID: rootHostedZoneID,
                rootHostedZoneIAMRole: `arn:aws:iam::${dnsStage.accountID}:role/${dnsUpdateRoleName}`
            },
            clusterVpc: vpcStack.vpc,
            applicationListener: ecsClusterStack.applicationListener,
            cloudAuthPolicy: vpcStack.cloudAuthPolicy
        });
        deploymentGroup.addStacks(vpcStack, ecsClusterStack, serviceStack, webStack);
        return webStack;
    }

    private createDnsStack(app: App, stage: DeploymentGroupStage, region: Region): RootHostedZoneStack {
        const deploymentGroup = stage.addDeploymentGroup({name: "DNSStack"});

        const stageConfig = StageConfig.stageFor(StageName.DNS, region);
        if (!stageConfig) {
            throw new Error(`Stage ${StageName.DNS} - ${region} not found.`);
        }

        const deploymentProps = {
            env: this.deploymentEnvironmentFor(stageConfig.accountID, region),
            softwareType: SoftwareType.INFRASTRUCTURE,
            rootHostedZoneName: rootHostedZoneName,
            dnsUpdateRoleName: dnsUpdateRoleName,
            // TODO: It would be better if we did not allow all accounts to update the DNS.
            accountsAllowedToUpdate: [
                StageConfig.stageFor(StageName.Alpha, Region.US_EAST_1)!,
                StageConfig.stageFor(StageName.Beta, Region.US_EAST_1)!,
                StageConfig.stageFor(StageName.Gamma, Region.US_EAST_1)!,
                StageConfig.stageFor(StageName.Prod, Region.US_EAST_1)!
            ].map(it => it.accountID)
        };

        const infraStack = new RootHostedZoneStack(app, "DNSStack", deploymentProps);
        deploymentGroup.addStacks(infraStack);
        return infraStack;
    }

    private addPackagesToAutobuild() {
        [
            "AdRiskDetectorAPI-1.0",
            "EeyoreHumanEvaluation-1.0",
            "EeyoreHumanEvaluationImageBuild-1.0",
            "EeyoreHumanEvaluationLambdaJavaClient-1.0",
            "EeyoreHumanEvaluationLambdaModel-1.0",
            "EeyoreHumanEvaluationLambdaWebsite-1.1",
            "EeyoreHumanEvaluationLogImageBuild-1.0",
            "WinnieJavaClient-1.0"
        ].map(pkg => this.addPackageToAutobuild(BrazilPackage.fromString(pkg)));

        // BrazilPackage.fromString doesn't work with this package because its MV does not have a minor version due to
        // the way that Coral2TS works.
        this.addPackageToAutobuild(
            BrazilPackage.fromProps({
                name: "EeyoreHumanEvaluationJavascriptClient",
                branch: "mainline",
                majorVersion: "1"
            })
        );

        ["EeyoreHumanEvaluationLambdaJavaClient-1.0", "EeyoreHumanEvaluationLambdaModel-1.0"].map(pkg =>
            this.addPackageToAutopublish(BrazilPackage.fromString(pkg), "live")
        );
    }
}
