import {personalAccountID, retainIfNotPersonalAccount} from "@amzn/adrisk-common-cdk";
import {AssertCritical, Exempt} from "@amzn/motecdk/mote-core";
import {SecureIPublicSecurityGroup, SecureIVpc, SecurePublicSecurityGroup} from "@amzn/motecdk/mote-ec2";
import {BrassOnboarding, BrassOnboardingTargets} from "@amzn/brass-onboarding-cdk-constructs";
import {stageToBrassConfig} from "../config/brass-stage-mappings";
import {ServiceEnvironment} from "@amzn/superstar-provisioner-cdk";
import {stageToSuperStarStageMappings} from "../config/superstar-stage-mappings";
import {SecureCluster} from "@amzn/motecdk/mote-ecs";
import {
    SecureApplicationListener,
    SecureApplicationLoadBalancer,
    SecureApplicationTargetGroup,
    SecureListenerAction,
    SecureWeightedTargetGroup
} from "@amzn/motecdk/mote-elasticloadbalancingv2";
import {SecureBlockPublicAccess, SecureBucket, SecureBucketProps, SecureIBucket} from "@amzn/motecdk/mote-s3";
import {DeploymentStack} from "@amzn/pipelines";
import {App, Duration} from "aws-cdk-lib";
import {ApplicationProtocol, DesyncMitigationMode, Protocol, TargetType} from "aws-cdk-lib/aws-elasticloadbalancingv2";
import {BucketEncryption} from "aws-cdk-lib/aws-s3";
import {HumanEvalDeploymentStackProps} from "../common/human-eval-deployment-stack-props";
import {HumanEvalLogGroup} from "../common/human-eval-log-group";
import {StageName} from "../stage";
import {IVpc} from "aws-cdk-lib/aws-ec2";

export interface EcsClusterStackProps extends HumanEvalDeploymentStackProps {
    /**
     * The VPC where the cluster is deployed.
     */
    readonly vpc: SecureIVpc;
    /**
     * Domain name under which the service will be hosted. If not set, then it will default to the value below.
     * Generally this should only be set for personal AWS accounts.
     *
     * ${stageName}.${region}.eriskay.advertising.amazon.dev
     */
    readonly domainName?: string;
    /**
     * True if this stage is split into OnePod/Fleet, false otherwise.
     */
    readonly stageHasOnePod: boolean;
}

export const ALB_LISTENER_PORT = 80;
export const SERVICE_PORT = 8443;

export class EcsClusterStack extends DeploymentStack {
    readonly cluster: SecureCluster;

    readonly applicationListener: SecureApplicationListener;
    readonly applicationLoadBalancer: SecureApplicationLoadBalancer;
    readonly applicationLoadBalancerSecurityGroup: SecureIPublicSecurityGroup;
    readonly targetGroupFleet: SecureApplicationTargetGroup;
    readonly targetGroupOnePod?: SecureApplicationTargetGroup;

    constructor(scope: App, id: string, props: EcsClusterStackProps) {
        super(scope, id, props);

        this.cluster = new SecureCluster(this, "Cluster", {
            vpc: props.vpc,
            containerInsights: true
        });
        AssertCritical(this.cluster);

        this.applicationLoadBalancerSecurityGroup = this.createApplicationLoadBalancerSecurityGroup(props.vpc);

        const accessLogsBucket = this.createAccessLogsBuckets();

        this.applicationLoadBalancer = this.createApplicationLoadBalancer(
            props,
            accessLogsBucket,
            this.applicationLoadBalancerSecurityGroup
        );

        this.targetGroupFleet = this.createApplicationTargetGroup(props.vpc);
        let albActions: SecureWeightedTargetGroup[];
        if (props.stageHasOnePod) {
            this.targetGroupOnePod = this.createApplicationTargetGroup(props.vpc, "OnePod");
            albActions = [
                {targetGroup: this.targetGroupFleet, weight: 99},
                {targetGroup: this.targetGroupOnePod, weight: 1}
            ];
        } else {
            albActions = [{targetGroup: this.targetGroupFleet, weight: 100}];
        }

        this.applicationListener = this.applicationLoadBalancer.addListener("ApplicationListener", {
            defaultAction: SecureListenerAction.weightedForward(albActions),
            protocol: Exempt(ApplicationProtocol.HTTP),
            port: Exempt(ALB_LISTENER_PORT)
        });

        // The names of the log groups created here must match the log_group_name configuration
        // parameter in the LogImageBuild package. Be sure to update those parameters when making
        // changes to the names in this class.
        [
            {name: "AppContainer-STDOUT", alarm: false, dataOnlyWhenRequests: true},
            {name: "ApplicationLogs", alarm: true, dataOnlyWhenRequests: true, isApplicationLogGroup: true},
            {name: "RequestLogs", alarm: true, dataOnlyWhenRequests: true},
            {name: "ServiceMetrics", alarm: true, dataOnlyWhenRequests: true}
        ].forEach(logGroup => {
            const onePodLogGroup = new HumanEvalLogGroup(this, `OnePod-${logGroup.name}`, {
                isOnePod: true,
                name: logGroup.name,
                serviceStageName: props.serviceStageName
            });

            const fleetLogGroup = new HumanEvalLogGroup(this, logGroup.name, {
                isOnePod: false,
                name: logGroup.name,
                serviceStageName: props.serviceStageName
            });

            if (!logGroup.alarm) {
                return;
            }
        });

        // Note that we don't create SuperStar in Personal deployments, because it involves onboarding and we won't ever
        // be connecting to SuperStar-enabled services.
        if (!personalAccountID()) {
            this.createSuperStarEnvironment(props.vpc, props.serviceStageName);
        }

        if (props.serviceStageName != StageName.Alpha) {
            // This construct is marked as BETA.
            // https://w.amazon.com/bin/view/BRASS/Onboarding/GuideForSelfServiceOnboarding/#HUsingCDK5BBETA5D
            new BrassOnboarding(this, "BrassOnboarding", {
                coralQualifiers: stageToBrassConfig[props.serviceStageName].publicCoralQualifiers,
                onboardingTargets: [BrassOnboardingTargets.BINDLE_LOCK],
                resourceSuffix: ""
            });
        }
    }

    private createApplicationLoadBalancerSecurityGroup(vpc: SecureIVpc): SecurePublicSecurityGroup {
        return new SecurePublicSecurityGroup(this, "ApplicationLoadBalancerSecurityGroup", {
            vpc,
            description: "Security Group to Allow Ingress to the ALB"
        });
    }

    createApplicationLoadBalancer(
        props: EcsClusterStackProps,
        accessLogsBucket: SecureIBucket,
        securityGroup: SecureIPublicSecurityGroup
    ): SecureApplicationLoadBalancer {
        const applicationLoadBalancer = new SecureApplicationLoadBalancer(this, "ApplicationLoadBalancer", {
            vpc: props.vpc,
            internetFacing: false,
            deletionProtection: !personalAccountID(),
            dropInvalidHeaderFields: true,
            desyncMitigationMode: DesyncMitigationMode.DEFENSIVE,
            securityGroup
        });
        applicationLoadBalancer.logAccessLogs(accessLogsBucket);

        return applicationLoadBalancer;
    }

    createAccessLogsBuckets(): SecureIBucket {
        const bucketProps = {
            blockPublicAccess: SecureBlockPublicAccess.BLOCK_ALL,
            encryption: Exempt(BucketEncryption.S3_MANAGED),
            enforceSSL: true,
            lifecycleRules: [
                {
                    enabled: true,
                    expiration: Duration.days(3653),
                    id: "ExpireAfterTenYears"
                }
            ],
            removalPolicy: retainIfNotPersonalAccount(),
            versioned: true
        };

        const serverAccessLogsBucket = new SecureBucket(this, "ServerAccessLogsBucket", {
            ...bucketProps,
            // Access logs don't need to be themselves logged.
            serverAccessLogsBucket: Exempt(undefined)
        } as SecureBucketProps);

        return new SecureBucket(this, "LoadBalancerAccessLogsBucket", {
            ...bucketProps,
            serverAccessLogsBucket: serverAccessLogsBucket
        } as SecureBucketProps);
    }

    createApplicationTargetGroup(vpc: SecureIVpc, prefix = ""): SecureApplicationTargetGroup {
        return new SecureApplicationTargetGroup(this, `${prefix}TargetGroup`, {
            deregistrationDelay: Duration.seconds(300),
            healthCheck: {
                path: "/sping",
                port: "traffic-port",
                protocol: Protocol.HTTPS,
                healthyThresholdCount: 3,
                unhealthyThresholdCount: 3,
                interval: Duration.minutes(2)
            },
            port: Exempt(SERVICE_PORT),
            protocol: ApplicationProtocol.HTTPS,
            targetType: TargetType.IP,
            vpc
        });
    }

    private createSuperStarEnvironment(vpc: IVpc, serviceStageName: StageName) {
        const serviceEnvironment = new ServiceEnvironment(this, "SuperStarServiceEnvironment", {
            bindleGUID: "amzn1.bindle.resource.enmy5zrbrhvolc2l5waa", // Eeyore bindle ID
            meta: {
                wiki: "https://w.amazon.com/bin/view/AdRisk/Projects/Eeyore/",
                description: "Human Evaluation Service"
            },
            name: "EeyoreHumanEvaluation",
            stage: stageToSuperStarStageMappings[serviceStageName].superStarStage
        });
        serviceEnvironment.within(vpc);
        stageToSuperStarStageMappings[serviceStageName].superStarEndpoints.forEach(endpoint =>
            serviceEnvironment.dependsOnEnvironment(endpoint)
        );
    }
}
