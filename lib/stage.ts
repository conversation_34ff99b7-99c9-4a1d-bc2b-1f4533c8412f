export enum StageName {
    Alpha = "alpha",
    Beta = "beta",
    Gamma = "gamma",
    Prod = "prod",
    DNS = "dns"
}

export enum Region {
    US_EAST_1 = "us-east-1"
}

class StageKey {
    readonly name: StageName;
    readonly region: Region;

    constructor(name: StageName, region: Region) {
        this.name = name;
        this.region = region;
    }

    public toKeyString(): string {
        return `${this.name}_${this.region}`;
    }
}

export class StageConfig {
    readonly name: StageName;
    readonly accountID: string;
    readonly region: Region;
    readonly prod?: boolean;

    private static readonly stages: Map<string, StageConfig> = new Map([
        [
            new StageKey(StageName.Alpha, Region.US_EAST_1).toKeyString(),
            new StageConfig(StageName.Alpha, "************", Region.US_EAST_1, false)
        ],
        [
            new StageKey(StageName.Beta, Region.US_EAST_1).toKeyString(),
            new StageConfig(StageName.Beta, "************", Region.US_EAST_1, false)
        ],
        [
            new StageKey(StageName.Gamma, Region.US_EAST_1).toKeyString(),
            new StageConfig(StageName.Gamma, "************", Region.US_EAST_1, false)
        ],
        [
            new StageKey(StageName.Prod, Region.US_EAST_1).toKeyString(),
            new StageConfig(StageName.Prod, "************", Region.US_EAST_1, true)
        ],
        [
            new StageKey(StageName.DNS, Region.US_EAST_1).toKeyString(),
            new StageConfig(StageName.DNS, "************", Region.US_EAST_1, true)
        ]
    ]);

    static stageFor(name: StageName, region: Region): StageConfig | undefined {
        const stageKeyString = new StageKey(name, region).toKeyString();
        return this.stages.get(stageKeyString);
    }

    private constructor(name: StageName, accountID: string, region: Region, prod?: boolean) {
        this.name = name;
        this.accountID = accountID;
        this.region = region;
        this.prod = prod;
    }
}
