import {personalAccountID} from "@amzn/adrisk-common-cdk";
import {Exempt} from "@amzn/motecdk/mote-core";
import {SecureIPublicSecurityGroup, SecureIVpc, SecurePeer, SecurePublicSecurityGroup} from "@amzn/motecdk/mote-ec2";
import {stageToBrassConfig} from "../config/brass-stage-mappings";
import {
    SecureContainerDefinition,
    SecureFargateService,
    SecureFargateTaskDefinition,
    SecureICluster
} from "@amzn/motecdk/mote-ecs";
import {SecureApplicationTargetGroup} from "@amzn/motecdk/mote-elasticloadbalancingv2";
import {
    SecureIPrincipal,
    SecureIRole,
    SecureManagedPolicy,
    SecurePolicyStatement,
    SecureRole,
    SecureServicePrincipal
} from "@amzn/motecdk/mote-iam";
import {BrazilContainerImage, BrazilPackage, DeploymentStack} from "@amzn/pipelines";
import {IHydraEnvironment} from "@amzn/pipelines/dist/lib/builder-tools-scaffolding/scaffolding/hydra";
import {Arn, ArnFormat, Aws, Duration, RemovalPolicy} from "aws-cdk-lib";
import {Port} from "aws-cdk-lib/aws-ec2";
import {
    AwsLogDriverMode,
    FargatePlatformVersion,
    FirelensConfigFileType,
    FirelensLogRouterType,
    LogDriver,
    LogDrivers
} from "aws-cdk-lib/aws-ecs";
import {Effect} from "aws-cdk-lib/aws-iam";
import {Fact, RegionInfo} from "aws-cdk-lib/region-info";
import {Construct} from "constructs";
import {HumanEvalDeploymentStackProps} from "../common/human-eval-deployment-stack-props";
import {HumanEvalLogGroup} from "../common/human-eval-log-group";
import {SERVICE_PORT} from "../ecs-cluster-stack/ecs-cluster-stack";
import {CloudAuthPolicy} from "./cloud-auth-policy";

export interface ServiceStackProps extends HumanEvalDeploymentStackProps {
    /**
     * The VPC where the cluster is deployed.
     */
    readonly vpc: SecureIVpc;
    /**
     * The ECS/Fargate Cluster to deploy the application tasks to.
     */
    readonly ecsCluster: SecureICluster;
    /**
     * A Security Group the load balancer is a member of. It's modified by this class to allow it to communicate with
     * the tasks themselves.
     */
    readonly applicationLoadBalancerSecurityGroup: SecureIPublicSecurityGroup;
    /**
     * Target Groups where the application should be registered to.
     */
    readonly loadBalancerTargetGroups: SecureApplicationTargetGroup[];
    /**
     * Domains which are allowed to send requests to this service.
     */
    readonly allowedOrigins: string[];
    /**
     * A ManagedPolicy which allows the service to communicate with CloudAuth. It does not imply that the service is
     * protected by CloudAuth.
     */
    readonly cloudAuthPolicy: CloudAuthPolicy;
}

const TASK_CPU = 2048;
const TASK_MEMORY = 4096;
const LOG_CONTAINER_CPU = 256;
// Important! When changing the FluentBit memory limit, *also* update the configuration to adjust each INPUT section's
// Mem_Buf_Limit, or the total memory usage will not change.
const LOG_CONTAINER_MEMORY = 512;
const APPLICATION_CONTAINER_CPU = TASK_CPU - LOG_CONTAINER_CPU;
const APPLICATION_CONTAINER_MEMORY = TASK_MEMORY - LOG_CONTAINER_MEMORY;

export class ServiceStack extends DeploymentStack {
    readonly service: SecureFargateService;
    readonly taskDefinition: SecureFargateTaskDefinition;
    readonly container: SecureContainerDefinition;
    readonly serviceInstanceRole: SecureIRole;

    private readonly hydraEnvironment: IHydraEnvironment;

    constructor(scope: Construct, id: string, props: ServiceStackProps) {
        super(scope, id, props);

        this.serviceInstanceRole = this.taskInstanceRole(this.region, props.cloudAuthPolicy);
        this.taskDefinition = new SecureFargateTaskDefinition(this, "TaskDefinition", {
            cpu: TASK_CPU,
            memoryLimitMiB: TASK_MEMORY,
            taskRole: this.serviceInstanceRole
        });

        const environment = {
            STAGE: props.serviceStageName,
            SERVICE_STAGE_NAME: props.serviceStageName,
            DEPLOYMENT_GROUP_NAME: props.serviceStageName,
            APPLICATION_NAME: "EeyoreHumanEvaluation",
            AWS_ACCOUNT_ID: Aws.ACCOUNT_ID,
            IS_ONEPOD: "FALSE",
            ALLOWED_ORIGINS: JSON.stringify(props.allowedOrigins)
        };

        this.container = this.taskDefinition.addContainer("Container", {
            image: BrazilContainerImage.fromBrazil({
                brazilPackage: BrazilPackage.fromString("EeyoreHumanEvaluation"),
                transformPackage: BrazilPackage.fromString("EeyoreHumanEvaluationImageBuild"),
                componentName: "service"
            }),
            stopTimeout: Duration.seconds(120),
            cpu: APPLICATION_CONTAINER_CPU,
            memoryLimitMiB: APPLICATION_CONTAINER_MEMORY,
            containerName: "Application",
            // The application crashes with a readonly filesystem
            readonlyRootFilesystem: Exempt(false),
            // TODO we should be running as "ecs-user", but if we set it here, then the container crashes
            user: undefined as unknown as string,
            removeSetUidBinaries: true,
            logging: LogDrivers.firelens({}), //defined below
            healthCheck: {
                command: [
                    "CMD-SHELL",
                    "curl -f http://127.0.0.1:8080/ping && curl -f -k https://127.0.0.1:8443/sping || exit 1"
                ],
                startPeriod: Duration.minutes(5)
            },
            environment
        });
        // We access BRASS over Private Link (SuperStar) in the service
        const brassCoralQualifier = stageToBrassConfig[props.serviceStageName].allegianceCoralQualifiers[0];
        if (brassCoralQualifier) {
            this.container.addEnvironment("BRASS_CORAL_QUALIFIER", brassCoralQualifier);
        }

        this.container.addPortMappings({
            containerPort: SERVICE_PORT
        });

        const serviceSecurityGroup = new SecurePublicSecurityGroup(this, "ServiceSecurityGroup", {
            vpc: props.vpc,
            allowAllOutbound: true
        });
        serviceSecurityGroup.addIngressRule(
            SecurePeer.ipv4(props.vpc.vpcCidrBlock),
            Exempt(Port.tcp(SERVICE_PORT)),
            "Allow access to the service port."
        );
        serviceSecurityGroup.addIngressRule(
            props.applicationLoadBalancerSecurityGroup,
            Exempt(Port.tcp(SERVICE_PORT)),
            "Allow the Load Balancer to connect to ECS Tasks."
        );

        this.service = new SecureFargateService(this, "Service", {
            platformVersion: FargatePlatformVersion.VERSION1_4,
            cluster: props.ecsCluster,
            taskDefinition: this.taskDefinition,
            securityGroups: [serviceSecurityGroup],
            enableExecuteCommand: !!personalAccountID(),
            circuitBreaker: {
                rollback: true
            }
        });

        props.loadBalancerTargetGroups.forEach(tg => tg.addTarget(this.service));

        const firelensLogGroup = new HumanEvalLogGroup(this, "FirelensLogGroup", {
            isOnePod: false,
            name: "FirelensLogGroup",
            serviceStageName: props.serviceStageName
        });
        this.addFireLensSidecar(props, firelensLogGroup);

        this.hydraEnvironment = props.env.hydraEnvironment;
    }

    private addFireLensSidecar(props: ServiceStackProps, firelensLogGroup: HumanEvalLogGroup) {
        // Add AWS for FluentBit sidecar container
        const fireLens = this.taskDefinition.addFirelensLogRouter("FireLensContainer", {
            image: BrazilContainerImage.fromBrazil({
                brazilPackage: BrazilPackage.fromString("EeyoreHumanEvaluationLogImageBuild"),
                componentName: "logging_container"
            }),
            cpu: LOG_CONTAINER_CPU,
            memoryLimitMiB: LOG_CONTAINER_MEMORY,
            // FluentBit crashes with a readonly root filesystem
            readonlyRootFilesystem: Exempt(false),
            removeSetUidBinaries: true,
            environment: {
                CLOUDWATCH_ENDPOINT: Fact.find(props.env.region, "cloudwatchlogs.endpoint") ?? "",
                LOG_REGION: props.env.region,
                STAGE: props.serviceStageName
            },
            firelensConfig: {
                type: FirelensLogRouterType.FLUENTBIT,
                options: {
                    enableECSLogMetadata: false,
                    configFileType: FirelensConfigFileType.FILE,
                    configFileValue: "/config/fluent-bit.conf"
                }
            },
            healthCheck: {
                command: ["CMD-SHELL", "curl -f http://127.0.0.1:2020/api/v1/uptime || exit 1"]
            },
            logging: LogDriver.awsLogs({
                logGroup: firelensLogGroup.logGroup,
                streamPrefix: "FireLens-",
                mode: AwsLogDriverMode.NON_BLOCKING
            })
        });

        const apolloMount = {
            sourceVolume: "apollo_dir",
            containerPath: "/apollo",
            readOnly: false
        };
        this.taskDefinition.addVolume({name: apolloMount.sourceVolume});

        // attach bind mount to app container to share logs
        this.container.addMountPoints(apolloMount);

        // add the same mount points to firelens, but as read only
        fireLens.addMountPoints({
            ...apolloMount,
            readOnly: true
        });
    }

    private taskInstanceRole(region: string, cloudAuthPolicy: CloudAuthPolicy): SecureIRole {
        const tasksPrincipal = RegionInfo.get(region).servicePrincipal("ecs-tasks") || "ecs-tasks.amazonaws.com";

        const taskInstanceManagedPolicy = new SecureManagedPolicy(this, "EcsTaskInstanceRoleManagedPolicy");

        const logging = new SecurePolicyStatement({
            actions: ["logs:CreateLogStream", "logs:Describe*", "logs:Get*", "logs:Filter*", "logs:List*", "logs:Put*"],
            resources: [
                Arn.format(
                    {
                        arnFormat: ArnFormat.COLON_RESOURCE_NAME,
                        service: "logs",
                        resource: "log-group",
                        resourceName: `${HumanEvalLogGroup.LOG_GROUP_NAME_PREFIX}*`
                    },
                    this
                )
            ]
        });
        const metrics = new SecurePolicyStatement({
            actions: ["cloudwatch:PutMetricData"],
            resources: ["*"]
        });

        taskInstanceManagedPolicy.addStatements(logging, metrics);
        taskInstanceManagedPolicy.applyRemovalPolicy(RemovalPolicy.DESTROY);

        const taskRole = new SecureRole(this, "EcsTaskInstanceRole", {
            assumedBy: new SecureServicePrincipal(tasksPrincipal) as SecureIPrincipal,
            managedPolicies: [taskInstanceManagedPolicy, cloudAuthPolicy.policy]
        });

        const brassAccessPolicy = new SecurePolicyStatement({
            actions: ["BrassService:IsAuthorized"],
            resources: ["*"]
        });

        taskRole.addToPolicy(brassAccessPolicy);

        taskRole.addToPolicy(
            new SecurePolicyStatement({
                effect: Effect.ALLOW,
                actions: [
                    "cloudwatch:PutMetricData",
                    "logs:FilterLogEvents",
                    "sts:AssumeRole",
                    "lambda:InvokeFunction",
                    "ssm:GetParameter*",
                    "athena:*",
                    "glue:*"
                ],
                resources: ["*"]
            })
        );

        return taskRole;
    }
}
