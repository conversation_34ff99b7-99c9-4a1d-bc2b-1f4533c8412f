import {Effect} from "aws-cdk-lib/aws-iam";
import {Construct} from "constructs";
import {SecureIManagedPolicy, SecureManagedPolicy, SecurePolicyStatement} from "@amzn/motecdk/mote-iam";

// The name prefix here is only required for personal stacks, to avoid conflicts with the policy in other stacks.
export const CLOUD_AUTH_MANAGED_POLICY_NAME = "EeyoreHumanEvaluation-CloudAuthAPIGatewayInvokeFullAccess";

/**
 * A managed policy for invoking the CloudAuth API Gateway. Required to authenticate clients using a CloudAuth token,
 * thus it needs to be applied to the instance role of services protected by CloudAuth.
 */
export class CloudAuthPolicy extends Construct {
    readonly policy: SecureIManagedPolicy;

    constructor(parent: Construct, name: string) {
        super(parent, name);
        this.policy = new SecureManagedPolicy(this, "Resource", {
            managedPolicyName: CLOUD_AUTH_MANAGED_POLICY_NAME,
            description: "Enables communication with CloudAuth Servers - http://w/?CloudAuth",
            statements: [
                new SecurePolicyStatement({
                    sid: "Global",
                    effect: Effect.ALLOW,
                    actions: ["execute-api:Invoke"],
                    resources: [
                        "arn:aws:execute-api:*:568383657092:*",
                        "arn:aws:execute-api:*:174627225380:*",
                        "arn:aws:execute-api:*:024024572172:*",
                        "arn:aws:execute-api:*:972766437379:*",
                        "arn:aws:execute-api:*:866227326436:*"
                    ]
                })
            ]
        });
    }
}
