import {retainIfNotPersonalAccount} from "@amzn/adrisk-common-cdk";
import {AssertConfidential} from "@amzn/motecdk/mote-core";
import {SecureILogGroup, SecureLogGroup} from "@amzn/motecdk/mote-logs";
import {RemovalPolicy} from "aws-cdk-lib";
import {RetentionDays} from "aws-cdk-lib/aws-logs";
import {Construct} from "constructs";
import {StageName} from "../stage";

export interface HumanEvalLogGroupProps {
    /**
     * Whether the log group is for the OnePod service
     * If true, adds the ONEPOD_LOG_GROUP_PREFIX to the name of the log group
     *
     * @default false
     */
    readonly isOnePod?: boolean;

    /**
     * Name of the log group
     */
    readonly name: string;

    /**
     * The service stage
     */
    readonly serviceStageName: StageName;

    /**
     * Removal policy
     *
     * @default RemovalPolicy.RETAIN
     */
    readonly removalPolicy?: RemovalPolicy;

    /**
     * How long log contents will be retained
     *
     * @default RetentionDays.TEN_YEARS
     */
    readonly retentionDays?: RetentionDays;
}

export class HumanEvalLogGroup extends Construct {
    /**
     * The log_group_name configuration parameter in the LogImageBuild package must match the
     * name of the log group created through this construct. Be sure to update those parameters
     * when making changes to the constants or the createLogGroupName method in this class.
     */
    static readonly LOG_GROUP_NAME_PREFIX = "EeyoreHumanEvaluation";
    static readonly ONEPOD_LOG_GROUP_PREFIX = "OnePod";
    readonly logGroup: SecureILogGroup;

    constructor(parent: Construct, name: string, props: HumanEvalLogGroupProps) {
        super(parent, name);

        const logGroupName = this.createLogGroupName(props);

        this.logGroup = new SecureLogGroup(this, logGroupName, {
            logGroupName: logGroupName,
            removalPolicy: props.removalPolicy ?? retainIfNotPersonalAccount(),
            retention: props.retentionDays ?? RetentionDays.EIGHTEEN_MONTHS
        });
        AssertConfidential(this.logGroup);
    }

    private createLogGroupName(props: HumanEvalLogGroupProps) {
        const segments = [HumanEvalLogGroup.LOG_GROUP_NAME_PREFIX];
        if (props.isOnePod) {
            segments.push(HumanEvalLogGroup.ONEPOD_LOG_GROUP_PREFIX);
        }
        segments.push(props.serviceStageName, props.name);
        return segments.join("-");
    }
}
