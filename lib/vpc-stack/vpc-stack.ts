import {Exempt} from "@amzn/motecdk/mote-core";
import {
    SecureFlowLog,
    SecureFlowLogDestination,
    SecureIVpc,
    SecurePeer,
    SecurePort,
    SecurePublicSecurityGroup,
    SecureVpc
} from "@amzn/motecdk/mote-ec2";
import {DeploymentStack} from "@amzn/pipelines";
import {Duration} from "aws-cdk-lib";
import {FlowLogResourceType, InterfaceVpcEndpointAwsService, IpAddresses, IVpc} from "aws-cdk-lib/aws-ec2";
import {ARecord, PrivateHostedZone, RecordTarget} from "aws-cdk-lib/aws-route53";
import {InterfaceVpcEndpointTarget} from "aws-cdk-lib/aws-route53-targets";
import {Construct} from "constructs";
import {HumanEvalDeploymentStackProps} from "../common/human-eval-deployment-stack-props";
import {HumanEvalLogGroup} from "../common/human-eval-log-group";
import {CloudAuthPolicy} from "../service-stack/cloud-auth-policy";
import {StageName} from "../stage";

interface VpcStackProps extends HumanEvalDeploymentStackProps {
    /**
     * The maximum number of AZs this VPC will have. One is suggested for personal stacks, three is for production and
     * is the default.
     */
    maxAzs?: number;
}

const CLOUD_AUTH_ENDPOINTS_BY_REGION: {[region: string]: string} = {
    ["eu-west-1"]: "com.amazonaws.vpce.eu-west-1.vpce-svc-0d77de0805c1bdea4",
    ["us-east-1"]: "com.amazonaws.vpce.us-east-1.vpce-svc-0a0fad57588b3cbed",
    ["us-west-2"]: "com.amazonaws.vpce.us-west-2.vpce-svc-09cf4701618242ad8",
    ["ap-northeast-1"]: "com.amazonaws.vpce.ap-northeast-1.vpce-svc-04b83fc8ae12fc531",
    ["eu-central-1"]: "com.amazonaws.vpce.eu-central-1.vpce-svc-058b269a390ffd362",
    ["ap-south-2"]: "com.amazonaws.vpce.ap-south-2.vpce-svc-0745a1cb103cf8e0b"
};
const OAUTH_CLOUD_AUTH_DOMAIN = "oauth.cloudauth.a2z.com";

export class VpcStack extends DeploymentStack {
    readonly vpc: SecureIVpc;
    readonly cloudAuthPolicy: CloudAuthPolicy;

    constructor(scope: Construct, id: string, props: VpcStackProps) {
        super(scope, id, props);

        this.vpc = new SecureVpc(this, "Vpc", {
            ipAddresses: IpAddresses.cidr("********/16"),
            requireS3GatewayEndpoint: true,
            requireDynamoDBGatewayEndpoint: true,
            maxAzs: props.maxAzs ?? 3,
            restrictDefaultSecurityGroup: true
        }) as SecureIVpc;

        this.addEndpoints(this.vpc);
        this.enableFlowLogs(this.vpc, props.serviceStageName);
        this.addCloudAuthPrivateLink(props);
        this.cloudAuthPolicy = new CloudAuthPolicy(this, "CloudAuthPolicy");
    }

    private addEndpoints(vpc: IVpc) {
        const endpoints: [string, InterfaceVpcEndpointAwsService][] = [
            ["CloudWatchLogs", InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS],
            ["CloudWatchMonitoring", InterfaceVpcEndpointAwsService.CLOUDWATCH_MONITORING],
            ["Ecr", InterfaceVpcEndpointAwsService.ECR],
            ["EcrDocker", InterfaceVpcEndpointAwsService.ECR_DOCKER]
        ];
        for (const [name, service] of endpoints) {
            vpc.addInterfaceEndpoint(`VpcEndpoint${name}`, {
                service,
                privateDnsEnabled: true,
                lookupSupportedAzs: !this.useIntrinsicAZs
            });
        }
    }

    private enableFlowLogs(vpc: IVpc, serviceStageName: StageName) {
        const vpcFlowLogsLogGroup = new HumanEvalLogGroup(this, "VpcFlowLogsLogGroup", {
            name: "VpcFlowLogs",
            serviceStageName
        });

        new SecureFlowLog(this, "VpcFlowLogs", {
            resourceType: FlowLogResourceType.fromVpc(vpc),
            destination: SecureFlowLogDestination.toCloudWatchLogs(vpcFlowLogsLogGroup.logGroup)
        });
    }

    private addCloudAuthPrivateLink(props: VpcStackProps) {
        // Security Group for CloudAuth Private Link
        const endpointSecurityGroup = new SecurePublicSecurityGroup(this, "CloudAuthPrivateLinkSecurityGroup", {
            vpc: this.vpc,
            description: "Security group for private link interface endpoint"
        });
        endpointSecurityGroup.addIngressRule(Exempt(SecurePeer.anyIpv4()), SecurePort.tcp(443));

        // Create Endpoint connection to CA server
        const cloudAuthEndpoint = CLOUD_AUTH_ENDPOINTS_BY_REGION[this.region];
        if (!cloudAuthEndpoint) {
            throw Error(`Region '${this.region}' is not supported by CloudAuth.`);
        }
        const vpcEndpoint = this.vpc.addInterfaceEndpoint("CloudAuthPrivateLinkVpcEndpoint", {
            service: {
                name: cloudAuthEndpoint,
                port: 443
            },
            privateDnsEnabled: false,
            securityGroups: [endpointSecurityGroup]
        });

        // Hosted zone to resolve DNS for cloudauth domain
        const cloudAuthHostedZone = new PrivateHostedZone(this, "CloudAuthHostedZone", {
            vpc: this.vpc,
            zoneName: OAUTH_CLOUD_AUTH_DOMAIN
        });

        // Create Record inside hosted zone
        new ARecord(this, "CloudAuthServerDns", {
            zone: cloudAuthHostedZone,
            target: RecordTarget.fromAlias(new InterfaceVpcEndpointTarget(vpcEndpoint)),
            recordName: OAUTH_CLOUD_AUTH_DOMAIN,
            ttl: Duration.seconds(60)
        });
    }
}
