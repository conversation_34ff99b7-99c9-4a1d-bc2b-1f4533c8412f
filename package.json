{"name": "@amzn/eeyore_human_evaluation_cdk", "version": "1.0.0", "license": "UNLICENSED", "main": "dist/lib/app.js", "types": "dist/types/app.d.ts", "scripts": {"clean": "rm -rf dist && rm -rf cdk.out && rm -rf node_modules", "build": "tsc", "watch": "tsc -w", "prepare": "npm run-script build", "test": "echo OK", "lint": "eslint '**/*.ts'", "lint:fix": "eslint --fix '**/*.ts'", "prettier": "prettier --check '**/*.ts'", "prettier:fix": "prettier --write '**/*.ts'", "prepublishOnly": "npm run lint && npm run prettier && npm run build", "reformat": "npm run lint:fix && npm run prettier:fix"}, "dependencies": {}, "devDependencies": {"@amzn/brass-onboarding-cdk-constructs": "1.0.0", "@amzn/superstar-provisioner-cdk": "^4.0.7", "@amzn/adrisk-common-cdk": "*", "@amzn/sim-ticket-cdk-constructs": "^3.0.0", "@amzn/cloudfront-signer": "5.0.0", "@amzn/pipelines": "^4.0.50", "@amzn/cdk-bones": "^4.0.53", "@amzn/hydra": "^4.0.7", "@amzn/motecdk": "^27.0.0", "@types/node": "*", "aws-cdk-lib": "2.160.0", "constructs": "10.2.65", "typescript": "~4.8.4", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "eslint": "^8.25.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.7.1"}}