{"name": "@amzn/eeyore-human-evaluation-website", "version": "1.0.0", "scripts": {"start": "sudo socat tcp-listen:443,reuseaddr,fork tcp:localhost:3443 > /dev/null 2>&1& react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "test:ci": "CI=true npm run test -- --ci --no-cache --reporters=default --reporters=jest-junit --coverage --coverageDirectory=./build/coverage", "release": "npx prettier --check src/ && npm run build && npm run test:ci && npm run copy-artifacts && npm run copy-coverage", "copy-artifacts": "node scripts/copy-artifacts.js", "copy-coverage": "node scripts/copy-coverage.js", "lint": "eslint --fix './src/**/*.{tsx,ts}'", "format": "npx prettier --write src/", "clean": "rm -fr node_modules/"}, "homepage": "https://localhost.a2z.com/", "babel": {"presets": ["react-app"]}, "jest": {"coverageReporters": ["cobertura", "lcov", "json-summary"]}, "dependencies": {"@amzn/coral_com-amazon-eeyore-humanevaluation-model": "*", "@amzn/midway-identity-credential-provider": "*", "@cloudscape-design/collection-hooks": "^1.0.0", "@cloudscape-design/components": "^3.0.945", "@cloudscape-design/global-styles": "^1.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/plotly.js": "^2.35.5", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.1", "@types/react-plotly.js": "^2.6.3", "@types/react-router-dom": "^5.3.3", "@zip.js/zip.js": "~2.7.60", "assert": "^2.1.0", "axios": "^1.8.4", "bootstrap": "^5.3.5", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "csv-parse": "^5.6.0", "font-awesome": "^4.7.0", "https-browserify": "^1.0.0", "node-html-parser": "^7.0.1", "null-loader": "^4.0.1", "os-browserify": "^0.3.0", "pako": "^2.1.0", "path-browserify": "^1.0.1", "plotly.js": "~3.0.1", "process": "^0.11.10", "query-string": "^9.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-grid-gallery": "^1.0.1", "react-icons": "^5.5.0", "react-minimal-pie-chart": "^9.1.0", "react-player": "^2.16.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.5.0", "react-scripts": "^5.0.1", "reactstrap": "^9.2.3", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "typescript": "^5.8.3", "url": "^0.11.4", "util": "^0.12.5", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "eslint": "^8.38.0", "eslint-config-react-app": "^7.0.1", "jest-junit": "^13.0.0", "prettier": "^2.8.7"}, "npm-pretty-much": {"runTest": "never", "runRelease": "always"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest-junit": {"outputDirectory": "./build/brazil-unit-tests/", "outputName": "TESTS-TestSuites.xml"}}