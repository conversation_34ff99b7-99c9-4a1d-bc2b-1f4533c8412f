<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="DEBUG">
    <Properties>
        <Property name="log-path">${sys:root}/var/output/logs</Property>
    </Properties>
    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT">
            <PatternLayout pattern="%enc{%m}{JSON}%n"/>
        </Console>
        <AmazonRollingRandomAccessFile name="APPLICATION" filePattern="${log-path}/application.log.%d{yyyy-MM-dd-HH}" >
            <PatternLayout>
                <pattern>%d{ISO8601} %highlight{[%p]} %X{RequestId} (%t) %c: %m%n</pattern>
            </PatternLayout>
        </AmazonRollingRandomAccessFile>
        <AmazonRollingRandomAccessFile name="REQUEST"
                                       filePattern="${log-path}/requests.log.%d{yyyy-MM-dd-HH}" >
        </AmazonRollingRandomAccessFile>
        <AmazonRollingRandomAccessFile name="WIRE"
                                       filePattern="${log-path}/wire.log.%d{yyyy-MM-dd-HH}" >
        </AmazonRollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <Root level="DEBUG">
            <AppenderRef ref="APPLICATION"/>
            <AppenderRef ref="STDOUT"/>
        </Root>

        <Logger name="com.amazon.coral.service.RequestLoggingInterceptor" level="TRACE" additivity="false">
            <AppenderRef ref="REQUEST" level="TRACE"/>
        </Logger>

        <!-- Since additivity is false, wire logging will not go to the root logger. This prevents
             wire logging from being interspersed with application logging.
         -->
        <Logger name="WIRE" level="OFF"/>
        <Logger name="com.amazon.coral" level="WARN"/>
        <Logger name="com.amazon.coral.reflect" level="ERROR"/>
        <Logger name="org.apache" level="WARN"/>
        <Logger name="com.amazon.aws" level="WARN"/>
        <Logger name="com.amazon.cloudauth" level="INFO"/>
        <Logger name="com.amazonaws" level="WARN"/>
        <Logger name="software.amazon.awssdk" level="WARN"/>
    </Loggers>
</Configuration>
