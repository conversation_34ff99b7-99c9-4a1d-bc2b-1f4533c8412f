#!/usr/bin/env sh

# Copy from bones_run_apollo_shim.sh and run the preserve-env-vars.sh before activate env.
# This file is meant for containers, not normal ec2 services
# It's job is to run the standard ApolloShim scripts, and then launch the app.

SERVICE_PID=0

post_execution_handler() {
    echo "Running apollo deactivation"
    /opt/amazon/bin/env-deactivate || {
        echo "Failed to deactivate apollo environment. continuing to exit anyway..."
    }

    echo "Delaying for a minute to give logs and metrics a chance to flush"
    sleep 1m

    echo "Finished clean-up workflow, now exiting the container"
}

signal_handler() {
    echo "Trapped signal from container host, now starting clean-up workflow"
    # Only run the post execution handler if the service started up to begin with
    if [ ${SERVICE_PID} -ne 0 ]; then
        # the above if statement is important because it ensures
        # that the application has already started. without it you
        # could attempt cleanup steps if the application failed to
        # start, causing errors.
        echo "Waiting for process to shutdown gracefully"
        post_execution_handler
        kill -15 "${SERVICE_PID}"
        wait "${SERVICE_PID}"
        echo "Successfully terminated process!"
        exit 0
    fi
    exit 143; # 128 + 15 -- SIGTERM see https://tldp.org/LDP/abs/html/exitcodes.html
}

set -uex
set -o pipefail


/opt/amazon/bin/env-cleanup && \
/opt/amazon/bin/rewrite_apollo_shim.sh && \
/opt/amazon/bin/env-setup && \
/apollo/bin/env -e EeyoreHumanEvaluation /opt/amazon/bin/preserve-env-vars.sh

## Setup signal trap
# on callback execute the specified handler
trap 'signal_handler' TERM INT HUP QUIT

set +e

## Application starts here
/opt/amazon/bin/env-activate && \
/opt/amazon/bin/bones_run.rb "$@" &

SERVICE_PID="${!}"
echo "PID=${SERVICE_PID}"

## Wait forever until app dies
wait "${SERVICE_PID}"
EXIT_CODE="${?}"

## Cleanup
# SIGTERM triggers a graceful shutdown which emits exit code 0
if [ ${EXIT_CODE} -eq 0 ]
then
    echo "Process exits normally"
else
    echo "Process crashed with exit code ${EXIT_CODE}."
    post_execution_handler
fi

# echo the return code of the application
exit $EXIT_CODE
