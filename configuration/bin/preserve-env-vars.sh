#!/usr/bin/env bash

# /apollo/bin/runCommand hides the host's environment variables from the
# ApolloCmd scripts (https://issues.amazon.com/issues/ApolloCS-12372)
# This script writes a selection of the host's environment variables to
# ${APOLLO_STARTUP_ENVIRONMENT_ROOT}/etc/env.d/
# If a script is then executed with /apollo/bin/env, the variables will be re-applied
# This script itself needs to be executed in an /apollo/bin/env context

ENV_D=${APOLLO_STARTUP_ENVIRONMENT_ROOT}/etc/env.d
mkdir -p ${ENV_D}
echo "export AWS_CONTAINER_CREDENTIALS_RELATIVE_URI=${AWS_CONTAINER_CREDENTIALS_RELATIVE_URI}
export AWS_REGION=${AWS_REGION}" > "${ENV_D}/ecs.sh"
