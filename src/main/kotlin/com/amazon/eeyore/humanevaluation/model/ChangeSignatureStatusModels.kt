package com.amazon.eeyore.humanevaluation.model

/**
 * Valid signature status values.
 * Restricted to SHADOW and LIVE as per the Woozle ThreatStore API.
 */
enum class SignatureStatus {
    SHADOW,
    LIVE;
    
    override fun toString(): String {
        return name
    }
}

/**
 * Request to change the status of a signature.
 * 
 * @property signatureId The ID of the signature whose status needs to be changed
 * @property newStatus The new status to set for the signature (limited to SHADOW or LIVE)
 * @property requester The entity that requested the status change
 * @property approver The user who approved the status change
 */
data class ChangeSignatureStatusRequest(
    val signatureId: String,
    val newStatus: SignatureStatus,
    val requester: String,
    val approver: String
)

/**
 * Response from a change signature status request.
 * 
 * @property success Whether the status change was successful
 */
data class ChangeSignatureStatusResponse(
    val success: Boolean = true
)
