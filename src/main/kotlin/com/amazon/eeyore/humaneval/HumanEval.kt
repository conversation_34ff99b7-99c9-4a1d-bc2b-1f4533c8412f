package com.amazon.eeyore.humaneval

import amazon.platform.config.AppConfig
import amazon.platform.config.AppConfigTree
import com.amazon.adrisk.util.ServiceMain.ServiceArguments
import com.amazon.adrisk.util.ServiceMain.servicemain
import com.amazon.eeyore.humaneval.config.HumanEvalEnvironmentVariableServiceConfiguration
import com.amazon.eeyore.humaneval.guice.HumanEvalServerModule
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlin.system.exitProcess

private val logger = KotlinLogging.logger {}

const val APP_NAME = "EeyoreHumanEvaluation"

fun main(args: Array<String>) {
    logger.info { "Starting with args [${args.joinToString(", ")}]." }
    val appConfigTree = initAppConfig(args)
    val serviceConfiguration = HumanEvalEnvironmentVariableServiceConfiguration()

    servicemain(
        ServiceArguments.builder()
            .appName(APP_NAME)
            .appConfigTree(appConfigTree)
            .modules(listOf(HumanEvalServerModule(serviceConfiguration)))
            // We don't have CodeGuru setup
            .percentageOfFleetProfiled(0.0)
            .build(),
    )
}

private fun initAppConfig(args: Array<String>): AppConfigTree {
    verifyArguments(args)
    AppConfig.initialize(APP_NAME, null, args)
    return AppConfig.instance()
}

private fun verifyArguments(args: Array<String>) {
    var hasRealm = false
    var hasDomain = false
    var hasRoot = false

    for (arg: String in args) {
        if (arg.startsWith("--realm=")) {
            hasRealm = true
        } else if (arg.startsWith("--domain=")) {
            hasDomain = true
        } else if (arg.startsWith("--root=")) {
            hasRoot = true
        }
    }

    if (!(hasRealm && hasDomain && hasRoot)) {
        println(
            """
               The service cannot determine what environment it is running in and will shut down.
               If you are trying to run from a local workspace, add the following to your launch configuration
               
                   --domain=test --realm=us-west-2 --root=build/private
               
               If you're trying seeing this on a deployed host, the initiation script has not passed the appropriate
               command line parameters to the Java program.
            """.trimIndent(),
        )
        exitProcess(2)
    }
}
