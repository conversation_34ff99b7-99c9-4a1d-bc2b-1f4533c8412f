package com.amazon.eeyore.humaneval.util

import com.amazon.coral.service.http.HttpHeaders

fun extractHeader(httpHeaders: HttpHeaders, headerName: String): String? {
    val providedHeaders = httpHeaders.headerNames.toSet()
    if (!providedHeaders.contains(headerName)) {
        return null
    }
    val headerValue = httpHeaders.getValue(headerName)
    if (headerValue.isBlank()) {
        return null
    }
    return headerValue.toString()
}
