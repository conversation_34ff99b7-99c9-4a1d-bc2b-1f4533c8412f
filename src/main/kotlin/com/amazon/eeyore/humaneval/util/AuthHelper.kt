package com.amazon.eeyore.humaneval.util

import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humanevaluation.model.ForbiddenException
import com.amazon.eeyore.humanevaluation.model.UnauthorizedException

/**
 * Helper utility for authorization checks.
 *import com.amazon.eeyore.humanevaluation.model.UnauthorizedException
 *
 * This class provides methods to check if the current user has access to specific resources,
 * with appropriate exception handling and logging.
 */
object AuthHelper {
    /**
     * Asserts that the current user has access to a specific resource type.
     *
     * @param authService The auth service to use for checking permissions.
     * @param resourceType The type of resource being accessed.
     * @throws UnauthorizedException with status code 401 if no user is authenticated.
     * @throws ForbiddenException with status code 403 if the user doesn't have access to the resource.
     */
    fun assertCanAccess(authService: AuthService, resourceType: ResourceType) {
        val currentUser = APIRequestContext.currentUser()
        if (currentUser == null) {
            throw UnauthorizedException("Client is unauthorized")
        }

        val canAccess = authService.isAuthorized(currentUser, resourceType)

        if (!canAccess) {
            throw ForbiddenException("Access denied to resource: $resourceType")
        }
    }

    /**
     * Convenience method to assert that the current user can read signatures.
     *
     * @param authService The auth service to use for checking permissions.
     * @throws UnauthorizedException with status code 401 if no user is authenticated.
     * @throws ForbiddenException with status code 403 if the user doesn't have read access to signatures.
     */
    fun assertCanReadSignature(authService: AuthService) {
        assertCanAccess(authService, ResourceType.SIGNATURE_READ)
    }

    /**
     * Convenience method to assert that the current user can update signatures.
     *
     * @param authService The auth service to use for checking permissions.
     * @throws UnauthorizedException with status code 401 if no user is authenticated.
     * @throws ForbiddenException with status code 403 if the user doesn't have update access to signatures.
     */
    fun assertCanUpdateSignature(authService: AuthService) {
        assertCanAccess(authService, ResourceType.SIGNATURE_UPDATE)
    }

    /**
     * Convenience method to assert that the current user can override signatures.
     *
     * @param authService The auth service to use for checking permissions.
     * @throws UnauthorizedException with status code 401 if no user is authenticated.
     * @throws ForbiddenException with status code 403 if the user doesn't have override access to signatures.
     */
    fun assertCanOverrideSignature(authService: AuthService) {
        assertCanAccess(authService, ResourceType.SIGNATURE_OVERRIDE)
    }
}
