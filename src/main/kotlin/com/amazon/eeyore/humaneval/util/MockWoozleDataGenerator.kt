package com.amazon.eeyore.humaneval.util

import com.amazon.eeyore.humanevaluation.model.DetectionStatus
import com.amazon.eeyore.humanevaluation.model.SignatureDecision
import com.amazon.eeyore.humanevaluation.model.SignatureStatus
import com.amazonaws.services.woozlethreatstore.model.AuthenticatedEntity
import com.amazonaws.services.woozlethreatstore.model.DetectionDetails
import com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent
import com.amazonaws.services.woozlethreatstore.model.SignatureContent
import com.amazonaws.services.woozlethreatstore.model.SignatureListItem
import java.util.Date
import java.util.UUID

data class SignatureParams(
    val domain: String,
    val status: String,
    val decisionType: String,
    val creator: String,
    val auditor: String,
    val source: String,
    val description: String,
)
object MockWoozleDataGenerator {

    fun generateMockSignatures(
        status: String?,
        maxResults: Int,
        nextToken: String?,
    ): List<SignatureListItem> {
        val baseSignatures = createBaseSignatures()
        val filtered = filterSignaturesByStatus(baseSignatures, status)
        return applyPagination(filtered, maxResults, nextToken)
    }

    private fun createBaseSignatures(): List<SignatureListItem> {
        return listOf(
            createMockSignature(
                SignatureParams(
                    domain = "example.com",
                    status = SignatureStatus.LIVE,
                    decisionType = SignatureDecision.THREAT,
                    creator = "test-user",
                    auditor = "auditor-1",
                    source = "HUMAN",
                    description = "Malicious domain",
                ),
            ),
            createMockSignature(
                SignatureParams(
                    domain = "malicious-site.net",
                    status = SignatureStatus.SHADOW,
                    decisionType = SignatureDecision.THREAT,
                    creator = "test-user",
                    auditor = "auditor-2",
                    source = "ADRISK_WEB_THREAT_HUNTING",
                    description = "Suspicious activity detected",
                ),
            ),
            createMockSignature(
                SignatureParams(
                    domain = "safe-domain.org",
                    status = SignatureStatus.LIVE_REFERRAL,
                    decisionType = SignatureDecision.SAFETY,
                    creator = "test-user",
                    auditor = "auditor-1",
                    source = "GOOGLE",
                    description = "Safe domain for referral",
                ),
            ),
            createMockSignature(
                SignatureParams(
                    domain = "test-domain.com",
                    status = SignatureStatus.PENDING_MANUAL_REVIEW,
                    decisionType = SignatureDecision.THREAT,
                    creator = "test-user",
                    auditor = "auditor-3",
                    source = "CONFIANT",
                    description = "Pending review",
                ),
            ),
            createMockSignature(
                SignatureParams(
                    domain = "blocked-site.evil",
                    status = SignatureStatus.REJECTED,
                    decisionType = SignatureDecision.THREAT,
                    creator = "test-user",
                    auditor = "auditor-2",
                    source = "TMT",
                    description = "Rejected due to false positive",
                ),
            ),
        )
    }

    private fun filterSignaturesByStatus(
        signatures: List<SignatureListItem>,
        status: String?,
    ): List<SignatureListItem> {
        return if (status != null) {
            signatures.filter { it.status.equals(status, ignoreCase = true) }
        } else {
            signatures
        }
    }

    fun generateMockDetections(
        signatureId: String,
        detectionType: String,
        maxResults: Int,
        nextToken: String?,
    ): List<DetectionDetails> {
        val baseDetections = listOf(
            createMockDetection(
                "mock-detection-1",
                detectionType,
                signatureId,
                DetectionStatus.PENDING_VERIFICATION,
                "https://example.com/landing",
            ),
            createMockDetection(
                "mock-detection-2",
                detectionType,
                signatureId,
                DetectionStatus.UNSAFE,
                "https://malicious.com/page",
            ),
            createMockDetection(
                "mock-detection-3",
                detectionType,
                signatureId,
                DetectionStatus.FALSE_POSITIVE,
                "https://safe-site.com/page",
            ),
            createMockDetection(
                "mock-detection-4",
                detectionType,
                signatureId,
                DetectionStatus.PENDING_VERIFICATION,
                "https://test.com/page",
            ),
            createMockDetection(
                "mock-detection-5",
                detectionType,
                signatureId,
                DetectionStatus.UNSAFE,
                "https://threat.com/page",
            ),
        )

        return applyPagination(baseDetections, maxResults, nextToken)
    }

    fun createMockDetection(
        detectionId: String,
        detectionType: String,
        signatureId: String,
        status: String,
        landingPage: String,
    ): DetectionDetails {
        return DetectionDetails()
            .withDetectionId("$detectionId-${UUID.randomUUID()}")
            .withDetectionType(detectionType)
            .withSignatureId(signatureId)
            .withStatus(status)
            .withCreationTime(Date())
            .withLandingPage(landingPage)
            .withLastUpdatedTime(Date())
    }

    fun createMockSignature(params: SignatureParams): SignatureListItem {
        return SignatureListItem()
            .withSignatureId("mock-sig-${UUID.randomUUID()}")
            .withSignatureDecisionType(params.decisionType)
            .withStatus(params.status)
            .withCreator(AuthenticatedEntity().withAuthenticatedUser(params.creator))
            .withAuditor(params.auditor)
            .withSource(params.source)
            .withDescription(params.description)
            .withCreationTime(Date())
            .withContent(
                SignatureContent().withDomainSignatureContent(
                    DomainSignatureContent().withDomain(params.domain),
                ),
            )
    }

    private fun <T> applyPagination(items: List<T>, maxResults: Int, nextToken: String?): List<T> {
        val effectiveMaxResults = if (maxResults <= 0) 50 else maxResults
        val startIndex = if (nextToken != null) 2 else 0
        val endIndex = minOf(startIndex + effectiveMaxResults, items.size)
        val result = if (startIndex >= items.size) emptyList() else items.subList(startIndex, endIndex)

        return when {
            nextToken != null && effectiveMaxResults == 2 -> result.take(2)
            nextToken == null -> result.take(3)
            else -> result
        }
    }
}
