package com.amazon.eeyore.humaneval.health

import com.amazon.coral.service.HealthCheckStrategy

class DeepHealthCheck(private val delegate: HealthCheckStrategy) : HealthCheckStrategy {
    /**
     * Performs a deep health check on your service.
     *
     * You should improve this method with some simple but meaningful health check. This will be invoked at startup
     * during SanityTest to make sure you have got everything configured properly before adding this host to the VIP.
     *
     * The default delegate validates that activities can be instantiated.
     *
     * @return true if the service is healthy
     */
    override fun isHealthy(): <PERSON><PERSON><PERSON> = delegate.isHealthy
}
