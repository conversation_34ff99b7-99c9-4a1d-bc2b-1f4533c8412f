package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService

/**
 * Hybrid auth service that delegates to either the real Brass service or a mock service based on user type.
 *
 * This service is used in the Beta environment to allow:
 * - Real users to authenticate through Brass
 * - Test users to bypass Brass authentication
 */
class SystemsIntegrationTestAuthService(
    private val userAuthService: AuthService,
    private val testsAuthService: AuthService,
) : AuthService {
    override fun isAuthorized(user: User, resourceType: ResourceType): Boolean = when (user.type) {
        UserType.Cognito -> userAuthService.isAuthorized(user, resourceType)
        UserType.Test -> testsAuthService.isAuthorized(user, resourceType)
        else -> false
    }
}
