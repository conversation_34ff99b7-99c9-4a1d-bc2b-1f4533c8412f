package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService

/**
 * Mock implementation of AuthService for integration tests.
 * Always returns true for authorization checks.
 */
class IntegrationTestsAuthService : AuthService {
    override fun isAuthorized(user: User, resourceType: ResourceType): <PERSON><PERSON><PERSON> {
        return true
    }
}
