package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.util.concurrent.UncheckedExecutionException
import io.github.oshai.kotlinlogging.KotlinLogging
import java.time.Duration

/**
 * A caching decorator for AuthService that caches authorization results.
 *
 * This service wraps another AuthService and caches the results of authorization checks
 * to reduce the number of calls to the underlying service.
 *
 * @property delegateAuthService The AuthService to delegate to for uncached results.
 */
class CachingAuthService(private val delegateAuthService: AuthService) : AuthService {
    private val logger = KotlinLogging.logger {}

    // Cache configuration:
    // - maximumSize: 10,000 entries is a reasonable upper limit that balances memory usage with cache effectiveness
    // - expireAfterWrite: 5 minutes is a reasonable time to cache authorization results before refreshing
    private val cache = CacheBuilder.newBuilder()
        .maximumSize(10_000)
        .expireAfterWrite(Duration.ofMinutes(5))
        .build(object : CacheLoader<CacheKey, Boolean>() {
            override fun load(key: CacheKey) =
                delegateAuthService.isAuthorized(key.user, key.resourceType)
        })

    /**
     * Checks if a user is authorized to access a specific resource type, using the cache if available.
     *
     * @param user The user to check authorization for.
     * @param resourceType The type of resource being accessed.
     * @return true if the user is authorized, false otherwise.
     */
    override fun isAuthorized(user: User, resourceType: ResourceType): Boolean {
        try {
            val cacheKey = CacheKey(user, resourceType)
            val result = cache.get(cacheKey)
            logger.debug { "Authorization check for user '${user.name}' and resource '$resourceType': $result" }
            return result
        } catch (ex: UncheckedExecutionException) {
            logger.error(ex.cause ?: ex) { "Error checking authorization" }
            throw ex.cause ?: ex
        }
    }

    private data class CacheKey(
        val user: User,
        val resourceType: ResourceType,
    )
}
