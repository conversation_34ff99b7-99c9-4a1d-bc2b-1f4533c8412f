package com.amazon.eeyore.humaneval.service

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.model.ResourceType

/**
 * Service interface for authorization checks.
 *
 * This interface follows <PERSON>'s approach with a single method for resource-specific authorization.
 */
interface AuthService {
    /**
     * Checks if a user is authorized to access a specific resource type.
     *
     * @param user The user to check authorization for.
     * @param resourceType The type of resource being accessed.
     * @return true if the user is authorized, false otherwise.
     */
    fun isAuthorized(user: User, resourceType: ResourceType): <PERSON><PERSON><PERSON>
}
