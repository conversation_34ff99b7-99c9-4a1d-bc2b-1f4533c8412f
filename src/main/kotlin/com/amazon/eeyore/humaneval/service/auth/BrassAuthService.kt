package com.amazon.eeyore.humaneval.service.auth

import com.amazon.brass.coral.calls.BrassServiceClient
import com.amazon.brass.coral.calls.IsAuthorizedRequest
import com.amazon.brass.coral.types.ActorReference
import com.amazon.brass.coral.types.ResourceReference
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import io.github.oshai.kotlinlogging.KotlinLogging

private const val BINDLE_NAME_SPACE = "Bindle"
private const val LOCK_RESOURCE_TYPE = "Lock"
private const val UNLOCK_OPERATION = "Unlock"
private const val PRINCIPAL_ACTOR_TYPE = "principal"

private val logger = KotlinLogging.logger { }

/**
 * Implementation of AuthService that uses BRASS for authorization checks.
 *
 * This service maps resource types to bindle locks and calls BRASS to check if a user has permission
 * to "unlock" the corresponding bindle lock.
 *
 * @property brassServiceClient The BRASS service client to use for authorization checks.
 * @property bindleLockMapping A mapping from resource types to bindle lock names.
 */
class BrassAuthService(
    private val brassServiceClient: BrassServiceClient,
    private val bindleLockMapping: Map<ResourceType, String>,
) : AuthService {
    /**
     * Checks if a user is authorized to access a specific resource type.
     *
     * @param user The user to check authorization for.
     * @param resourceType The type of resource being accessed.
     * @return true if the user is authorized, false otherwise.
     */
    override fun isAuthorized(user: User, resourceType: ResourceType): Boolean {
        val bindleLock = resourceToBindleLockName(resourceType) ?: return false

        if (user.type != UserType.Cognito) {
            logger.warn { "Tried to access BRASS using a non-human user, this will be rejected." }
            return false
        }

        val response = brassServiceClient.callIsAuthorized(
            IsAuthorizedRequest.builder()
                .withActor(
                    ActorReference.builder()
                        .withActorId(user.name)
                        .withActorType(PRINCIPAL_ACTOR_TYPE)
                        .build(),
                )
                .withResource(
                    ResourceReference.builder()
                        .withNamespace(BINDLE_NAME_SPACE)
                        .withResourceType(LOCK_RESOURCE_TYPE)
                        .withResourceName(bindleLock)
                        .build(),
                )
                .withOperation(UNLOCK_OPERATION)
                .build(),
        )
        return response.isAuthorized
    }

    private fun resourceToBindleLockName(resourceType: ResourceType): String? {
        val bindleLock = bindleLockMapping[resourceType]
        if (bindleLock == null) {
            logger.error { "There is no bindle lock mapping for ResourceType '$resourceType'." }
        }
        return bindleLock
    }
}
