package com.amazon.eeyore.humaneval.service

import com.amazonaws.services.woozlethreatstore.WoozleThreatStore
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsRequest
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsResult
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesResult
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult

class WoozleThreatStoreService(
    private val threatStoreClient: WoozleThreatStore,
) : ThreatStoreService {
    override fun createSignatures(createRequests: List<CreateSignatureRequest>): List<CreateSignatureResult> =
        createRequests.map { cr -> threatStoreClient.createSignature(cr) }

    override fun createSignature(createRequest: CreateSignatureRequest): CreateSignatureResult =
        threatStoreClient.createSignature(createRequest)

    override fun validateSignatures(validateRequests: List<ValidateSignatureRequest>): List<ValidateSignatureResult> =
        validateRequests.map { vr -> threatStoreClient.validateSignature(vr) }

    override fun validateSignature(validateRequest: ValidateSignatureRequest): ValidateSignatureResult =
        threatStoreClient.validateSignature(validateRequest)

    override fun searchSignatures(searchRequest: SearchSignaturesRequest): SearchSignaturesResult =
        threatStoreClient.searchSignatures(searchRequest)

    override fun searchDetections(searchRequest: SearchDetectionsRequest): SearchDetectionsResult =
        threatStoreClient.searchDetections(searchRequest)
}
