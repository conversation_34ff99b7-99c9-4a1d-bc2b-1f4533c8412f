package com.amazon.eeyore.humaneval.service

import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesResult
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult

/**
 * Interface for threat store operations.
 * Provides methods for managing signatures in the threat store.
 */
interface ThreatStoreService {
    /**
     * Creates multiple signatures.
     * @param createRequests List of requests to create new signatures
     * @return List of results containing either the created signature data or an error
     */
    fun createSignatures(createRequests: List<CreateSignatureRequest>): List<CreateSignatureResult>

    /**
     * Creates a single signature.
     * @param createRequest Request to create a new signature
     * @return Result containing the created signature data
     */
    fun createSignature(createRequest: CreateSignatureRequest): CreateSignatureResult

    /**
     * Validates multiple signatures.
     * @param validateRequests List of requests to validate signatures
     * @return List of results containing either the validation data or an error
     */
    fun validateSignatures(validateRequests: List<ValidateSignatureRequest>): List<ValidateSignatureResult>

    /**
     * Validates a single signature.
     * @param validateRequest Request to validate a signature
     * @return Result containing the validation data
     */
    fun validateSignature(validateRequest: ValidateSignatureRequest): ValidateSignatureResult

    /**
     * Searches for signatures based on status and other criteria.
     * @param searchRequest Request containing search criteria including status, pagination parameters
     * @return Result containing list of matching signatures and pagination information
     */
    fun searchSignatures(
        searchRequest: SearchSignaturesRequest,
    ): SearchSignaturesResult
}
