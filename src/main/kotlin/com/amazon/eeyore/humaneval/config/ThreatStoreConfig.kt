package com.amazon.eeyore.humaneval.config

data class ThreatStoreConfig(
    val accountId: String,
    val stageName: String,
)

private val THREAT_STORE_STAGE_MAPPING = mapOf(
    "alpha" to ThreatStoreConfig("************", "dev"),
    "beta" to ThreatStoreConfig("************", "beta"),
    "gamma" to ThreatStoreConfig("************", "gamma"),
    "prod" to ThreatStoreConfig("************", "prod"),
)

fun getThreatStoreConfig(eeyoreStage: String): ThreatStoreConfig {
    return THREAT_STORE_STAGE_MAPPING[eeyoreStage]
        ?: throw IllegalArgumentException(
            "Unsupported Eeyore stage: $eeyoreStage. " +
                "Supported stages: ${THREAT_STORE_STAGE_MAPPING.keys}",
        )
}

fun getThreatStoreClientRoleArn(eeyoreStage: String): String {
    val config = getThreatStoreConfig(eeyoreStage)
    return "arn:aws:iam::${config.accountId}:role/ThreatStoreClientRole"
}
