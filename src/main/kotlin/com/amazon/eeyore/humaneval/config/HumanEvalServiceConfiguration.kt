package com.amazon.eeyore.humaneval.config

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.guice.AwsModule
import com.amazon.eeyore.humaneval.guice.EriskayClientModule
import com.amazon.eeyore.humaneval.guice.WoozleClientModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.amazon.adrisk.util.JsonUtils.MAPPER as JSON_MAPPER

enum class ServiceStageName(val configurationName: String) {
    ALPHA("alpha"),
    BETA("beta"),
    GAMMA("gamma"),
    PROD("prod"),
    ;

    companion object {
        private val byConfigurationName = ServiceStageName.entries.associateBy(ServiceStageName::configurationName)

        fun fromConfigurationName(configurationName: String) =
            byConfigurationName[configurationName]
                ?: throw IllegalArgumentException("$configurationName is not a valid Service Stage Name")
    }
}

interface HumanEvalServiceConfiguration {
    val serviceStageName: ServiceStageName
    val region: String
    val awsAccountID: String
    val isOnePod: Boolean
    val applicationName: String
    val allowedOrigins: Set<String>

    fun awsModule(): SingletonModule
    fun eriskayClientModule(): SingletonModule
    fun woozleClientModule(): SingletonModule
    fun isRunningInDeveloperPersonalAccount(): Boolean
}

class HumanEvalEnvironmentVariableServiceConfiguration :
    HumanEvalServiceConfiguration {
    override val serviceStageName = ServiceStageName.fromConfigurationName(environmentVariable("SERVICE_STAGE_NAME"))
    override val region = environmentVariable("AWS_REGION")
    override val awsAccountID = environmentVariable("AWS_ACCOUNT_ID")
    override val isOnePod = environmentVariable("IS_ONEPOD").toBoolean()
    override val applicationName = environmentVariable("APPLICATION_NAME")
    override val allowedOrigins = JSON_MAPPER.readValue<Set<String>>(environmentVariable("ALLOWED_ORIGINS"))

    override fun eriskayClientModule(): SingletonModule = EriskayClientModule(this)
    override fun woozleClientModule(): SingletonModule = WoozleClientModule(this)
    override fun awsModule(): SingletonModule = AwsModule()

    override fun isRunningInDeveloperPersonalAccount(): Boolean =
        System.getenv("RUNNING_IN_DEVELOPER_ACCOUNT")?.toBoolean() ?: false
}

fun environmentVariable(name: String) =
    checkNotNull(System.getenv(name)) {
        "Environment Variable '$name' is not set"
    }
