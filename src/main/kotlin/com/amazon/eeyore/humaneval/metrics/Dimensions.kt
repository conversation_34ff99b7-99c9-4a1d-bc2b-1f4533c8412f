package com.amazon.eeyore.humaneval.metrics

// Dimensions style is to use Pascal<PERSON>ase rather than UPPER_SNAKE_CASE
@Suppress("ktlint:standard:property-naming")
object Dimensions {
    const val Operation = "Operation"
    const val Program = "Program"
}

const val METRIC_DIMENSION_PROGRAM_HARRIS = "HumanEval"

// DimensionSets are deliberately split by underscores.
@Suppress("ktlint:standard:enum-entry-name-case")
enum class DimensionSets(val dimensions: List<String>) {
    Program(listOf(Dimensions.Program)),
    Program_Operation(listOf(Dimensions.Program, Dimensions.Operation)),
}
