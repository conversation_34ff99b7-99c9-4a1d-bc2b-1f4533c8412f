package com.amazon.eeyore.humaneval.metrics

import com.amazon.adrisk.util.RequestContext
import com.amazon.coral.metrics.Metrics
import com.amazon.coral.metrics.helper.DelegateMetrics
import com.amazon.coral.service.AbstractHandler
import com.amazon.coral.service.Constant
import com.amazon.coral.service.Job
import io.github.oshai.kotlinlogging.KotlinLogging

internal val REQUEST_CONTEXT = Constant(RequestContext::class.java, "AdRiskRequestContext")

private val logger = KotlinLogging.logger {}

class RequestMetricsHandler : AbstractHandler() {
    override fun before(job: Job) {
        logger.debug { "Initializing RequestContext with job metrics." }
        job.setAttribute(REQUEST_CONTEXT, RequestContext(IgnoresCloseMetrics(job.metrics)))
        INPUT_REQUEST_COUNT.record(mapOf(Dimensions.Program to METRIC_DIMENSION_PROGRAM_HARRIS))
    }

    override fun after(job: Job) {
        logger.debug { "Closing RequestContext." }
        job.getAttribute(REQUEST_CONTEXT)?.close()
    }
}

/**
 * A metrics implementation which does not close when close is called. This is gnarly. RequestContext always closes the
 * metrics, however in this request flow, we do not want it to do so because the web server will close it for us, and
 * whilst close() is general idempotent, the server may elect to emit more metrics before closing. We do need to close
 * the RequestContext though, so this is passed to the RequestContext to prevent it closing the metrics.
 */
internal class IgnoresCloseMetrics(metrics: Metrics) : DelegateMetrics(metrics) {
    override fun close() {
        // ignore close
    }
}
