package com.amazon.eeyore.humaneval.model

/**
 * Represents the different types of resources that can be accessed in the system.
 *
 * This enum is used for resource-specific authorization checks.
 */
enum class ResourceType {
    /**
     * Permission to read signatures.
     */
    SIGNATURE_READ,

    /**
     * Permission to update signatures.
     */
    SIGNATURE_UPDATE,

    /**
     * Permission to override signatures.
     */
    SIGNATURE_OVERRIDE,
}
