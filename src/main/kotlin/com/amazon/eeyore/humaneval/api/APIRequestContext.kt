package com.amazon.eeyore.humaneval.api

import com.amazon.eeyore.humaneval.identity.User
import java.io.Closeable

/**
 * This class acts as a thread-local way of getting the username into Coral operations. It's created once per request
 * and lives for the duration of the session, closed by the request handler.
 */
class APIRequestContext(user: User) : Closeable {
    companion object {
        private val USER = ThreadLocal<User>()
        fun currentUser(): User? = USER.get()
    }

    init {
        USER.set(user)
    }

    override fun close() {
        USER.remove()
    }
}
