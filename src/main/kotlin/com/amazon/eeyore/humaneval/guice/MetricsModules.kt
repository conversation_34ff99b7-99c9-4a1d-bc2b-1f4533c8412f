package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.RequestContext
import com.amazon.adrisk.util.metrics.emf.AdRiskEMFMetricsFactory.Companion.emfReporter
import com.amazon.coral.metrics.MetricsFactory
import com.amazon.coral.metrics.helper.SensingMetricsHelper
import com.amazon.coral.metrics.reporter.ReporterFactory
import com.amazon.coral.support.emf.MetricOutput
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.metrics.DimensionSets
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton

private val dimensionSets = DimensionSets.entries.map { it.dimensions.toSet() }.toSet()

class HumanEvalFargateMetricsModule(private val configuration: HumanEvalServiceConfiguration) : AbstractModule() {
    @Provides
    @Singleton
    fun metricsFactory(): MetricsFactory =
        metricsFactory(MetricOutput.writeToLogger(), configuration, dimensionSets)

    @Provides
    fun requestContext(metricsFactory: MetricsFactory): RequestContext = RequestContext(metricsFactory)
}

fun metricsFactory(
    metricOutput: MetricOutput,
    configuration: HumanEvalServiceConfiguration,
    dimensionSets: Set<Set<String>>,
): MetricsFactory {
    val reporters = mutableListOf<ReporterFactory>()

    reporters.add(emfReporter(configuration.applicationName, metricOutput, dimensionSets))
    if (configuration.isOnePod) {
        reporters.add(emfReporter("${configuration.applicationName}-onepod", metricOutput, dimensionSets))
    }

    val factory = SensingMetricsHelper()
    factory.setReporters(reporters)
    factory.setProgram(configuration.applicationName)
    factory.setMarketplace("eeyore-he:${configuration.serviceStageName.configurationName}:${configuration.region}")
    factory.setSensors(listOf())
    return factory
}
