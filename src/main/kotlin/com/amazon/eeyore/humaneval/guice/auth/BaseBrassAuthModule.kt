package com.amazon.eeyore.humaneval.guice.auth

import com.amazon.brass.coral.calls.BrassServiceClient
import com.amazon.coral.client.CallAttachmentVisitor
import com.amazon.coral.client.Calls
import com.amazon.coral.client.ClientBuilder
import com.amazon.coral.retry.strategy.ExponentialBackoffAndJitterBuilder
import com.amazon.coralx.awsauth.AwsV4SigningCallVisitor
import com.amazon.eeyore.humaneval.config.BrassConfiguration
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.config.ServiceStageName
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.auth.BrassAuthService
import com.amazon.eeyore.humaneval.service.auth.CachingAuthService
import com.google.inject.Exposed
import com.google.inject.PrivateModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named
import io.github.oshai.kotlinlogging.KotlinLogging
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider

private val logger = KotlinLogging.logger {}

// Define bindle lock mappings for different environments
private val brassGammaBindleLockMapping =
    mapOf(
        ResourceType.SIGNATURE_READ to "amzn1.bindle.resource.24zzavuhy3pklwqsoceq",
        ResourceType.SIGNATURE_UPDATE to "amzn1.bindle.resource.24zzavuhy3pklwqsoceq",
        ResourceType.SIGNATURE_OVERRIDE to "amzn1.bindle.resource.24zzavuhy3pklwqsoceq",
    )

private val brassProdBindleLockMapping =
    mapOf(
        ResourceType.SIGNATURE_READ to "amzn1.bindle.resource.24zzavuhy3pklwqsoceq",
        ResourceType.SIGNATURE_UPDATE to "amzn1.bindle.resource.ht2nm6wdkpjystfp5ufa",
        ResourceType.SIGNATURE_OVERRIDE to "amzn1.bindle.resource.wi4q7xa43dml2nn4rmmq",
    )

class BaseBrassAuthModule(
    private val serviceConfiguration: HumanEvalServiceConfiguration,
    private val brassConfiguration: BrassConfiguration,
) : PrivateModule() {
    override fun configure() {
        // We use PrivateModule rather than AbstractModule here and we need to implement configure.
        // The method can't be empty because detekt might fail.
    }

    @Provides
    @Singleton
    @Exposed
    @Named("BRASS")
    fun authService(brassServiceClient: BrassServiceClient): AuthService {
        val bindleLockMapping =
            if (serviceConfiguration.serviceStageName == ServiceStageName.BETA) {
                logger.info { "Using GAMMA bindle locks for authorization" }
                brassGammaBindleLockMapping
            } else {
                logger.info { "Using PROD bindle locks for authorization" }
                brassProdBindleLockMapping
            }

        return CachingAuthService(BrassAuthService(brassServiceClient, bindleLockMapping))
    }

    @Provides
    @Singleton
    fun brassServiceClient(awsCredentialsProvider: AwsCredentialsProvider): BrassServiceClient {
        requireNotNull(brassConfiguration.brassCoralQualifier) { "Coral Qualifier for the BRASS client was null" }
        val retryStrategy = ExponentialBackoffAndJitterBuilder()
            .retryOn(Calls.getCommonRecoverableThrowables())
            .withMaxAttempts(2)
            .withInitialIntervalMillis(0.0)
            .newStrategy()

        return ClientBuilder()
            .remoteOf(BrassServiceClient::class.java)
            .withConfiguration(brassConfiguration.brassCoralQualifier)
            .withCallVisitors(
                AwsV4SigningCallVisitor(awsCredentialsProvider),
                CallAttachmentVisitor(Calls.retry(retryStrategy)),
            )
            .newClient()
    }
}
