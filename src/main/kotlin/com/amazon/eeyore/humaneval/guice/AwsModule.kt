package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.google.inject.Provides
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.S3Utilities
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import javax.inject.Singleton

class AwsModule : SingletonModule() {
    @Provides
    @Singleton
    fun provideS3Client(): S3Client =
        S3Client.builder().build()

    @Provides
    @Singleton
    fun provideS3Utilities(s3Client: S3Client): S3Utilities =
        s3Client.utilities()

    @Provides
    @Singleton
    fun provideS3Presigner(s3Client: S3Client): S3Presigner =
        S3Presigner.builder().s3Client(s3Client).build()

    @Provides
    @Singleton
    fun provideAwsCredentialsProvider(): AwsCredentialsProvider {
        return DefaultCredentialsProvider.create()
    }
}
