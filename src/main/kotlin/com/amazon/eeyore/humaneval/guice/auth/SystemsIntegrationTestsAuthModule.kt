package com.amazon.eeyore.humaneval.guice.auth

import com.amazon.eeyore.humaneval.config.BrassConfiguration
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.auth.IntegrationTestsAuthService
import com.amazon.eeyore.humaneval.service.auth.SystemsIntegrationTestAuthService
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named

/**
 * Guice module for providing a hybrid auth service for beta environment.
 *
 * This module combines the real Brass auth service with a mock auth service,
 * allowing real users to authenticate through Brass while test users bypass authentication.
 */
class SystemsIntegrationTestsAuthModule(
    private val configuration: HumanEvalServiceConfiguration,
    private val brassConfiguration: BrassConfiguration,
) : AbstractModule() {
    override fun configure() {
        install(BaseBrassAuthModule(configuration, brassConfiguration))
    }

    @Provides
    @Singleton
    fun authService(
        @Named("BRASS") brassService: AuthService,
    ): AuthService = SystemsIntegrationTestAuthService(
        userAuthService = brassService,
        testsAuthService = IntegrationTestsAuthService(),
    )
}
