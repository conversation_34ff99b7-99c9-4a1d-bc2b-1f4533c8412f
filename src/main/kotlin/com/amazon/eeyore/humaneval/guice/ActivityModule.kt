package com.amazon.eeyore.humaneval.guice

import com.amazon.eeyore.humaneval.activity.IdentityActivity
import com.amazon.eeyore.humaneval.activity.PermissionActivity
import com.amazon.eeyore.humaneval.activity.ThreatStoreActivity
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.google.inject.AbstractModule
import com.google.inject.Provides

/**
 * Guice module for providing Activity instances with their dependencies.
 */
class ActivityModule(
    private val configuration: HumanEvalServiceConfiguration,
) : AbstractModule() {
    /**
     * Provides ThreatStoreActivity with injected dependencies.
     *
     * @param threatStoreService The service for threat store operations
     * @param authService The service for authentication operations
     * @return Configured ThreatStoreActivity instance
     */
    @Provides
    fun threatStoreActivity(
        threatStoreService: ThreatStoreService,
        authService: AuthService,
    ): ThreatStoreActivity = ThreatStoreActivity(threatStoreService, authService, configuration)

    @Provides
    fun permissionActivity(authService: AuthService) = PermissionActivity(authService)

    @Provides
    fun identityActivity() = IdentityActivity()
}
