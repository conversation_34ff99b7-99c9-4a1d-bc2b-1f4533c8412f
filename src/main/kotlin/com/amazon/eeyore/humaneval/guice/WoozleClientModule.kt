package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.config.getThreatStoreClientRoleArn
import com.amazon.eeyore.humaneval.config.getThreatStoreConfig
import com.amazon.woozle.threatstore.WoozleThreatStoreClientFactory
import com.amazonaws.auth.STSAssumeRoleSessionCredentialsProvider
import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClient
import com.amazonaws.services.woozlethreatstore.WoozleThreatStore
import com.amazonaws.services.woozlethreatstore.WoozleThreatStoreClientBuilder
import com.google.inject.Provides
import io.github.oshai.kotlinlogging.KotlinLogging
import javax.inject.Singleton

private val logger = KotlinLogging.logger { }

internal const val THREAT_STORE_ENDPOINT_ENVIRONMENT_VARIABLE_NAME = "THREAT_STORE_ENDPOINT"

class WoozleClientModule(
    private val configuration: HumanEvalServiceConfiguration,
) : SingletonModule() {

    @Provides
    @Singleton
    fun woozleThreatStoreClient(): WoozleThreatStore {
        val stageName = configuration.serviceStageName.configurationName
        val threatStoreConfig = getThreatStoreConfig(stageName)
        val threatStoreClientRoleArn = getThreatStoreClientRoleArn(stageName)
        val stsClient = AWSSecurityTokenServiceClient.builder().build()
        val credentialsProvider = STSAssumeRoleSessionCredentialsProvider.Builder(
            threatStoreClientRoleArn,
            "HumanEvaluationWoozleClient",
        )
            .withRoleSessionDurationSeconds(60 * 60)
            .withStsClient(stsClient)
            .build()

        // In Personal Accounts, we can use a custom endpoint via environment variable for testing
        val customEndpoint = System.getenv(THREAT_STORE_ENDPOINT_ENVIRONMENT_VARIABLE_NAME)

        return if (configuration.isRunningInDeveloperPersonalAccount() && customEndpoint != null) {
            logger.info { "Using Threat Store Client in ${configuration.region} with custom endpoint $customEndpoint." }

            WoozleThreatStoreClientBuilder.standard()
                .withCredentials(credentialsProvider)
                .withEndpointConfiguration(AwsClientBuilder.EndpointConfiguration(customEndpoint, configuration.region))
                .build()
        } else {
            val stage = threatStoreConfig.stageName
            val region = configuration.region
            logger.info { "Using Threat Store Client in $stage.$region" }

            WoozleThreatStoreClientFactory.createThreatStoreClient(
                "$stage.$region",
                WoozleThreatStoreClientBuilder.standard().withCredentials(credentialsProvider),
            )
        }
    }
}
