package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humaneval.service.WoozleThreatStoreService
import com.amazonaws.services.woozlethreatstore.WoozleThreatStore
import com.google.inject.Provides
import javax.inject.Singleton

class ServiceModule(
    private val configuration: HumanEvalServiceConfiguration,
) : SingletonModule() {
    override fun configure() {
        install(configuration.eriskayClientModule())
        install(configuration.woozleClientModule())
    }

    @Provides
    @Singleton
    fun threatStoreService(woozleThreatStoreClient: WoozleThreatStore): ThreatStoreService {
        return WoozleThreatStoreService(woozleThreatStoreClient)
    }
}
