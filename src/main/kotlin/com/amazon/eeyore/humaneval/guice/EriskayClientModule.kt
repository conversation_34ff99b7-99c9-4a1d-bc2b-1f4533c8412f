package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.cloudauth.CloudAuthRegion
import com.amazon.cloudauth.client.CloudAuthCredentials.RegionalAwsCredentials
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eriskay.EriskayClient
import com.amazon.eriskay.EriskayClientFactory
import com.amazonaws.auth.STSAssumeRoleSessionCredentialsProvider
import com.google.inject.Provides
import io.github.oshai.kotlinlogging.KotlinLogging
import javax.inject.Singleton

private val logger = KotlinLogging.logger { }

private val ERISKAY_STAGE_MAPPING =
    mapOf(
        "beta" to "test",
        "gamma" to "gamma",
        "prod" to "prod",
    )

class EriskayClientModule(
    private val configuration: HumanEvalServiceConfiguration,
) : SingletonModule() {
    @Provides
    @Singleton
    fun eriskayClient(): EriskayClient {
        val stageName = configuration.serviceStageName.configurationName
        val eriskayAccessRoleCredentials =
            STSAssumeRoleSessionCredentialsProvider
                .Builder(
                    "arn:aws:iam::${configuration.awsAccountID}:role/HumanEvalEriskayAccessRole",
                    "HumanEvaluationEriskayClient",
                ).build()

        val cloudAuthCredentials = RegionalAwsCredentials(eriskayAccessRoleCredentials, CloudAuthRegion.US_EAST_1)
        var eriskayStageName = ERISKAY_STAGE_MAPPING[stageName]
        if (eriskayStageName == null) {
            logger.warn { "Attempting to access Eriskay Beta from Eeyore stage '$stageName' - access may be denied." }
            eriskayStageName = ERISKAY_STAGE_MAPPING["beta"]
        } else {
            logger.debug { "Accessing Eriskay '$eriskayStageName' from Eeyore stage '$stageName'." }
        }
        val scopes =
            setOf(
                "AMAZON_READ",
                "AMAZON_RENDER",
                "AMAZON_ANNOTATE",
                "CANARY_READ",
                "CANARY_RENDER",
                "CANARY_ANNOTATE",
                "APS_3PB_READ",
                "APS_3PB_RENDER",
                "APS_3PB_ANNOTATE",
            )

        return EriskayClientFactory.cloudAuthClient(
            "CloudAuth.$eriskayStageName.USAmazon",
            cloudAuthCredentials,
            scopes,
        )
    }
}
