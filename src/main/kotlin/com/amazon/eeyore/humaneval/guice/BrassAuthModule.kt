package com.amazon.eeyore.humaneval.guice

import com.amazon.eeyore.humaneval.config.BrassConfiguration
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.guice.auth.BaseBrassAuthModule
import com.amazon.eeyore.humaneval.service.AuthService
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton
import com.google.inject.name.Named

/**
 * Guice module for setting up BRASS-based authentication.
 *
 * This module provides the AuthService implementation and related dependencies.
 */
class BrassAuthModule(
    private val serviceConfiguration: HumanEvalServiceConfiguration,
    private val brassConfiguration: BrassConfiguration,
) : AbstractModule() {
    override fun configure() {
        install(BaseBrassAuthModule(serviceConfiguration, brassConfiguration))
    }

    @Provides
    @Singleton
    fun brassAuthService(@Named("BRASS") authService: AuthService): AuthService = authService
}
