package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazonaws.services.woozlethreatstore.model.AuthenticatedEntity
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent
import com.amazonaws.services.woozlethreatstore.model.IntelligenceLineage
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesResult
import com.amazonaws.services.woozlethreatstore.model.SignatureContent
import com.amazonaws.services.woozlethreatstore.model.SignatureListItem
import com.amazonaws.services.woozlethreatstore.model.ThreatClassification
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import com.google.inject.Provides
import com.google.inject.Singleton
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.Date
import java.util.UUID

private val logger = KotlinLogging.logger { }

class TestThreatStoreService : ThreatStoreService {

    override fun createSignatures(createRequests: List<CreateSignatureRequest>): List<CreateSignatureResult> {
        logger.info { "MockThreatStoreService: Creating ${createRequests.size} signatures" }
        return createRequests.map { createSignature(it) }
    }

    override fun createSignature(createRequest: CreateSignatureRequest): CreateSignatureResult {
        val domain = createRequest.content?.domainSignatureContent?.domain ?: "unknown-domain"
        val signatureId = "mock-signature-${UUID.randomUUID()}"

        logger.info { "MockThreatStoreService: Creating signature for domain '$domain' with ID '$signatureId'" }

        val result = CreateSignatureResult()
        try {
            result.signatureId = signatureId
            result.creationTime = Date()
        } catch (e: IllegalAccessException) {
            logger.warn { "Could not access properties on CreateSignatureResult: ${e.message}" }
        } catch (e: NoSuchMethodException) {
            logger.warn { "Method not found on CreateSignatureResult: ${e.message}" }
        } catch (e: SecurityException) {
            logger.warn { "Security exception accessing CreateSignatureResult: ${e.message}" }
        }
        return result
    }

    override fun validateSignatures(validateRequests: List<ValidateSignatureRequest>): List<ValidateSignatureResult> {
        logger.info { "MockThreatStoreService: Validating ${validateRequests.size} signatures" }
        return validateRequests.map { validateSignature(it) }
    }

    override fun validateSignature(validateRequest: ValidateSignatureRequest): ValidateSignatureResult {
        val domain = validateRequest.content?.domainSignatureContent?.domain ?: "unknown-domain"

        val (status, reason) = when {
            domain.contains("malicious") ||
                domain.contains("spam") -> "Rejected" to "Domain flagged as potentially malicious"
            domain.contains("test-reject") -> "Rejected" to "Test rejection for validation"
            domain.isBlank() -> "Rejected" to "Domain cannot be empty"
            else -> "Approved" to "Domain validation passed"
        }

        logger.info { "MockThreatStoreService: Validating domain '$domain' -> Status: $status, Reason: $reason" }

        val result = ValidateSignatureResult()
        try {
            result.status = status.uppercase()
            result.reason = reason
        } catch (e: IllegalAccessException) {
            logger.warn { "Could not access properties on ValidateSignatureResult: ${e.message}" }
        } catch (e: NoSuchMethodException) {
            logger.warn { "Method not found on ValidateSignatureResult: ${e.message}" }
        } catch (e: SecurityException) {
            logger.warn { "Security exception accessing ValidateSignatureResult: ${e.message}" }
        }
        return result
    }

    override fun searchSignatures(searchRequest: SearchSignaturesRequest): SearchSignaturesResult {
        val status = searchRequest.status
        val maxResults = searchRequest.maxResults ?: 50
        val nextToken = searchRequest.nextToken

        logger.info { "MockThreatStoreService: Searching signatures with status '$status', maxResults: $maxResults" }

        val mockSignatures = generateMockSignatures(status, maxResults, nextToken)

        return try {
            val responseNextToken = if (mockSignatures.size >= maxResults && nextToken == null) {
                "mock-next-token-${System.currentTimeMillis()}"
            } else {
                null
            }

            SearchSignaturesResult()
                .withItems(mockSignatures)
                .withNextToken(responseNextToken)
        } catch (e: IllegalStateException) {
            logger.warn(e) { "Could not create SearchSignaturesResult: ${e.message}" }
            SearchSignaturesResult().withItems(emptyList())
        }
    }

    private fun generateMockSignatures(status: String, maxResults: Int, nextToken: String?): List<SignatureListItem> {
        val baseSignatures = createBaseSignatures()
        val filteredSignatures = filterSignaturesByStatus(baseSignatures, status)
        return paginateSignatures(filteredSignatures, maxResults, nextToken)
    }

    private fun createBaseSignatures(): List<SignatureListItem> {
        return listOf(
            createMockSignature(
                MockSignatureParams(
                    "example.com",
                    "LIVE",
                    "THREAT",
                    "test-user",
                    "auditor-1",
                    "HUMAN",
                    "Malicious domain",
                ),
            ),
            createMockSignature(
                MockSignatureParams(
                    "malicious-site.net",
                    "SHADOW",
                    "THREAT",
                    "test-user",
                    "auditor-2",
                    "ADRISK_WEB_THREAT_HUNTING",
                    "Suspicious activity detected",
                ),
            ),
            createMockSignature(
                MockSignatureParams(
                    "safe-domain.org",
                    "LIVE_REFERRAL",
                    "SAFETY",
                    "test-user",
                    "auditor-1",
                    "GOOGLE",
                    "Safe domain for referral",
                ),
            ),
            createMockSignature(
                MockSignatureParams(
                    "test-domain.com",
                    "PENDING_MANUAL_REVIEW",
                    "THREAT",
                    "test-user",
                    "auditor-3",
                    "CONFIANT",
                    "Pending review",
                ),
            ),
            createMockSignature(
                MockSignatureParams(
                    "blocked-site.evil",
                    "REJECTED",
                    "THREAT",
                    "test-user",
                    "auditor-2",
                    "TMT",
                    "Rejected due to false positive",
                ),
            ),
        )
    }

    private fun filterSignaturesByStatus(signatures: List<SignatureListItem>, status: String): List<SignatureListItem> {
        return signatures.filter { it.status.equals(status, ignoreCase = true) }
    }

    private fun paginateSignatures(
        signatures: List<SignatureListItem>,
        maxResults: Int,
        nextToken: String?,
    ): List<SignatureListItem> {
        val startIndex = if (nextToken != null) 2 else 0
        val endIndex = minOf(startIndex + maxResults, signatures.size)

        return if (startIndex >= signatures.size) {
            emptyList()
        } else {
            signatures.subList(startIndex, endIndex)
        }
    }

    data class MockSignatureParams(
        val domain: String,
        val status: String,
        val decision: String,
        val creator: String,
        val auditor: String,
        val source: String,
        val description: String,
    )

    private fun createMockSignature(params: MockSignatureParams): SignatureListItem {
        val signature = SignatureListItem()
        try {
            signature.signatureId = "mock-sig-${UUID.randomUUID()}"
            signature.status = params.status
            signature.signatureDecisionType = params.decision

            val authenticatedEntity = AuthenticatedEntity()
                .withAuthenticatedUser(params.creator)
            signature.creator = authenticatedEntity
            signature.auditor = params.auditor
            signature.source = params.source
            signature.description = params.description
            signature.creationTime = Date()

            val domainContent = DomainSignatureContent()
                .withDomain(params.domain)
            val signatureContent = SignatureContent()
                .withDomainSignatureContent(domainContent)
            signature.content = signatureContent

            val lineage = IntelligenceLineage()
            signature.lineage = lineage

            val threatClass = ThreatClassification()
            signature.threatClassification = threatClass
        } catch (e: IllegalStateException) {
            logger.warn(e) { "Could not set properties on SignatureListItem: ${e.message}" }
        }
        return signature
    }
}

class TestWoozleServiceModule : SingletonModule() {

    @Provides
    @Singleton
    fun threatStoreService(): ThreatStoreService {
        logger.info { "Providing MockThreatStoreService for development/testing" }
        return TestThreatStoreService()
    }
}
