package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humaneval.util.MockWoozleDataGenerator
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.IntelligenceLineage
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsRequest
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsResult
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesResult
import com.amazonaws.services.woozlethreatstore.model.ThreatClassification
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import com.google.inject.Provides
import com.google.inject.Singleton
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.Date
import java.util.UUID

private val logger = KotlinLogging.logger { }



class TestThreatStoreService : ThreatStoreService {
    private val signatureService = MockSignatureService()
    private val detectionService = MockDetectionService()

    override fun createSignatures(createRequests: List<CreateSignatureRequest>) =
        createRequests.map { createSignature(it) }

    override fun createSignature(createRequest: CreateSignatureRequest) =
        signatureService.createSignature(createRequest)

    override fun validateSignatures(validateRequests: List<ValidateSignatureRequest>) =
        validateRequests.map { validateSignature(it) }

    override fun validateSignature(validateRequest: ValidateSignatureRequest) =
        signatureService.validateSignature(validateRequest)

    override fun searchSignatures(searchRequest: SearchSignaturesRequest) =
        signatureService.searchSignatures(searchRequest)

    override fun searchDetections(searchRequest: SearchDetectionsRequest) =
        detectionService.searchDetections(searchRequest)
}

class MockSignatureService {
    fun createSignature(createRequest: CreateSignatureRequest): CreateSignatureResult {
        val domain = createRequest.content?.domainSignatureContent?.domain ?: "unknown-domain"
        val signatureId = "mock-signature-${UUID.randomUUID()}"
        logger.info { "MockThreatStoreService: Creating signature for domain '$domain' with ID '$signatureId'" }
        return CreateSignatureResult().apply {
            this.signatureId = signatureId
            this.creationTime = Date()
        }
    }

    fun validateSignature(validateRequest: ValidateSignatureRequest): ValidateSignatureResult {
        val domain = validateRequest.content?.domainSignatureContent?.domain ?: "unknown-domain"
        val (status, reason) = when {
            domain.contains("malicious") || domain.contains("spam") -> "Rejected" to "Domain flagged as potentially malicious"
            domain.contains("test-reject") -> "Rejected" to "Test rejection for validation"
            domain.isBlank() -> "Rejected" to "Domain cannot be empty"
            else -> "Approved" to "Domain validation passed"
        }
        logger.info { "MockThreatStoreService: Validating domain '$domain' -> Status: $status, Reason: $reason" }
        return ValidateSignatureResult().apply {
            this.status = status.uppercase()
            this.reason = reason
        }
    }

    fun searchSignatures(searchRequest: SearchSignaturesRequest): SearchSignaturesResult {
        val status = searchRequest.status
        val maxResults = searchRequest.maxResults ?: 50
        val nextToken = searchRequest.nextToken
        logger.info { "MockThreatStoreService: Searching signatures with status '$status', maxResults: $maxResults" }
        val mockSignatures = MockWoozleDataGenerator.generateMockSignatures(status, maxResults, nextToken)
        val responseNextToken = if (mockSignatures.size >= maxResults && nextToken == null) {
            "mock-next-token-${System.currentTimeMillis()}"
        } else {
            null
        }
        return SearchSignaturesResult().withItems(mockSignatures as MutableCollection<SignatureListItem>).withNextToken(responseNextToken)
    }
}

class MockDetectionService {
    fun searchDetections(searchRequest: SearchDetectionsRequest): SearchDetectionsResult {
        val signatureId = searchRequest.signatureId ?: "unknown-signature"
        val detectionType = searchRequest.detectionType ?: "unknown-type"
        val maxResults = searchRequest.maxResults ?: 50
        val nextToken = searchRequest.nextToken
        logger.info { "MockThreatStoreService: Searching detections for signature '$signatureId' of type '$detectionType', maxResults: $maxResults" }
        val mockDetections = MockWoozleDataGenerator.generateMockDetections(signatureId, detectionType, maxResults, nextToken)
        val responseNextToken = if (mockDetections.size >= maxResults && nextToken == null) {
            "mock-detection-token-${System.currentTimeMillis()}"
        } else {
            null
        }
        return SearchDetectionsResult().withItems(mockDetections as MutableCollection<DetectionDetails>).withNextToken(responseNextToken)
    }
}

class TestWoozleServiceModule : SingletonModule() {

    @Provides
    @Singleton
    fun threatStoreService(): ThreatStoreService {
        logger.info { "Providing MockThreatStoreService for development/testing" }
        return TestThreatStoreService()
    }
}
