package com.amazon.eeyore.humaneval.guice.auth

import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.auth.IntegrationTestsAuthService
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton

/**
 * Guice module for providing a mock AuthService for integration tests.
 *
 * This module is used in the Alpha environment to bypass Brass authentication completely.
 */
class IntegrationTestsAuthModule : AbstractModule() {
    @Provides
    @Singleton
    fun authService(): AuthService = IntegrationTestsAuthService()
}
