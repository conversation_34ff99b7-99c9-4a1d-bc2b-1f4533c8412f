package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.config.DefaultBrassConfiguration
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.config.ServiceStageName
import com.amazon.eeyore.humaneval.guice.auth.IntegrationTestsAuthModule
import com.amazon.eeyore.humaneval.guice.auth.SystemsIntegrationTestsAuthModule
import com.amazon.eeyore.humaneval.service.BobcatService
import com.google.common.util.concurrent.ServiceManager
import com.google.inject.AbstractModule
import com.google.inject.Provides
import com.google.inject.Singleton

class HumanEvalServerModule(
    private val configuration: HumanEvalServiceConfiguration,
) : AbstractModule() {
    override fun configure() {
        install(HumanEvalFargateMetricsModule(configuration))
        install(CoralServerModule(configuration))
        install(configuration.awsModule())

        // Use mock Woozle services for ALPHA stage, real services for others
        when (configuration.serviceStageName) {
            ServiceStageName.ALPHA -> {
                install(AlphaServiceModule(configuration))
                install(IntegrationTestsAuthModule())
            }
            ServiceStageName.BETA -> {
                install(ServiceModule(configuration))
                val brassConfig = DefaultBrassConfiguration()
                install(SystemsIntegrationTestsAuthModule(configuration, brassConfig))
            }
            else -> {
                install(ServiceModule(configuration))
                val brassConfig = DefaultBrassConfiguration()
                install(BrassAuthModule(configuration, brassConfig))
            }
        }
        install(ActivityModule(configuration))
    }

    @Provides
    @Singleton
    fun serviceManager(bobcatService: BobcatService): ServiceManager = ServiceManager(listOf(bobcatService))
}

/**
 * Service module for ALPHA stage that provides mock Woozle service.
 * This avoids cross-account dependencies while still providing all necessary services.
 */
class AlphaServiceModule(
    private val configuration: HumanEvalServiceConfiguration,
) : SingletonModule() {
    override fun configure() {
        install(TestWoozleServiceModule())
    }
}
