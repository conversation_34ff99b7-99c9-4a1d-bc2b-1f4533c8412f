package com.amazon.eeyore.humaneval.guice

import com.amazon.coral.bobcat.Bobcat3EndpointConfig
import com.amazon.coral.bobcat.BobcatServer
import com.amazon.coral.bobcat.SelfSignedKeystoreConfig
import com.amazon.coral.guice.GuiceActivityHandler
import com.amazon.coral.guice.health.GuiceActivityHealthCheck
import com.amazon.coral.metrics.MetricsFactory
import com.amazon.coral.service.BasicShallowHealthCheck
import com.amazon.coral.service.HttpHandler
import com.amazon.coral.service.Log4jAwareRequestIdHandler
import com.amazon.coral.service.Orchestrator
import com.amazon.coral.service.PingHandler
import com.amazon.coral.service.RejectUnclaimedJobHandler
import com.amazon.coral.service.ServiceHandler
import com.amazon.coral.service.helper.ChainHelper
import com.amazon.coral.service.helper.OrchestratorHelper
import com.amazon.coral.service.http.CrossOriginHandler
import com.amazon.coral.service.http.JsonHttpBindingHandler
import com.amazon.coral.validate.ValidationHandler
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.config.ServiceStageName
import com.amazon.eeyore.humaneval.handler.IdentityHandler
import com.amazon.eeyore.humaneval.handler.IntegrationTestsIdentityHandler
import com.amazon.eeyore.humaneval.health.DeepHealthCheck
import com.amazon.eeyore.humaneval.health.HumanEvalServicePrimer
import com.amazon.eeyore.humaneval.metrics.RequestMetricsHandler
import com.amazon.eeyore.humaneval.service.BobcatService
import com.google.inject.AbstractModule
import com.google.inject.Injector
import com.google.inject.Provides
import com.google.inject.Singleton
import java.time.Duration

/**
 * The number of worker threads available to Coral. Combined with the maxRequestQueueDepth, if they are set too low,
 * once all threads are exhausted and the queue is full, then the service will reject work and emit the
 * RejectedRequests metric. Bobcat 3 will enqueue work by default, up to maxRequestQueueDepth (which is, by default,
 * twice the number of threads). This is fine for us as our operations are processed well faster than their SLA, and we
 * will auto-scale out if the load is sustained.
 *
 * Note that this number must be less than the maximum number of AWS connections, otherwise threads will block waiting
 * for an AWS connection from the pool. The default pool size in both AWS SDK v1 and v2 is 50 connections.
 */
const val NUM_THREADS = 32

private val IDLE_TIMEOUT = Duration.ofSeconds(65)
private val ORCHESTRATOR_SHUTDOWN_DELAY = Duration.ofSeconds(30)

class CoralServerModule(
    private val configuration: HumanEvalServiceConfiguration,
) : AbstractModule() {
    @Provides
    @Singleton
    fun bobcatService(bobcatServer: BobcatServer): BobcatService = BobcatService(bobcatServer)

    @Provides
    @Singleton
    fun bobcatServer(
        coral: Orchestrator,
        metricsFactory: MetricsFactory,
    ): BobcatServer {
        val endpointConfig = Bobcat3EndpointConfig()
        endpointConfig.setMetricsFactory(metricsFactory)
        endpointConfig.setOrchestrator(coral)
        endpointConfig.setNumThreads(NUM_THREADS)
        endpointConfig.setKeystoreConfig(SelfSignedKeystoreConfig())
        endpointConfig.setEndpoints(
            listOf(
                Bobcat3EndpointConfig.uri("http://0.0.0.0:8080"),
                Bobcat3EndpointConfig.uri("https://0.0.0.0:8443"),
            ),
        )
        endpointConfig.setOverrideRequestId(true)
        endpointConfig.setIdleTimeout(IDLE_TIMEOUT)
        endpointConfig.legacySslVipCompatibilityMode(false)
        return BobcatServer(endpointConfig)
    }

    @Provides
    @Singleton
    fun orchestrator(
        injector: Injector,
        deepPingHandler: PingHandler,
        primer: HumanEvalServicePrimer,
    ): Orchestrator {
        val corsHandler =
            CrossOriginHandler().apply {
                setEmitMetrics(true)
                setOriginValidator { origin -> configuration.allowedOrigins.contains(origin) }
            }

        val chainHelper = ChainHelper()
        chainHelper.addHandler(Log4jAwareRequestIdHandler())
        chainHelper.addHandler(HttpHandler())
        chainHelper.addHandler(corsHandler)
        chainHelper.addHandler(PingHandler(BasicShallowHealthCheck(), primer))
        chainHelper.addHandler(deepPingHandler)

        // In Alpha and Beta, we add a facade to auth which allows us to control the user with a specific header.
        val identityHandler = IdentityHandler()
        if (listOf(ServiceStageName.ALPHA, ServiceStageName.BETA).contains(configuration.serviceStageName)) {
            chainHelper.addHandler(IntegrationTestsIdentityHandler(identityHandler))
        } else {
            chainHelper.addHandler(identityHandler)
        }

        chainHelper.addHandler(ServiceHandler("EeyoreHumanEvaluationBackendApi"))
        chainHelper.addHandler(JsonHttpBindingHandler())
        chainHelper.addHandler(RejectUnclaimedJobHandler())

        chainHelper.addHandler(RequestMetricsHandler())

        chainHelper.addHandler(ValidationHandler())
        chainHelper.addHandler(GuiceActivityHandler.create(injector))

        return OrchestratorHelper(chainHelper, ORCHESTRATOR_SHUTDOWN_DELAY.toMillis())
    }

    @Provides
    @Singleton
    fun servicePrimer() = HumanEvalServicePrimer()

    @Provides
    @Singleton
    fun deepPingHandler(injector: Injector): PingHandler {
        val deepPingHandler = PingHandler(DeepHealthCheck(GuiceActivityHealthCheck(injector)))
        deepPingHandler.setLocalhostOnly(true)
        deepPingHandler.urIs = listOf("/deep_ping")
        return deepPingHandler
    }
}
