package com.amazon.eeyore.humaneval.handler

import com.amazon.coral.service.AbstractHandler
import com.amazon.coral.service.Constant
import com.amazon.coral.service.HttpConstant
import com.amazon.coral.service.Job
import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.identity.extractEcsUser
import io.github.oshai.kotlinlogging.KotlinLogging

internal val IDENTITY_CONTEXT = Constant(APIRequestContext::class.java, "AdRiskIdentityContext")

private val logger = KotlinLogging.logger {}

/**
 * Uses the Identity Parser on the HTTP Headers supplied by Coral to determine user identity.
 */
class IdentityHandler : AbstractHandler() {
    override fun before(job: Job) {
        logger.debug { "Determining user identity from Job." }

        val user = extractEcsUser(job.request.getAttribute(HttpConstant.HTTP_HEADERS))
        user?.let {
            logger.debug { "Determined user as '$it'." }
            val apiIdentityContext = APIRequestContext(it)
            job.setAttribute(IDENTITY_CONTEXT, apiIdentityContext)
        }
    }

    override fun after(job: Job) {
        logger.debug { "Clearing user identity." }
        job.getAttribute(IDENTITY_CONTEXT)?.close()
    }
}
