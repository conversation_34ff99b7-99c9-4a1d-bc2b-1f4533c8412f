package com.amazon.eeyore.humaneval.handler

import com.amazon.coral.service.AbstractHandler
import com.amazon.coral.service.HttpConstant
import com.amazon.coral.service.Job
import com.amazon.coral.service.http.HttpHeaders
import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.util.extractHeader
import io.github.oshai.kotlinlogging.KotlinLogging

private val logger = KotlinLogging.logger {}

internal const val INTEGRATION_TEST_AUTH_USER_HEADER = "x-humaneval-integ-test-auth-user"

/**
 * This handler looks for a specific header set by the integration tests when testing auth. It must never be used in
 * production. The handler chain should only install it in alpha and beta environments.
 *
 * If the header is not present it will delegate to the normal identity handler, provided.
 */
class IntegrationTestsIdentityHandler(private val identityHandler: IdentityHandler) : AbstractHandler() {
    override fun before(job: Job) {
        logger.debug { "Determining user identity from <PERSON>." }

        val user = extractIntegrationTestUser(job.request.getAttribute(HttpConstant.HTTP_HEADERS))
        if (user != null) {
            logger.debug { "Determined user as '$user'." }
            val apiIdentityContext = APIRequestContext(user)
            job.setAttribute(IDENTITY_CONTEXT, apiIdentityContext)
        } else {
            identityHandler.before(job)
        }
    }

    override fun after(job: Job) = identityHandler.after(job)

    private fun extractIntegrationTestUser(httpHeaders: HttpHeaders): User? {
        val integrationTestAuthHeader = extractHeader(httpHeaders, INTEGRATION_TEST_AUTH_USER_HEADER) ?: return null

        return User(integrationTestAuthHeader, UserType.Test)
    }
}
