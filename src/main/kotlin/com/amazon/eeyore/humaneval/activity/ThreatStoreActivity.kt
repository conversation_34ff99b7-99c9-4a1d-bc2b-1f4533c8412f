package com.amazon.eeyore.humaneval.activity

import com.amazon.coral.annotation.Operation
import com.amazon.coral.annotation.Service
import com.amazon.coral.service.Activity
import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humanevaluation.model.CreateSignaturesRequest
import com.amazon.eeyore.humanevaluation.model.CreateSignaturesResponse
import com.amazon.eeyore.humanevaluation.model.CreatedSignatureEntry
import com.amazon.eeyore.humanevaluation.model.Detection
import com.amazon.eeyore.humanevaluation.model.DetectionStatus
import com.amazon.eeyore.humanevaluation.model.DetectionType
import com.amazon.eeyore.humanevaluation.model.ForbiddenException
import com.amazon.eeyore.humanevaluation.model.ImpactAssessment
import com.amazon.eeyore.humanevaluation.model.ImpactedCreatives
import com.amazon.eeyore.humanevaluation.model.InternalException
import com.amazon.eeyore.humanevaluation.model.InvalidRequestParametersException
import com.amazon.eeyore.humanevaluation.model.SearchDetectionsRequest
import com.amazon.eeyore.humanevaluation.model.SearchDetectionsResponse
import com.amazon.eeyore.humanevaluation.model.SearchSignaturesRequest
import com.amazon.eeyore.humanevaluation.model.SearchSignaturesResponse
import com.amazon.eeyore.humanevaluation.model.SignatureEntry
import com.amazon.eeyore.humanevaluation.model.SignatureListItem
import com.amazon.eeyore.humanevaluation.model.UnauthorizedException
import com.amazon.eeyore.humanevaluation.model.ValidationResult
import com.amazonaws.services.woozlethreatstore.model.AuthenticatedEntity
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureContent
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent
import com.amazonaws.services.woozlethreatstore.model.EntrySystem
import com.amazonaws.services.woozlethreatstore.model.UIEntrySystem
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureContent
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import io.github.oshai.kotlinlogging.KotlinLogging
import com.amazonaws.services.woozlethreatstore.model.SearchDetectionsRequest as WoozleSearchDetectionsRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest as WoozleSearchSignaturesRequest

private val logger = KotlinLogging.logger { }

@Service("EeyoreHumanEvaluationBackendApi")
class ThreatStoreActivity(
    private val threatStoreService: ThreatStoreService,
    private val authService: AuthService,
    private val configuration: HumanEvalServiceConfiguration,
) : Activity() {

    @Operation("CreateSignatures")
    fun createSignature(request: CreateSignaturesRequest): CreateSignaturesResponse {
        logger.info { "Received CreateSignaturesRequest: $request" }

        assertAuth(ResourceType.SIGNATURE_UPDATE)

        val currentUser = APIRequestContext.currentUser()
        requireNotNull(currentUser) { "Current user is required for signature creation" }

        val createdSignatures = request.signatures.map { signature ->
            try {
                val validateRequest = buildValidateRequest(signature)
                val validationResult = threatStoreService.validateSignature(validateRequest)

                val validationResultModel = buildValidationResult(signature.domain, validationResult)
                if (validationResult.status?.uppercase() == "APPROVED") {
                    val createRequest = buildCreateRequest(signature, currentUser)
                    val createResult = threatStoreService.createSignature(createRequest)
                    buildSuccessEntry(signature.domain, createResult, validationResultModel)
                } else {
                    buildFailureEntry(signature.domain, validationResult.reason, validationResultModel)
                }
            } catch (e: InvalidRequestParametersException) {
                logger.error(e) { "Invalid request parameters for domain: ${signature.domain}" }
                buildErrorEntry(signature.domain, e.message)
            } catch (e: InternalException) {
                logger.error(e) { "Internal service error processing signature for domain: ${signature.domain}" }
                buildErrorEntry(signature.domain, e.message)
            }
        }

        return CreateSignaturesResponse.builder()
            .withCreatedSignatures(createdSignatures)
            .build()
    }

    @Operation("SearchSignatures")
    fun searchSignatures(request: SearchSignaturesRequest): SearchSignaturesResponse {
        logger.info { "Received SearchSignaturesRequest: $request" }

        assertAuth(ResourceType.SIGNATURE_READ)

        try {
            val woozleSearchRequest = WoozleSearchSignaturesRequest()
                .withStatus(request.status.uppercase())
                .withMaxResults(request.maxResults ?: 50)
                .withNextToken(request.nextToken)

            val woozleResult = threatStoreService.searchSignatures(woozleSearchRequest)

            val signatureItems = woozleResult.items?.map { woozleSignature ->
                SignatureListItem.builder()
                    .withSignatureId(woozleSignature.signatureId)
                    .withSignatureDecisionType(woozleSignature.signatureDecisionType)
                    .withStatus(woozleSignature.status)
                    .withCreator(
                        woozleSignature.creator.authenticatedUser
                            ?: woozleSignature.creator.authenticatedService,
                    )
                    .withAuditor(woozleSignature.auditor)
                    .withSource(woozleSignature.source)
                    .withDescription(woozleSignature.description)
                    .withCreationTime(woozleSignature.creationTime?.toString())
                    .withContent(
                        woozleSignature.content?.domainSignatureContent?.domain
                            ?: woozleSignature.content?.toString(),
                    )
                    .withLineage(woozleSignature.lineage?.toString())
                    .withThreatClassification(woozleSignature.threatClassification?.toString())
                    .build()
            } ?: emptyList()

            return SearchSignaturesResponse.builder()
                .withItems(signatureItems)
                .withNextToken(woozleResult.nextToken)
                .withTotalCount(signatureItems.size)
                .build()
        } catch (e: InvalidRequestParametersException) {
            logger.error(e) { "Invalid request parameters for search request with status: ${request.status}" }
            throw InvalidRequestParametersException("Invalid search parameters: ${e.message}", e)
        } catch (e: InternalException) {
            logger.error(e) { "Internal service error during signature search with status: ${request.status}" }
            throw e
        }
    }

    @Operation("SearchDetections")
    fun searchDetections(request: SearchDetectionsRequest): SearchDetectionsResponse {
        logger.info { "Received SearchDetectionsRequest: $request" }
        assertAuth(ResourceType.DETECTION_READ)

        try {
            val woozleSearchRequest = WoozleSearchDetectionsRequest()
                .withSignatureId(request.signatureId)
                .withDetectionType(request.detectionType)
                .withMaxResults(request.maxResults ?: 50)
                .withNextToken(request.nextToken)

            val woozleResult = threatStoreService.searchDetections(woozleSearchRequest)

            val detectionItems = woozleResult.items?.map { woozleDetection ->
                Detection.builder()
                    .withDetectionId(woozleDetection.detectionId ?: "")
                    .withDetectionType(DetectionType.SIMULATED)
                    .withSignatureId(request.signatureId)
                    .withStatus(DetectionStatus.PENDING_VERIFICATION)
                    .withCreationTime("")
                    .build()
            } ?: emptyList()

            return SearchDetectionsResponse.builder()
                .withItems(detectionItems)
                .withNextToken(woozleResult.nextToken)
                .build()
        } catch (e: InvalidRequestParametersException) {
            logger.error(e) { "Invalid request parameters for search detections: ${e.message}" }
            throw e
        } catch (e: InternalException) {
            logger.error(e) { "Internal service error during detection search: ${e.message}" }
            throw e
        }
    }

    private fun assertAuth(resourceType: ResourceType) {
        val currentUser = APIRequestContext.currentUser()
        if (currentUser == null) {
            logger.error { "Received a request without authentication details." }
            throw UnauthorizedException("Client is unauthorized")
        }

        val isAuthorized = authService.isAuthorized(currentUser, resourceType)
        if (!isAuthorized) {
            logger.info {
                "User '${currentUser.name}' made an unauthorized request to access resource type '$resourceType'. " +
                    "Required permission: $resourceType"
            }
            throw ForbiddenException("Unauthorized to access resource: $resourceType")
        }

        logger.debug {
            "User '${currentUser.name}' is authorized to access resource type '$resourceType'"
        }
    }

    private fun buildValidateRequest(signature: SignatureEntry): ValidateSignatureRequest {
        return ValidateSignatureRequest()
            .withSignatureDecisionType(signature.decision.uppercase())
            .withContent(
                ValidateSignatureContent()
                    .withDomainSignatureContent(
                        DomainSignatureContent()
                            .withDomain(signature.domain),
                    ),
            )
    }

    private fun buildValidationResult(domain: String, validationResult: ValidateSignatureResult): ValidationResult {
        val impactAssessment = validationResult.details?.impactAssessment?.let { woozleImpact ->
            ImpactAssessment.builder()
                .withTimesSeen(woozleImpact.timesSeen?.toInt() ?: 0)
                .withFirstPartyAdvertisersCount(woozleImpact.firstPartyAdvertisersCount?.toInt() ?: 0)
                .withImpactedCreatives(
                    woozleImpact.impactedCreatives?.let { woozleCreatives ->
                        ImpactedCreatives.builder()
                            .withSafeCreativesCount(woozleCreatives.safeCreativesCount?.toInt() ?: 0)
                            .withUnsafeCreativesCount(woozleCreatives.unsafeCreativesCount?.toInt() ?: 0)
                            .withClicks(woozleCreatives.clicks?.toInt() ?: 0)
                            .withImpressions(woozleCreatives.impressions?.toInt() ?: 0)
                            .build()
                    },
                )
                .build()
        }

        return ValidationResult.builder()
            .withDomain(domain)
            .withStatus(validationResult.status)
            .withReason(validationResult.reason)
            .withImpactAssessment(impactAssessment)
            .build()
    }

    private fun buildCreateRequest(signature: SignatureEntry, currentUser: User): CreateSignatureRequest {
        val authenticatedEntity = AuthenticatedEntity()
            .withAuthenticatedService("${configuration.awsAccountID}:ecs-service/${configuration.applicationName}")
            .withAuthenticatedUser(currentUser.name)

        val uiEntrySystem = UIEntrySystem()
            .withUiId("HumanexUI")
        val entrySystem = EntrySystem()
            .withUIEntrySystem(uiEntrySystem)

        return CreateSignatureRequest()
            .withSignatureDecisionType(signature.decision.uppercase())
            .withStatus(signature.status.uppercase())
            .withEntrySystem(entrySystem)
            .withCreator(authenticatedEntity)
            .withAuditor(currentUser.name)
            .withSource(signature.source)
            .withDescription(signature.description)
            .withContent(
                CreateSignatureContent()
                    .withDomainSignatureContent(
                        DomainSignatureContent()
                            .withDomain(signature.domain),
                    ),
            )
    }

    private fun buildSuccessEntry(
        domain: String,
        createResult: CreateSignatureResult,
        validationResult: ValidationResult,
    ): CreatedSignatureEntry {
        val createdAtTimestamp = createResult.creationTime?.toInstant()?.toString()
            ?: java.time.Instant.now().toString()

        return CreatedSignatureEntry.builder()
            .withDomain(domain)
            .withSignatureId(createResult.signatureId)
            .withCreatedAt(createdAtTimestamp)
            .withSuccess(true)
            .withValidationResult(validationResult)
            .build()
    }

    private fun buildFailureEntry(
        domain: String,
        reason: String?,
        validationResult: ValidationResult,
    ): CreatedSignatureEntry {
        return CreatedSignatureEntry.builder()
            .withDomain(domain)
            .withSuccess(false)
            .withErrorMessage("Validation failed: $reason")
            .withValidationResult(validationResult)
            .build()
    }

    private fun buildErrorEntry(domain: String, errorMessage: String?): CreatedSignatureEntry {
        return CreatedSignatureEntry.builder()
            .withDomain(domain)
            .withSuccess(false)
            .withErrorMessage("Error: $errorMessage")
            .build()
    }
}
