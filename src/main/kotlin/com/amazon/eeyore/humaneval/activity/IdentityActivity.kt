package com.amazon.eeyore.humaneval.activity

import com.amazon.coral.annotation.Operation
import com.amazon.coral.annotation.Service
import com.amazon.coral.service.Activity
import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humanevaluation.model.GetIdentityRequest
import com.amazon.eeyore.humanevaluation.model.GetIdentityResponse

/**
 * This is implementation of the GetIdentity activity.
 * It simply returns the caller and their profile picture URL.
 */
@Service("EeyoreHumanEvaluationBackendApi")
class IdentityActivity : Activity() {

    @Operation("GetIdentity")
    @Suppress("UNUSED_PARAMETER")
    fun handleHumanEvalRequest(requestType: GetIdentityRequest?): GetIdentityResponse? {
        val username: String? = APIRequestContext.currentUser()?.name.toString()

        return GetIdentityResponse
            .builder()
            .withUsername(username)
            .withIcon(String.format(PHOTO_FORMAT, username))
            .build()
    }

    companion object {
        private const val PHOTO_FORMAT = "https://internal-cdn.amazon.com/badgephotos.amazon.com/?uid=%s"
    }
}
