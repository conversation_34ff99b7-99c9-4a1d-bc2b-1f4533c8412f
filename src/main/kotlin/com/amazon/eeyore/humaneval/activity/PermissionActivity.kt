package com.amazon.eeyore.humaneval.activity

import com.amazon.coral.annotation.Operation
import com.amazon.coral.annotation.Service
import com.amazon.coral.service.Activity
import com.amazon.coral.service.LogRequests
import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humanevaluation.model.GetPermissionsRequest
import com.amazon.eeyore.humanevaluation.model.GetPermissionsResponse
import com.amazon.eeyore.humanevaluation.model.PermissionID
import com.amazon.eeyore.humanevaluation.model.PermissionObject
import com.amazon.eeyore.humanevaluation.model.UnauthorizedException
import io.github.oshai.kotlinlogging.KotlinLogging

private val logger = KotlinLogging.logger { }

@Service("EeyoreHumanEvaluationBackendApi")
class PermissionActivity(private val authService: AuthService) : Activity() {

    @Operation("GetPermissions")
    @LogRequests(logOutput = false)
    @Suppress("UNUSED_PARAMETER")
    fun getPermissions(input: GetPermissionsRequest): GetPermissionsResponse {
        val currentUser = APIRequestContext.currentUser()
        if (currentUser == null) {
            logger.error { "Received a request without authentication details." }
            throw UnauthorizedException("Client is unauthorized")
        }

        val authorizedResourceTypes = ResourceType.entries
            .map { it to authService.isAuthorized(currentUser, it) }
            .filter { it.second }
            .map { it.first }

        val mappedPermissions = authorizedResourceTypes.map {
            PermissionObject.builder().withPermissionID(convertResourceTypeToPermissionID(it)).build()
        }

        return GetPermissionsResponse.builder()
            .withPermissions(mappedPermissions)
            .build()
    }

    private fun convertResourceTypeToPermissionID(resourceType: ResourceType) = when (resourceType) {
        ResourceType.SIGNATURE_READ -> PermissionID.SIGNATURE_READ
        ResourceType.SIGNATURE_UPDATE -> PermissionID.SIGNATURE_UPDATE
        ResourceType.SIGNATURE_OVERRIDE -> PermissionID.SIGNATURE_OVERRIDE
        ResourceType.DETECTION_READ -> PermissionID.DETECTION_READ
    }
}
