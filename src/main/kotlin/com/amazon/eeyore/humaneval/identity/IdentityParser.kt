package com.amazon.eeyore.humaneval.identity

import com.amazon.coral.service.http.HttpHeaders
import com.amazon.eeyore.humaneval.util.extractHeader
import io.github.oshai.kotlinlogging.KotlinLogging
import java.util.regex.Pattern

private val logger = KotlinLogging.logger { }

const val HTTP_HEADER_COGNITO_AUTHENTICATION_PROVIDER = "x-eeyore-cognito-configuration-provider"
const val HTTP_HEADER_USER_ARN = "x-eeyore-user-arn"

val COGNITO_USER_PREFIX_REGEX = Pattern.compile("arn:aws[a-z-]*:iam::[0-9]+:oidc-provider")
val USER_PREFIX_REGEX = Pattern.compile("arn:aws[a-z-]*:iam::[0-9]+:user")
val ROLE_PREFIX_REGEX = Pattern.compile("arn:aws[a-z-]*:iam::[0-9]+:role")
val ASSUMED_ROLE_PREFIX_REGEX = Pattern.compile("arn:aws[a-z-]*:sts::[0-9]+:assumed-role")

const val IAM_USER_ROLE_DELIMITER = '/'
const val OIDC_PROVIDER_DELIMITER = ':'

fun extractEcsUser(httpHeaders: HttpHeaders): User? {
    val cognitoAuthenticationProvider = extractHeader(httpHeaders, HTTP_HEADER_COGNITO_AUTHENTICATION_PROVIDER)
    cognitoAuthenticationProvider?.let {
        return extract(it)
    }

    val userArn = extractHeader(httpHeaders, HTTP_HEADER_USER_ARN)
    userArn?.let {
        return extract(it)
    }

    logger.info { "No user information present in the supplied headers." }
    return null
}

private fun extract(userString: String): User? {
    return if (COGNITO_USER_PREFIX_REGEX.matcher(userString).find()) {
        buildUser(UserType.Cognito, userString, OIDC_PROVIDER_DELIMITER)
    } else if (USER_PREFIX_REGEX.matcher(userString).find()) {
        buildUser(UserType.User, userString, IAM_USER_ROLE_DELIMITER)
    } else if (ROLE_PREFIX_REGEX.matcher(userString).find()) {
        buildUser(UserType.Role, userString, IAM_USER_ROLE_DELIMITER)
    } else if (ASSUMED_ROLE_PREFIX_REGEX.matcher(userString).find()) {
        buildUser(UserType.AssumedRole, userString, IAM_USER_ROLE_DELIMITER)
    } else {
        return null
    }
}

private fun buildUser(type: UserType, userString: String, delimiter: Char): User {
    val nameStartIndex = userString.lastIndexOf(delimiter) + 1
    val name = userString.substring(nameStartIndex)
    return User(name, type)
}
