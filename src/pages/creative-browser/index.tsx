import React from "react";
import {<PERSON><PERSON><PERSON>, SpaceBetween, Box, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>} from "@cloudscape-design/components";
import BreadcrumbNavigation from "../../components/breadcrumb";

const CreativeBrowserPage: React.FC = () => {
    const openSkyeLink = () => {
        window.open("https://prod.skye.advertising.amazon.dev", "_blank");
    };

    return (
        <Container>
            <SpaceBetween size="l">
                <BreadcrumbNavigation />

                <Header variant="h1">Creative Browser</Header>

                <Alert type="info">
                    <Box variant="h4">Creative Browser is now on Skye</Box>
                    <Box variant="p">
                        The Creative Browser functionality has been moved to the Skye platform. Click the button below
                        to open the Skye Creative Browser in a new tab.
                    </Box>
                </Alert>

                <Box textAlign="center">
                    <Button onClick={openSkyeLink} variant="primary" iconName="external">
                        Open Skye Creative Browser
                    </Button>
                </Box>
            </SpaceBetween>
        </Container>
    );
};

export default CreativeBrowserPage;
