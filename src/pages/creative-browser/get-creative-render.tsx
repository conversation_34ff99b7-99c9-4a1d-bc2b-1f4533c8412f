import * as React from "react";
import {useParams} from "react-router-dom";

interface State {
    eriskayID: string;
    renderID: string;
}

export const GetCreativeRender: React.FC = () => {
    const params = useParams<{eriskayID: string; renderID: string}>();
    const [state] = React.useState<State>({
        eriskayID: params.eriskayID || "",
        renderID: params.renderID || ""
    });

    React.useEffect(() => {
        document.title = "Eeyore - Creative Browser - View Render (Loading...)";

        const redirectUrl = `https://prod.skye.advertising.amazon.dev/#/creative-render/${state.eriskayID}/${state.renderID}`;
        window.location.href = redirectUrl;
    }, [state.eriskayID, state.renderID]);

    return <div />;
};

export default GetCreativeRender;
