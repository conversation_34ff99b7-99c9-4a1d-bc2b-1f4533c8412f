import * as React from "react";
import {useParams} from "react-router-dom";

interface State {
    creativeID: string;
    creativeIDSpace: string;
    variantID?: string;
}

export const GetCreativeRenders: React.FC = () => {
    const params = useParams<{creativeID?: string; creativeIDSpace?: string; variantID?: string}>();

    const [state] = React.useState<State>({
        creativeID: params.creativeID || "",
        creativeIDSpace: params.creativeIDSpace || "AAX_CRID",
        variantID: params.variantID
    });

    React.useEffect(() => {
        document.title = "Eeyore - Creative Browser";

        let redirectUrl = "https://prod.skye.advertising.amazon.dev/#/creative";

        if (state.creativeID) {
            redirectUrl += `/${state.creativeIDSpace}/${state.creativeID}`;

            if (state.variantID) {
                redirectUrl += `/${state.variantID}`;
            }
        }

        window.location.href = redirectUrl;
    }, [state.creativeID, state.creativeIDSpace, state.variantID]);

    return <div />;
};

export default GetCreativeRenders;
