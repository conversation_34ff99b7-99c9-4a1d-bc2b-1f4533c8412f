/* eslint-disable require-await */
import React, {useState, useCallback, useRef} from "react";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>} from "@cloudscape-design/components";
import BreadcrumbNavigation from "../../components/breadcrumb";
import {SignatureInput, SignatureUploadApi, Status, Decision, IntelligenceSource} from "../../api/signature-upload-api";
import FormTable from "../../components/signature-upload/form-table";
import CsvForm from "../../components/signature-upload/csv-form";
import ResultsModal from "../../components/signature-upload/results-modal";

import {ExtendedSignatureEntry} from "../../components/signature-upload/results-modal";

const SignatureUploadPage: React.FC = () => {
    const [formRows, setFormRows] = useState<SignatureInput[]>([
        {
            domain: "",
            source: "",
            description: "",
            status: Status.SHADOW,
            decision: Decision.THREAT
        }
    ]);
    const [uploadMode, setUploadMode] = useState<string>("add");
    const [formErrors, setFormErrors] = useState<boolean>(true);
    const [fieldErrors, setFieldErrors] = useState<{[key: string]: boolean}>({});
    const [touchedFields, setTouchedFields] = useState<{[key: string]: boolean}>({});

    const [csvFile, setCsvFile] = useState<File | null>(null);
    const [csvError, setCsvError] = useState<string | null>(null);

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [uploadResults, setUploadResults] = useState<ExtendedSignatureEntry[]>([]);
    const [showNotification, setShowNotification] = useState<boolean>(false);

    const api = useRef(new SignatureUploadApi());

    const validateDomainFormat = useCallback((domain: string): boolean => {
        return domain.includes(".") && !domain.includes(" ");
    }, []);

    const [duplicateDomainError, setDuplicateDomainError] = useState<string | null>(null);

    const validateForm = useCallback(
        (rows: SignatureInput[]) => {
            let hasErrors = false;
            const newFieldErrors: {[key: string]: boolean} = {};

            const domains = rows.map(row => row.domain.trim().toLowerCase()).filter(domain => domain !== "");
            const uniqueDomains = new Set(domains);

            if (domains.length !== uniqueDomains.size) {
                const duplicates = domains.filter((domain, index) => domains.indexOf(domain) !== index);
                const uniqueDuplicates = [...new Set(duplicates)];
                setDuplicateDomainError(
                    `Duplicate domains found: ${uniqueDuplicates.join(
                        ", "
                    )}. Please remove duplicates before submitting.`
                );
                hasErrors = true;
            } else {
                setDuplicateDomainError(null);
            }

            rows.forEach((row, index) => {
                if (!row.domain) {
                    newFieldErrors[`domain-${index}`] = false;
                    hasErrors = true;
                } else if (!validateDomainFormat(row.domain)) {
                    newFieldErrors[`domain-${index}`] = false;
                    hasErrors = true;
                } else {
                    newFieldErrors[`domain-${index}`] = false;
                }

                if (!row.source) {
                    newFieldErrors[`source-${index}`] = false;
                    hasErrors = true;
                } else if (!Object.values(IntelligenceSource).includes(row.source as IntelligenceSource)) {
                    newFieldErrors[`source-${index}`] = false;
                    hasErrors = true;
                } else {
                    newFieldErrors[`source-${index}`] = false;
                }

                if (!row.description) {
                    newFieldErrors[`description-${index}`] = false;
                    hasErrors = true;
                } else {
                    newFieldErrors[`description-${index}`] = false;
                }

                if (!row.status) {
                    newFieldErrors[`status-${index}`] = false;
                    hasErrors = true;
                } else if (![Status.SHADOW, Status.LIVE].includes(row.status as Status)) {
                    newFieldErrors[`status-${index}`] = false;
                    hasErrors = true;
                } else {
                    newFieldErrors[`status-${index}`] = false;
                }

                if (!row.decision) {
                    newFieldErrors[`decision-${index}`] = false;
                    hasErrors = true;
                } else if (![Decision.THREAT, Decision.SAFETY].includes(row.decision as Decision)) {
                    newFieldErrors[`decision-${index}`] = false;
                    hasErrors = true;
                } else {
                    newFieldErrors[`decision-${index}`] = false;
                }
            });

            setFieldErrors(newFieldErrors);
            setFormErrors(hasErrors);
        },
        [validateDomainFormat]
    );

    React.useEffect(() => {
        validateForm(formRows);
    }, [formRows, validateForm]);

    const handleAddRow = () => {
        setFormRows([
            ...formRows,
            {
                domain: "",
                source: "",
                description: "",
                status: Status.SHADOW,
                decision: Decision.THREAT
            }
        ]);
    };

    const handleRemoveRow = (index: number) => {
        const updatedRows = [...formRows];
        updatedRows.splice(index, 1);
        setFormRows(updatedRows);
    };

    const handleFormChange = (index: number, field: keyof SignatureInput, value: string) => {
        const updatedRows = [...formRows];
        updatedRows[index] = {...updatedRows[index], [field]: value};
        setFormRows(updatedRows);
        validateForm(updatedRows);
    };

    const handleFieldBlur = (index: number, field: keyof SignatureInput) => {
        setTouchedFields(prev => ({
            ...prev,
            [`${field}-${index}`]: true
        }));
    };

    const handleCsvUpload = (event: {detail: {value: File[]}}) => {
        setCsvError(null);

        if (event.detail.value.length > 0) {
            const file = event.detail.value[0];
            setCsvFile(file);

            try {
                const reader = new FileReader();
                reader.onload = e => {
                    const content = e.target?.result as string;
                    const lines = content.trim().split("\n");
                    if (lines.length === 0) {
                        setCsvError("CSV file is empty");
                        return;
                    }

                    if (lines.length === 1) {
                        setCsvError("CSV file contains only headers. Please add at least one data row.");
                        return;
                    }

                    const headers = lines[0].split(",");
                    const requiredColumns = ["Domain", "Source", "Description", "Status", "Decision"];
                    const missingColumns = requiredColumns.filter(col => !headers.includes(col));

                    if (missingColumns.length > 0) {
                        setCsvError(`CSV is not valid. Missing required columns: ${missingColumns.join(", ")}`);
                        return;
                    }

                    const errors: string[] = [];
                    for (let i = 1; i < lines.length; i++) {
                        const values = lines[i].split(",");
                        if (values.length !== headers.length) {
                            errors.push(`Row ${i}: Invalid number of columns`);
                            continue;
                        }

                        const domainIndex = headers.indexOf("Domain");
                        const sourceIndex = headers.indexOf("Source");
                        const descriptionIndex = headers.indexOf("Description");
                        const statusIndex = headers.indexOf("Status");
                        const decisionIndex = headers.indexOf("Decision");

                        if (!values[domainIndex]) {
                            errors.push(`Row ${i}: Domain is required`);
                        } else if (!validateDomainFormat(values[domainIndex])) {
                            errors.push(`Row ${i}: Invalid domain format. Domain must contain a dot and no spaces`);
                        }

                        if (!values[sourceIndex]) {
                            errors.push(`Row ${i}: Source is required`);
                        } else if (
                            !Object.values(IntelligenceSource).includes(values[sourceIndex] as IntelligenceSource)
                        ) {
                            errors.push(
                                `Row ${i}: Invalid Source value. Must be one of the valid intelligence sources.`
                            );
                        }

                        if (!values[descriptionIndex]) {
                            errors.push(`Row ${i}: Description is required`);
                        }

                        if (
                            values[statusIndex] &&
                            ![String(Status.SHADOW), String(Status.LIVE)].includes(values[statusIndex].toLowerCase())
                        ) {
                            errors.push(
                                `Row ${i}: Invalid Status value. Must be one of: ${String(Status.SHADOW)}, ${String(
                                    Status.LIVE
                                )}`
                            );
                        }

                        if (
                            values[decisionIndex] &&
                            ![String(Decision.THREAT), String(Decision.SAFETY)].includes(
                                values[decisionIndex].toLowerCase()
                            )
                        ) {
                            errors.push(
                                `Row ${i}: Invalid Decision value. Must be one of: ${String(Decision.THREAT)}, ${String(
                                    Decision.SAFETY
                                )}`
                            );
                        }
                    }

                    if (errors.length > 0) {
                        setCsvError(`CSV validation failed:\n${errors.join("\n")}`);
                    }
                };

                reader.readAsText(file);
            } catch (error) {
                setCsvError("Failed to process CSV file");
            }
        } else {
            setCsvFile(null);
        }
    };

    const handleFormSubmit = () => {
        setIsLoading(true);

        try {
            const failedEntries = formRows.map(row => ({
                domain: row.domain,
                signatureId: null,
                createdAt: new Date().toISOString(),
                success: false,
                errorMessage: "Feature not yet live. Backend integration is pending.",
                validationDetails: {
                    status: "REJECTED" as const,
                    reason: "Feature not implemented",
                    impactAssessment: {
                        timesSeen: 0,
                        firstPartyAdvertisersCount: 0,
                        impactedCreatives: {
                            safeCreativesCount: 0,
                            unsafeCreativesCount: 0,
                            clicks: 0,
                            impressions: 0
                        }
                    }
                },
                originalInput: {
                    domain: row.domain,
                    source: row.source,
                    description: row.description || "",
                    status: row.status,
                    decision: row.decision
                }
            }));
            setUploadResults(failedEntries);
            setShowNotification(true);
        } catch (error) {
            setUploadResults([
                {
                    domain: "Error",
                    signatureId: null,
                    createdAt: null,
                    success: false,
                    errorMessage: "Failed to submit form. Please try again.",
                    validationDetails: {
                        status: "REJECTED",
                        reason: "API error",
                        impactAssessment: {
                            timesSeen: 0,
                            firstPartyAdvertisersCount: 0,
                            impactedCreatives: {
                                safeCreativesCount: 0,
                                unsafeCreativesCount: 0,
                                clicks: 0,
                                impressions: 0
                            }
                        }
                    }
                }
            ]);
            setShowNotification(true);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCsvSubmit = () => {
        setIsLoading(true);

        if (!csvFile || csvError) {
            setIsLoading(false);
            return;
        }

        try {
            const reader = new FileReader();
            reader.onload = e => {
                const csvContent = e.target?.result as string;
                api.current
                    .validateCsvContent(csvContent)
                    .then(validationResult => {
                        if (validationResult.valid) {
                            const failedEntries = validationResult.signatures.map(signature => ({
                                domain: signature.domain,
                                signatureId: null,
                                createdAt: new Date().toISOString(),
                                success: false,
                                errorMessage: "Feature not yet live. Backend integration is pending.",
                                validationDetails: {
                                    status: "REJECTED" as const,
                                    reason: "Feature not implemented",
                                    impactAssessment: {
                                        timesSeen: 0,
                                        firstPartyAdvertisersCount: 0,
                                        impactedCreatives: {
                                            safeCreativesCount: 0,
                                            unsafeCreativesCount: 0,
                                            clicks: 0,
                                            impressions: 0
                                        }
                                    }
                                },
                                originalInput: {
                                    domain: signature.domain,
                                    source: signature.source,
                                    description: signature.description || "",
                                    status: signature.status,
                                    decision: signature.decision
                                }
                            }));
                            setUploadResults(failedEntries);
                        } else {
                            setUploadResults([
                                {
                                    domain: "Error",
                                    signatureId: null,
                                    createdAt: null,
                                    success: false,
                                    errorMessage: "CSV validation failed",
                                    validationDetails: {
                                        status: "REJECTED",
                                        reason: "Validation error",
                                        impactAssessment: {
                                            timesSeen: 0,
                                            firstPartyAdvertisersCount: 0,
                                            impactedCreatives: {
                                                safeCreativesCount: 0,
                                                unsafeCreativesCount: 0,
                                                clicks: 0,
                                                impressions: 0
                                            }
                                        }
                                    }
                                }
                            ]);
                        }
                        setShowNotification(true);
                        setIsLoading(false);
                    })
                    .catch(() => {
                        setUploadResults([
                            {
                                domain: "Error",
                                signatureId: null,
                                createdAt: null,
                                success: false,
                                errorMessage: "Failed to validate CSV. Please try again.",
                                validationDetails: {
                                    status: "REJECTED",
                                    reason: "API error",
                                    impactAssessment: {
                                        timesSeen: 0,
                                        firstPartyAdvertisersCount: 0,
                                        impactedCreatives: {
                                            safeCreativesCount: 0,
                                            unsafeCreativesCount: 0,
                                            clicks: 0,
                                            impressions: 0
                                        }
                                    }
                                }
                            }
                        ]);
                        setShowNotification(true);
                        setIsLoading(false);
                    });
            };
            reader.readAsText(csvFile);
        } catch (error) {
            setUploadResults([
                {
                    domain: "Error",
                    signatureId: null,
                    createdAt: null,
                    success: false,
                    errorMessage: "Failed to submit CSV. Please try again.",
                    validationDetails: {
                        status: "REJECTED",
                        reason: "API error",
                        impactAssessment: {
                            timesSeen: 0,
                            firstPartyAdvertisersCount: 0,
                            impactedCreatives: {
                                safeCreativesCount: 0,
                                unsafeCreativesCount: 0,
                                clicks: 0,
                                impressions: 0
                            }
                        }
                    }
                }
            ]);
            setShowNotification(true);
            setIsLoading(false);
        }
    };

    const handleReset = () => {
        setFormRows([
            {
                domain: "",
                source: "",
                description: "",
                status: Status.SHADOW,
                decision: Decision.THREAT
            }
        ]);
        setCsvFile(null);
    };

    const handleDownloadTemplate = () => {
        const headerRow = "Domain,Source,Description,Status,Decision";
        const templateContent = headerRow;

        const blob = new Blob([templateContent], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "domain_upload_template.csv";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Container>
            <SpaceBetween size="l">
                <BreadcrumbNavigation />

                <Alert type="info">This feature is not yet live. Backend integration is pending.</Alert>

                <Header variant="h1">Upload Signature</Header>

                <Tabs
                    tabs={[
                        {
                            label: "Form Upload",
                            id: "form",
                            content: (
                                <FormTable
                                    formRows={formRows}
                                    uploadMode={uploadMode}
                                    touchedFields={touchedFields}
                                    fieldErrors={fieldErrors}
                                    isLoading={isLoading}
                                    handleAddRow={handleAddRow}
                                    handleRemoveRow={handleRemoveRow}
                                    handleFormChange={handleFormChange}
                                    handleFieldBlur={handleFieldBlur}
                                    setUploadMode={setUploadMode}
                                    handleFormSubmit={handleFormSubmit}
                                    handleReset={handleReset}
                                    formErrors={formErrors}
                                    duplicateDomainError={duplicateDomainError}
                                />
                            )
                        },
                        {
                            label: "CSV Upload",
                            id: "csv",
                            content: (
                                <CsvForm
                                    csvFile={csvFile}
                                    csvError={csvError}
                                    uploadMode={uploadMode}
                                    isLoading={isLoading}
                                    handleCsvUpload={handleCsvUpload}
                                    handleDownloadTemplate={handleDownloadTemplate}
                                    setUploadMode={setUploadMode}
                                    handleCsvSubmit={handleCsvSubmit}
                                    handleReset={handleReset}
                                />
                            )
                        }
                    ]}
                />

                <ResultsModal
                    visible={showNotification}
                    uploadResults={uploadResults}
                    onDismiss={() => setShowNotification(false)}
                />
            </SpaceBetween>
        </Container>
    );
};

export default SignatureUploadPage;
