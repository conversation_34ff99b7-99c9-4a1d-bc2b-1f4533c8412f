import React, {useState, useCallback, useMemo, useEffect} from "react";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Bet<PERSON><PERSON>, Ta<PERSON>} from "@cloudscape-design/components";
import BreadcrumbNavigation from "../../components/breadcrumb";
import {SignatureInput, SignatureUploadApi} from "../../api/signature-upload-api";
import {permissionsApi} from "../../api";
import {
    IntelligenceSource,
    IntelligenceLineage,
    ThreatClassification,
    PermissionID
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

import {createErrorEntry} from "../../api/utils/data-transformation";
import FormTable from "../../components/signature-upload/form-table";
import CsvForm from "../../components/signature-upload/csv-form";
import ResultsModal from "../../components/signature-upload/results-modal";
import {ExtendedSignatureEntry} from "../../components/signature-upload/results-modal";

const initialFormRow: SignatureInput = {
    domain: "",
    source: "" as IntelligenceSource,
    description: "",
    status: "",
    decision: "",
    lineage: undefined,
    threatClassification: undefined
};

const SignatureUploadPage: React.FC = () => {
    const [hasUpdatePermission, setHasUpdatePermission] = useState<boolean>(false);
    const [permissionsLoading, setPermissionsLoading] = useState<boolean>(true);
    const [formRows, setFormRows] = useState<SignatureInput[]>([initialFormRow]);
    const [uploadMode, setUploadMode] = useState<string>("add");
    const [fieldErrors] = useState<{[key: string]: boolean}>({});
    const [touchedFields, setTouchedFields] = useState<{[key: string]: boolean}>({});
    const [formErrors, setFormErrors] = useState<boolean>(true); // Initialize to true so button is disabled until form is valid

    const [csvFile, setCsvFile] = useState<File | null>(null);
    const [csvError, setCsvError] = useState<string | null>(null);

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [uploadResults, setUploadResults] = useState<ExtendedSignatureEntry[]>([]);
    const [showNotification, setShowNotification] = useState<boolean>(false);

    const api = useMemo(() => new SignatureUploadApi(), []);

    const [duplicateDomainError] = useState<string | null>(null);

    useEffect(() => {
        const checkPermissions = async () => {
            try {
                const permissions = await permissionsApi.getUserPermissions();
                setHasUpdatePermission(permissions.includes(PermissionID.SIGNATURE_UPDATE));
            } catch (error) {
                // Handle error silently
                setHasUpdatePermission(false);
            } finally {
                setPermissionsLoading(false);
            }
        };
        checkPermissions();
    }, []);

    const handleAddRow = useCallback(() => {
        setFormRows(prev => [...prev, {...initialFormRow}]);
    }, []);

    const handleRemoveRow = useCallback((index: number) => {
        setFormRows(prev => prev.filter((_, i) => i !== index));
    }, []);

    const validateFormRows = useCallback((rows: SignatureInput[]) => {
        // Check if any required fields are empty
        return rows.some(row => !row.domain.trim() || !row.source || !row.description || !row.status || !row.decision);
    }, []);

    const handleFormChange = useCallback(
        (index: number, field: keyof SignatureInput, value: string) => {
            setFormRows(prev => {
                const updatedRows = [...prev];
                let processedValue:
                    | string
                    | IntelligenceSource
                    | IntelligenceLineage
                    | ThreatClassification
                    | undefined = value;

                if ((field === "lineage" || field === "threatClassification") && value === "") {
                    processedValue = undefined;
                } else if (field === "source") {
                    processedValue = value as IntelligenceSource;
                } else if (field === "lineage") {
                    processedValue = value as IntelligenceLineage;
                } else if (field === "threatClassification") {
                    processedValue = value as ThreatClassification;
                }

                updatedRows[index] = {...updatedRows[index], [field]: processedValue};

                // Update form errors
                setFormErrors(validateFormRows(updatedRows));

                return updatedRows;
            });
        },
        [validateFormRows]
    );

    const handleFieldBlur = useCallback((index: number, field: keyof SignatureInput) => {
        setTouchedFields(prev => ({
            ...prev,
            [`${field}-${index}`]: true
        }));
    }, []);

    const handleCsvUpload = useCallback(
        (event: {detail: {value: File[]}}) => {
            setCsvError(null);

            if (event.detail.value.length > 0) {
                const file = event.detail.value[0];
                setCsvFile(file);

                api.processCsvFile(file)
                    .then(result => {
                        if (!result.valid && result.errors) {
                            setCsvError(result.errors.join("\n"));
                        }
                    })
                    .catch(error => {
                        setCsvError(
                            `Failed to process CSV file: ${error instanceof Error ? error.message : "Unknown error"}`
                        );
                    });
            } else {
                setCsvFile(null);
            }
        },
        [api]
    );

    const handleFormSubmit = useCallback(async () => {
        const hasEmptyDomains = formRows.some(row => !row.domain.trim());
        if (hasEmptyDomains) {
            setUploadResults([createErrorEntry("Validation Error", "All domains must be filled in")]);
            setShowNotification(true);
            return;
        }

        setIsLoading(true);

        const signatureInputs: SignatureInput[] = formRows.map(row => ({
            domain: row.domain,
            source: row.source as IntelligenceSource,
            description: row.description || "",
            status: row.status,
            decision: row.decision,
            lineage: row.lineage as IntelligenceLineage | undefined,
            threatClassification: row.threatClassification as ThreatClassification | undefined
        }));

        try {
            const response = await api.createSignatures(signatureInputs);

            const resultsWithOriginalInput = response.signatureEntries.map((entry, index) => {
                const input = signatureInputs[index];
                return {
                    ...entry,
                    originalInput: input
                        ? {
                              domain: input.domain,
                              source: input.source,
                              description: input.description,
                              status: input.status,
                              decision: input.decision,
                              lineage: input.lineage,
                              threatClassification: input.threatClassification
                          }
                        : undefined
                };
            });

            setUploadResults(resultsWithOriginalInput);
            setShowNotification(true);
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message.includes("Unauthorized")
                        ? "Access denied: You don&apos;t have permission to create signatures. Please contact your administrator."
                        : error.message
                    : "Failed to submit form. Please try again.";

            const errorResults = signatureInputs.map(input => ({
                ...createErrorEntry(input.domain, errorMessage),
                originalInput: {
                    domain: input.domain,
                    source: input.source,
                    description: input.description,
                    status: input.status,
                    decision: input.decision,
                    lineage: input.lineage,
                    threatClassification: input.threatClassification
                }
            }));

            setUploadResults(errorResults);
            setShowNotification(true);
        } finally {
            setIsLoading(false);
        }
    }, [formRows, api]);

    const handleCsvSubmit = useCallback(async () => {
        if (!csvFile || csvError) {
            return;
        }

        setIsLoading(true);

        try {
            const validationResult = await api.processCsvFile(csvFile);

            if (validationResult.valid && validationResult.signatures) {
                const response = await api.createSignatures(validationResult.signatures);

                const resultsWithOriginalInput = response.signatureEntries.map((entry, index) => {
                    const signature = validationResult.signatures && validationResult.signatures[index];
                    return {
                        ...entry,
                        originalInput: signature
                            ? {
                                  domain: signature.domain,
                                  source: signature.source,
                                  description: signature.description,
                                  status: signature.status,
                                  decision: signature.decision,
                                  lineage: signature.lineage,
                                  threatClassification: signature.threatClassification
                              }
                            : {
                                  domain: "",
                                  source: "",
                                  description: "",
                                  status: "",
                                  decision: "",
                                  lineage: undefined,
                                  threatClassification: undefined
                              }
                    };
                });

                setUploadResults(resultsWithOriginalInput);
            } else {
                const errorMessage = `CSV validation failed: ${validationResult.errors?.join(", ") || "Unknown error"}`;
                setUploadResults([createErrorEntry("Error", errorMessage)]);
            }
            setShowNotification(true);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to process CSV. Please try again.";
            setUploadResults([createErrorEntry("Error", errorMessage)]);
            setShowNotification(true);
        } finally {
            setIsLoading(false);
        }
    }, [csvFile, csvError, api]);

    const handleReset = useCallback(() => {
        setFormRows([{...initialFormRow}]);
        setCsvFile(null);
        setCsvError(null);
        setFormErrors(true); // Set form errors to true on reset since fields will be empty
    }, []);

    const handleDownloadTemplate = useCallback(() => {
        const headerRow = "Domain,Source,Description,Status,Decision,Lineage,ThreatClassification";
        const templateContent = headerRow;

        const blob = new Blob([templateContent], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "domain_upload_template.csv";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, []);

    if (permissionsLoading) {
        return (
            <div style={{maxWidth: "1400px", margin: "0 auto", padding: "20px"}}>
                <Container>
                    <SpaceBetween size="l">
                        <BreadcrumbNavigation />
                        <Header variant="h1">Upload Signature</Header>
                        <div>Loading permissions...</div>
                    </SpaceBetween>
                </Container>
            </div>
        );
    }

    return (
        <div style={{maxWidth: "1400px", margin: "0 auto", padding: "20px"}}>
            <Container>
                <SpaceBetween size="l">
                    <BreadcrumbNavigation />

                    <Header variant="h1">Upload Signature</Header>

                    <Tabs
                        tabs={[
                            {
                                label: "Form Upload",
                                id: "form",
                                content: (
                                    <FormTable
                                        formRows={formRows}
                                        uploadMode={uploadMode}
                                        touchedFields={touchedFields}
                                        fieldErrors={fieldErrors}
                                        isLoading={isLoading}
                                        handleAddRow={handleAddRow}
                                        handleRemoveRow={handleRemoveRow}
                                        handleFormChange={handleFormChange}
                                        handleFieldBlur={handleFieldBlur}
                                        handleFormSubmit={handleFormSubmit}
                                        handleReset={handleReset}
                                        formErrors={formErrors}
                                        duplicateDomainError={duplicateDomainError}
                                        hasUpdatePermission={hasUpdatePermission}
                                    />
                                )
                            },
                            {
                                label: "CSV Upload",
                                id: "csv",
                                content: (
                                    <CsvForm
                                        csvFile={csvFile}
                                        csvError={csvError}
                                        uploadMode={uploadMode}
                                        isLoading={isLoading}
                                        handleCsvUpload={handleCsvUpload}
                                        handleDownloadTemplate={handleDownloadTemplate}
                                        setUploadMode={setUploadMode}
                                        handleCsvSubmit={handleCsvSubmit}
                                        handleReset={handleReset}
                                        hasUpdatePermission={hasUpdatePermission}
                                    />
                                )
                            }
                        ]}
                    />

                    <ResultsModal
                        visible={showNotification}
                        uploadResults={uploadResults}
                        onDismiss={() => setShowNotification(false)}
                    />
                </SpaceBetween>
            </Container>
        </div>
    );
};

export default SignatureUploadPage;
