import React, {useEffect} from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    SpaceBetween,
    Table,
    Button,
    Box,
    StatusIndicator,
    Badge,
    ColumnLayout,
    KeyValuePairs,
    ExpandableSection,
    TextContent
} from "@cloudscape-design/components";
import {useParams, useNavigate} from "react-router-dom";
import BreadcrumbNavigation from "../../components/breadcrumb";
import {useSignatureSearchStore} from "../../store/signature-search-store";
import {Detection} from "../../api/detection-search-api";

const DetectionDetailsPage: React.FC = () => {
    const {signatureId} = useParams<{signatureId: string}>();
    const navigate = useNavigate();

    const {signatures} = useSignatureSearchStore();
    const signatureDetails = signatures.find(sig => sig.signatureId === signatureId);

    // Mock detections for now
    const detections: Detection[] = [];
    const detectionsLoading = false;

    const handleBack = () => {
        navigate("/referred-signatures");
    };

    const handleRefresh = () => {
        // Refresh functionality to be implemented
    };

    const getStatusIndicator = (status: string) => {
        switch (status) {
            case "UNSAFE":
                return <StatusIndicator type="error">Unsafe</StatusIndicator>;
            case "FALSE_POSITIVE":
                return <StatusIndicator type="success">False Positive</StatusIndicator>;
            case "PENDING_VERIFICATION":
                return <StatusIndicator type="pending">Pending Verification</StatusIndicator>;
            case "SHADOW":
                return <StatusIndicator type="info">Shadow</StatusIndicator>;
            default:
                return <StatusIndicator type="info">{status}</StatusIndicator>;
        }
    };

    const columnDefinitions = [
        {
            id: "detectionId",
            header: "Detection ID",
            cell: (item: Detection) => (
                <Box fontSize="body-s" color="text-body-secondary">
                    {item.detectionId}
                </Box>
            ),
            width: 150
        },
        {
            id: "detectionType",
            header: "Type",
            cell: (item: Detection) => (
                <Badge color={item.detectionType === "ONLINE" ? "blue" : "grey"}>{item.detectionType}</Badge>
            ),
            width: 100
        },
        {
            id: "status",
            header: "Status",
            cell: (item: Detection) => getStatusIndicator(item.status),
            width: 150
        },
        {
            id: "matchType",
            header: "Match Type",
            cell: (item: Detection) => (
                <Badge color={item.matchType === "EXACT" ? "green" : "blue"}>{item.matchType}</Badge>
            ),
            width: 120
        },
        {
            id: "creationTime",
            header: "Created",
            cell: (item: Detection) => <Box fontSize="body-s">{new Date(item.creationTime).toLocaleString()}</Box>,
            width: 180
        }
    ];

    return (
        <Container>
            <SpaceBetween size="l">
                <BreadcrumbNavigation />

                <Header
                    variant="h1"
                    actions={
                        <SpaceBetween direction="horizontal" size="xs">
                            <Button onClick={handleBack} iconName="arrow-left">
                                Back to Signatures
                            </Button>
                            <Button onClick={handleRefresh} iconName="refresh" loading={detectionsLoading}>
                                Refresh
                            </Button>
                        </SpaceBetween>
                    }
                >
                    Signature Details
                </Header>

                {/* Signature Details Section */}
                <Container header={<Header variant="h2">Signature Information</Header>}>
                    <ColumnLayout columns={2}>
                        <KeyValuePairs
                            columns={1}
                            items={[
                                {
                                    label: "Signature ID",
                                    value: (
                                        <SpaceBetween direction="horizontal" size="xs">
                                            <TextContent>
                                                <code>{signatureId}</code>
                                            </TextContent>
                                            <Button
                                                variant="icon"
                                                iconName="copy"
                                                onClick={() => navigator.clipboard.writeText(signatureId || "")}
                                                ariaLabel="Copy signature ID"
                                            />
                                        </SpaceBetween>
                                    )
                                },
                                {
                                    label: "Domain/Content",
                                    value: signatureDetails?.content || "-"
                                },
                                {
                                    label: "Decision",
                                    value: signatureDetails ? (
                                        <StatusIndicator
                                            type={
                                                signatureDetails.signatureDecisionType === "THREAT"
                                                    ? "error"
                                                    : "success"
                                            }
                                        >
                                            {signatureDetails.signatureDecisionType}
                                        </StatusIndicator>
                                    ) : (
                                        "-"
                                    )
                                },
                                {
                                    label: "Source",
                                    value: signatureDetails ? (
                                        <Badge color="blue">{signatureDetails.source}</Badge>
                                    ) : (
                                        "-"
                                    )
                                }
                            ]}
                        />
                        <KeyValuePairs
                            columns={1}
                            items={[
                                {
                                    label: "Creator",
                                    value: signatureDetails?.creator || "-"
                                },
                                {
                                    label: "Created",
                                    value: signatureDetails
                                        ? new Date(signatureDetails.creationTime).toLocaleString()
                                        : "-"
                                },
                                {
                                    label: "Status",
                                    value: signatureDetails ? (
                                        <StatusIndicator type="warning">{signatureDetails.status}</StatusIndicator>
                                    ) : (
                                        "-"
                                    )
                                },
                                {
                                    label: "Threat Classification",
                                    value: signatureDetails?.threatClassification ? (
                                        <Badge color="red">{signatureDetails.threatClassification}</Badge>
                                    ) : (
                                        "-"
                                    )
                                }
                            ]}
                        />
                    </ColumnLayout>

                    {signatureDetails?.description && (
                        <ExpandableSection headerText="Description" defaultExpanded>
                            <TextContent>
                                <p>{signatureDetails.description}</p>
                            </TextContent>
                        </ExpandableSection>
                    )}
                </Container>

                {/* Detection Details Section */}
                <Container
                    header={
                        <Header variant="h2" counter={`(${detections.length})`}>
                            Detection Details
                        </Header>
                    }
                >
                    <Table
                        columnDefinitions={columnDefinitions}
                        items={detections}
                        loading={detectionsLoading}
                        loadingText="Loading detections..."
                        empty={
                            <Box textAlign="center" color="inherit">
                                <b>No detections found</b>
                                <Box padding={{bottom: "s"}} variant="p" color="inherit">
                                    No detection events found for this signature.
                                </Box>
                            </Box>
                        }
                        variant="container"
                        stripedRows
                        stickyHeader
                    />
                </Container>
            </SpaceBetween>
        </Container>
    );
};

export default DetectionDetailsPage;
