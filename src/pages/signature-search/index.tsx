import React, {useEffect, useState} from "react";
import {
    <PERSON><PERSON><PERSON>,
    Header,
    <PERSON>,
    Box,
    SpaceBetween,
    Button,
    Al<PERSON>,
    Pa<PERSON><PERSON>,
    Ta<PERSON>,
    Badge
} from "@cloudscape-design/components";
import BreadcrumbNavigation from "../../components/breadcrumb";
import {useSignatureSearchStore} from "../../store/signature-search-store";
import {SignatureStatus, SignatureListItem} from "../../api/signature-search-api";

const SignatureSearchPage: React.FC = () => {
    const {signatures, loading, error, searchSignatures, clearError} = useSignatureSearchStore();

    const [currentPageIndex, setCurrentPageIndex] = useState(1);
    const [activeTabId, setActiveTabId] = useState<string>("all");
    const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
    const pageSize = 25;

    useEffect(() => {
        searchSignatures(SignatureStatus.PENDING_MANUAL_REVIEW);
    }, [searchSignatures]);

    // Get unique sources from signatures
    const allSources = [...new Set(signatures.map(sig => sig.source))];

    // Filter signatures by active tab
    const filteredSignatures =
        activeTabId === "all" ? signatures : signatures.filter(sig => sig.source === activeTabId);

    // Pagination
    const startIndex = (currentPageIndex - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedSignatures = filteredSignatures.slice(startIndex, endIndex);
    const totalPages = Math.ceil(filteredSignatures.length / pageSize);

    const handleSignatureClick = (signatureId: string) => {
        // Open in a new tab with the correct path
        // The detection API will be called when the page loads
        window.open(`#/signature/${signatureId}`, "_blank");
    };

    const handleSelectionChange = ({detail}: {detail: {selectedItems: any[]}}) => {
        const newSelectedItems = new Set<string>();
        detail.selectedItems.forEach(item => newSelectedItems.add(item.signatureId));
        setSelectedItems(newSelectedItems);
    };

    const handleApprove = () => {
        alert(`Approving ${selectedItems.size} signatures: ${Array.from(selectedItems).join(", ")}`);
        // Here you would call the API to approve the signatures
        setSelectedItems(new Set());
    };

    const handleReject = () => {
        alert(`Rejecting ${selectedItems.size} signatures: ${Array.from(selectedItems).join(", ")}`);
        // Here you would call the API to reject the signatures
        setSelectedItems(new Set());
    };

    return (
        <Container
            header={
                <Header
                    variant="h1"
                    description="Search and review signatures by status"
                    actions={
                        <Button
                            iconName="refresh"
                            onClick={() => searchSignatures(SignatureStatus.PENDING_MANUAL_REVIEW, true)}
                            loading={loading}
                        >
                            Refresh
                        </Button>
                    }
                >
                    Referred Signatures
                </Header>
            }
        >
            <SpaceBetween size="l">
                <BreadcrumbNavigation />

                {error && (
                    <Alert type="error" dismissible onDismiss={clearError} header="Error loading signatures">
                        {error}
                    </Alert>
                )}

                {allSources.length > 0 && (
                    <div style={{overflowX: "auto"}}>
                        <Tabs
                            activeTabId={activeTabId}
                            onChange={({detail}) => {
                                setActiveTabId(detail.activeTabId);
                                setCurrentPageIndex(1);
                            }}
                            tabs={[
                                {
                                    label: (
                                        <>
                                            All Sources <Badge color="blue">{signatures.length}</Badge>
                                        </>
                                    ),
                                    id: "all"
                                },
                                ...allSources.map(source => ({
                                    label: (
                                        <>
                                            {source}{" "}
                                            <Badge color="blue">
                                                {signatures.filter(sig => sig.source === source).length}
                                            </Badge>
                                        </>
                                    ),
                                    id: source
                                }))
                            ]}
                        />
                    </div>
                )}

                <SpaceBetween size="s">
                    {selectedItems.size > 0 && (
                        <Box float="right">
                            <SpaceBetween direction="horizontal" size="xs">
                                <Button onClick={handleApprove} variant="primary">
                                    Approve ({selectedItems.size})
                                </Button>
                                <Button onClick={handleReject} variant="normal">
                                    Reject ({selectedItems.size})
                                </Button>
                            </SpaceBetween>
                        </Box>
                    )}

                    <Table
                        selectionType="multi"
                        selectedItems={paginatedSignatures.filter(item => selectedItems.has(item.signatureId))}
                        onSelectionChange={handleSelectionChange}
                        columnDefinitions={[
                            {
                                id: "signatureId",
                                header: "Signature ID",
                                cell: item => (
                                    <SpaceBetween direction="horizontal" size="xxs">
                                        <Button variant="link" onClick={() => handleSignatureClick(item.signatureId)}>
                                            {item.signatureId.substring(0, 10)}...
                                        </Button>
                                        <Button 
                                            iconName="copy" 
                                            variant="icon" 
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                navigator.clipboard.writeText(item.signatureId);
                                            }}
                                            ariaLabel="Copy signature ID"
                                        />
                                    </SpaceBetween>
                                ),
                                minWidth: 220,
                                sortingField: "signatureId"
                            },
                            {
                                id: "content",
                                header: "Domain",
                                cell: item => item.content,
                                minWidth: 150,
                                sortingField: "content"
                            },
                            {
                                id: "requestedStatus",
                                header: "Requested Status",
                                cell: item =>
                                    item.requestedStatus ? (
                                        <Badge color={item.requestedStatus === "SHADOW" ? "grey" : "green"}>
                                            {item.requestedStatus}
                                        </Badge>
                                    ) : (
                                        "-"
                                    ),
                                minWidth: 130
                            },
                            {
                                id: "source",
                                header: "Source",
                                cell: item => item.source,
                                minWidth: 150,
                                sortingField: "source"
                            }
                        ]}
                        items={paginatedSignatures}
                        loading={loading}
                        loadingText="Loading signatures..."
                        empty={
                            <Box textAlign="center" color="inherit">
                                <b>No signatures found</b>
                                <Box padding={{bottom: "s"}} variant="p" color="inherit">
                                    No signatures match the current filter criteria.
                                </Box>
                            </Box>
                        }
                        variant="container"
                        stripedRows
                        stickyHeader
                        wrapLines={false}
                        resizableColumns
                    />
                </SpaceBetween>

                <Pagination
                    currentPageIndex={currentPageIndex}
                    pagesCount={totalPages}
                    onChange={({detail}) => setCurrentPageIndex(detail.currentPageIndex)}
                    ariaLabels={{
                        nextPageLabel: "Next page",
                        previousPageLabel: "Previous page",
                        pageLabel: pageNumber => `Page ${pageNumber} of ${totalPages}`
                    }}
                />
            </SpaceBetween>
        </Container>
    );
};

export default SignatureSearchPage;