import React, {Component} from "react";
import {CenteredErrorMessage} from "../../components/centered";

interface SomethingBrokenProps {
    message?: string;
}

export default class SomethingBroken extends Component<SomethingBrokenProps> {
    public render() {
        const {message = "I'm sorry, but something is broken"} = this.props;
        return (
            <div>
                <CenteredErrorMessage message={message} />
            </div>
        );
    }
}
