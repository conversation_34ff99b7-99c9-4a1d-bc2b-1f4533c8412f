import React, {useState, useEffect} from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    SpaceBetween,
    Button,
    Alert,
    Box,
    StatusIndicator,
    ColumnLayout,
    KeyValuePairs,
    Table,
    Badge,
    Link,
    Spinner
} from "@cloudscape-design/components";
import {useParams, useNavigate} from "react-router-dom";
import BreadcrumbNavigation from "../../components/breadcrumb";

// Mock detection data based on the Smithy model
interface DetectionDetails {
    detectionId: string;
    detectionType: "ONLINE" | "SIMULATED";
    eriskayId: string;
    signatureId: string;
    landingPage: string;
    renderId: string;
    status: "PENDING_VERIFICATION" | "UNSAFE" | "FALSE_POSITIVE" | "SHADOW";
    creationTime: string;
    lastUpdatedTime: string;
    matchType: "EXACT" | "SIMILARITY";
    validationDetails: Record<
        string,
        {
            status: string;
            reason?: string;
        }
    >;
    metadata?: string;
}

const SignatureDetailsPage: React.FC = () => {
    const {signatureId} = useParams<{signatureId: string}>();
    const navigate = useNavigate();
    const [detections, setDetections] = useState<DetectionDetails[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // Mock data loading - replace with actual API call
        const loadDetectionDetails = async () => {
            setIsLoading(true);
            setError(null);

            try {
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Mock detection data
                const mockDetections: DetectionDetails[] = [
                    {
                        detectionId: `${signatureId}#detection1`,
                        detectionType: "SIMULATED",
                        eriskayId: "eriskay-123456",
                        signatureId: signatureId || "",
                        landingPage: "https://example-malicious-site.com/landing",
                        renderId: "render-789012",
                        status: "PENDING_VERIFICATION",
                        creationTime: new Date(Date.now() - 86400000).toISOString(),
                        lastUpdatedTime: new Date(Date.now() - 3600000).toISOString(),
                        matchType: "EXACT",
                        validationDetails: {
                            "ALLOWLIST": {
                                status: "PENDING_VALIDATION",
                                reason: "NONE"
                            },
                            "PROFOUND": {
                                status: "VALIDATED_DETECTION",
                                reason: "VERIFIED_MALICIOUS_SIGNAL"
                            },
                            "MANUAL": {
                                status: "PENDING_VALIDATION",
                                reason: "NONE"
                            }
                        },
                        metadata: "Additional context about this detection"
                    },
                    {
                        detectionId: `${signatureId}#detection2`,
                        detectionType: "ONLINE",
                        eriskayId: "eriskay-654321",
                        signatureId: signatureId || "",
                        landingPage: "https://another-suspicious-site.com/page",
                        renderId: "render-345678",
                        status: "UNSAFE",
                        creationTime: new Date(Date.now() - 172800000).toISOString(),
                        lastUpdatedTime: new Date(Date.now() - 7200000).toISOString(),
                        matchType: "SIMILARITY",
                        validationDetails: {
                            "ALLOWLIST": {
                                status: "FALSE_DETECTION",
                                reason: "NO_MALICIOUS_SIGNAL"
                            },
                            "PROFOUND": {
                                status: "VALIDATED_DETECTION",
                                reason: "VERIFIED_MALICIOUS_SIGNAL"
                            },
                            "MANUAL": {
                                status: "VALIDATED_DETECTION",
                                reason: "VERIFIED_MALICIOUS_SIGNAL"
                            }
                        }
                    }
                ];

                setDetections(mockDetections);
            } catch (err) {
                setError("Failed to load detection details");
            } finally {
                setIsLoading(false);
            }
        };

        if (signatureId) {
            loadDetectionDetails();
        }
    }, [signatureId]);

    const getStatusIndicator = (status: string) => {
        switch (status.toUpperCase()) {
            case "PENDING_VERIFICATION":
                return <StatusIndicator type="pending">Pending Verification</StatusIndicator>;
            case "UNSAFE":
                return <StatusIndicator type="error">Unsafe</StatusIndicator>;
            case "FALSE_POSITIVE":
                return <StatusIndicator type="success">False Positive</StatusIndicator>;
            case "SHADOW":
                return <StatusIndicator type="info">Shadow</StatusIndicator>;
            default:
                return <StatusIndicator type="info">{status}</StatusIndicator>;
        }
    };

    const getValidationStatusIndicator = (status: string) => {
        switch (status.toUpperCase()) {
            case "PENDING_VALIDATION":
                return <StatusIndicator type="pending">Pending</StatusIndicator>;
            case "VALIDATED_DETECTION":
                return <StatusIndicator type="error">Validated Threat</StatusIndicator>;
            case "FALSE_DETECTION":
                return <StatusIndicator type="success">False Detection</StatusIndicator>;
            case "FAILED_VALIDATION":
                return <StatusIndicator type="warning">Failed</StatusIndicator>;
            case "SKIPPED":
                return <StatusIndicator type="info">Skipped</StatusIndicator>;
            default:
                return <StatusIndicator type="info">{status}</StatusIndicator>;
        }
    };

    const getDetectionTypeIndicator = (type: string) => {
        return type === "ONLINE" ? <Badge color="red">Online</Badge> : <Badge color="blue">Simulated</Badge>;
    };

    const getMatchTypeIndicator = (type: string) => {
        return type === "EXACT" ? (
            <Badge color="green">Exact Match</Badge>
        ) : (
            <Badge color="grey">Similarity Match</Badge>
        );
    };

    const formatDateTime = (dateTimeString: string) => {
        try {
            return new Date(dateTimeString).toLocaleString();
        } catch {
            return dateTimeString;
        }
    };

    const detectionColumnDefinitions = [
        {
            id: "detectionId",
            header: "Detection ID",
            cell: (item: DetectionDetails) => (
                <Box fontSize="body-s">
                    <code>{item.detectionId}</code>
                </Box>
            ),
            width: 200
        },
        {
            id: "detectionType",
            header: "Type",
            cell: (item: DetectionDetails) => getDetectionTypeIndicator(item.detectionType),
            width: 100
        },
        {
            id: "status",
            header: "Status",
            cell: (item: DetectionDetails) => getStatusIndicator(item.status),
            width: 150
        },
        {
            id: "matchType",
            header: "Match Type",
            cell: (item: DetectionDetails) => getMatchTypeIndicator(item.matchType),
            width: 120
        },
        {
            id: "landingPage",
            header: "Landing Page",
            cell: (item: DetectionDetails) => (
                <Link external href={item.landingPage} fontSize="body-s">
                    {item.landingPage.length > 50 ? `${item.landingPage.substring(0, 50)}...` : item.landingPage}
                </Link>
            ),
            width: 250
        },
        {
            id: "creationTime",
            header: "Created",
            cell: (item: DetectionDetails) => (
                <Box fontSize="body-s" color="text-body-secondary">
                    {formatDateTime(item.creationTime)}
                </Box>
            ),
            width: 150
        }
    ];

    if (isLoading) {
        return (
            <Container>
                <SpaceBetween size="l">
                    <BreadcrumbNavigation />
                    <Box textAlign="center" padding="xxl">
                        <Spinner size="large" />
                        <Box variant="p" padding={{top: "s"}}>
                            Loading detection details...
                        </Box>
                    </Box>
                </SpaceBetween>
            </Container>
        );
    }

    return (
        <Container>
            <SpaceBetween size="l">
                <BreadcrumbNavigation />

                <Header
                    variant="h1"
                    description={`Detailed information about signature ${signatureId} and its associated detections`}
                    actions={
                        <Button onClick={() => navigate(-1)} iconName="arrow-left">
                            Back
                        </Button>
                    }
                >
                    Signature Details
                </Header>

                {error && (
                    <Alert type="error" dismissible onDismiss={() => setError(null)}>
                        {error}
                    </Alert>
                )}

                <ColumnLayout columns={2} variant="text-grid">
                    <SpaceBetween size="l">
                        <Container>
                            <Header variant="h2">Signature Information</Header>
                            <KeyValuePairs
                                columns={1}
                                items={[
                                    {
                                        label: "Signature ID",
                                        value: (
                                            <Box fontSize="body-s">
                                                <code>{signatureId}</code>
                                            </Box>
                                        )
                                    },
                                    {
                                        label: "Total Detections",
                                        value: <Badge color="blue">{detections.length} detections</Badge>
                                    },
                                    {
                                        label: "Online Detections",
                                        value: (
                                            <Badge color="red">
                                                {detections.filter(d => d.detectionType === "ONLINE").length}
                                            </Badge>
                                        )
                                    },
                                    {
                                        label: "Simulated Detections",
                                        value: (
                                            <Badge color="blue">
                                                {detections.filter(d => d.detectionType === "SIMULATED").length}
                                            </Badge>
                                        )
                                    }
                                ]}
                            />
                        </Container>
                    </SpaceBetween>

                    <SpaceBetween size="l">
                        <Container>
                            <Header variant="h2">Detection Summary</Header>
                            <KeyValuePairs
                                columns={1}
                                items={[
                                    {
                                        label: "Unsafe Detections",
                                        value: (
                                            <Badge color="red">
                                                {detections.filter(d => d.status === "UNSAFE").length}
                                            </Badge>
                                        )
                                    },
                                    {
                                        label: "Pending Verification",
                                        value: (
                                            <Badge color="grey">
                                                {detections.filter(d => d.status === "PENDING_VERIFICATION").length}
                                            </Badge>
                                        )
                                    },
                                    {
                                        label: "False Positives",
                                        value: (
                                            <Badge color="green">
                                                {detections.filter(d => d.status === "FALSE_POSITIVE").length}
                                            </Badge>
                                        )
                                    },
                                    {
                                        label: "Shadow Detections",
                                        value: (
                                            <Badge color="blue">
                                                {detections.filter(d => d.status === "SHADOW").length}
                                            </Badge>
                                        )
                                    }
                                ]}
                            />
                        </Container>
                    </SpaceBetween>
                </ColumnLayout>

                <Container>
                    <Table
                        columnDefinitions={detectionColumnDefinitions}
                        items={detections}
                        loading={isLoading}
                        loadingText="Loading detections..."
                        empty={
                            <Box textAlign="center" color="inherit">
                                <b>No detections found</b>
                                <Box padding={{bottom: "s"}} variant="p" color="inherit">
                                    No detection data available for this signature.
                                </Box>
                            </Box>
                        }
                        header={
                            <Header
                                counter={`(${detections.length})`}
                                description="Detailed list of all detections associated with this signature"
                            >
                                Detection Details
                            </Header>
                        }
                        variant="full-page"
                        stickyHeader
                    />
                </Container>

                {detections.length > 0 && (
                    <Container>
                        <Header variant="h2">Validation Details</Header>
                        {detections.map((detection, index) => (
                            <Container key={detection.detectionId}>
                                <Header variant="h3">
                                    Detection {index + 1}: {detection.detectionId}
                                </Header>
                                <ColumnLayout columns={2}>
                                    <KeyValuePairs
                                        columns={1}
                                        items={[
                                            {
                                                label: "Eriskay ID",
                                                value: (
                                                    <Box fontSize="body-s">
                                                        <code>{detection.eriskayId}</code>
                                                    </Box>
                                                )
                                            },
                                            {
                                                label: "Render ID",
                                                value: (
                                                    <Box fontSize="body-s">
                                                        <code>{detection.renderId}</code>
                                                    </Box>
                                                )
                                            },
                                            {
                                                label: "Last Updated",
                                                value: formatDateTime(detection.lastUpdatedTime)
                                            }
                                        ]}
                                    />
                                    <KeyValuePairs
                                        columns={1}
                                        items={Object.entries(detection.validationDetails).map(([type, details]) => ({
                                            label: `${type} Validation`,
                                            value: (
                                                <SpaceBetween direction="horizontal" size="xs">
                                                    {getValidationStatusIndicator(details.status)}
                                                    {details.reason && details.reason !== "NONE" && (
                                                        <Box fontSize="body-s" color="text-body-secondary">
                                                            ({details.reason.replace(/_/g, " ").toLowerCase()})
                                                        </Box>
                                                    )}
                                                </SpaceBetween>
                                            )
                                        }))}
                                    />
                                </ColumnLayout>
                                {detection.metadata && (
                                    <Box padding={{top: "s"}}>
                                        <Box variant="strong">Metadata:</Box>
                                        <Box fontSize="body-s" color="text-body-secondary">
                                            {detection.metadata}
                                        </Box>
                                    </Box>
                                )}
                            </Container>
                        ))}
                    </Container>
                )}
            </SpaceBetween>
        </Container>
    );
};

export default SignatureDetailsPage;
