import React from "react";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, SpaceBetween, Box, Grid, Icon, Cards} from "@cloudscape-design/components";
import MalwareSlideshow from "../../components/malware-slideshow";
import {useDarkMode} from "../../hooks/useDarkMode";
import BreadcrumbNavigation from "../../components/breadcrumb";

const HomePage: React.FC = () => {
    const [isDarkMode] = useDarkMode();

    const heroStyles = {
        padding: "20px",
        borderRadius: "8px",
        marginBottom: "10px",
        background: isDarkMode ? "#13263e" : "#f2f3f3", // Dark blue for dark mode, light gray for light mode
        borderBottom: `1px solid ${isDarkMode ? "#1f3b5a" : "#eaeded"}`,
        color: isDarkMode ? "#ffffff" : "inherit" // White text for dark mode
    };

    return (
        <Container>
            <SpaceBetween size="l">
                <BreadcrumbNavigation />

                <div style={heroStyles}>
                    <Header variant="h1">Welcome to HumanEx</Header>
                    <Box variant="p" padding={{top: "xs"}} fontSize="heading-m">
                        A centralized platform for security investigation and signature management.
                    </Box>
                </div>

                <Grid
                    gridDefinition={[
                        {colspan: {default: 12, xxs: 12, xs: 12, s: 4, m: 4, l: 4, xl: 4}},
                        {colspan: {default: 12, xxs: 12, xs: 12, s: 8, m: 8, l: 8, xl: 8}}
                    ]}
                >
                    <div>
                        <Cards
                            cardDefinition={{
                                header: item => (
                                    <Header variant="h2">
                                        <SpaceBetween direction="horizontal" size="xs">
                                            <Icon name="status-info" />
                                            About HumanEx
                                        </SpaceBetween>
                                    </Header>
                                ),
                                sections: [
                                    {
                                        id: "description",
                                        content: item => (
                                            <div>
                                                <Box variant="p" color="text-body-secondary">
                                                    Our system combines advanced machine learning algorithms with human
                                                    expertise to detect, analyze, and mitigate security threats in
                                                    advertising.
                                                </Box>
                                                <Box variant="p" color="text-body-secondary">
                                                    HumanEx is a tool for investigating and managing signatures
                                                    enforcing signature lifecycle management.
                                                </Box>
                                            </div>
                                        )
                                    }
                                ]
                            }}
                            cardsPerRow={[{cards: 1}]}
                            items={[{id: "about"}]}
                            trackBy="id"
                        />
                    </div>

                    <div>
                        <Header variant="h2">
                            <SpaceBetween direction="horizontal" size="xs">
                                <Icon name="security" />
                                Malware Threat Insights
                            </SpaceBetween>
                        </Header>
                        <Box padding={{bottom: "xs"}} color="text-body-secondary">
                            Interesting insights about malware techniques and trends
                        </Box>
                        <MalwareSlideshow />
                    </div>
                </Grid>
            </SpaceBetween>
        </Container>
    );
};

export default HomePage;
