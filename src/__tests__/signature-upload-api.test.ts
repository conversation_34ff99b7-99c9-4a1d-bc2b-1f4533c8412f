import {SignatureUploadApi, SignatureInput} from "../api/signature-upload-api";
import {
    SignatureStatus,
    SignatureDecision,
    IntelligenceSource,
    IntelligenceLineage,
    ThreatClassification
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

const Lineage = IntelligenceLineage;
const ThreatType = ThreatClassification;
import {validateCsvContent} from "../api/utils/csv-validation";

jest.mock("../api/index", () => ({
    getHumanEvaluationClient: jest.fn()
}));

describe("SignatureUploadApi", () => {
    let api: SignatureUploadApi;
    let mockClient: any;

    beforeEach(() => {
        api = new SignatureUploadApi();
        mockClient = {
            createSignatures: jest.fn()
        };

        const {getHumanEvaluationClient} = require("../api/index");
        getHumanEvaluationClient.mockReturnValue(mockClient);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("createSignatures", () => {
        const mockSignatureInputs: SignatureInput[] = [
            {
                domain: "example1.com",
                source: IntelligenceSource.SYSTEM_TESTING,
                description: "Test domain 1",
                status: "SHADOW",
                decision: "THREAT",
                lineage: IntelligenceLineage.AUTOMATED_DETECTION,
                threatClassification: ThreatClassification.MALICIOUS_EXECUTABLE
            },
            {
                domain: "example2.com",
                source: IntelligenceSource.SYSTEM_TESTING,
                description: "Test domain 2",
                status: "SHADOW",
                decision: "THREAT",
                lineage: IntelligenceLineage.THREAT_HUNTING,
                threatClassification: ThreatClassification.PHISHING_DOMAIN
            }
        ];

        it("should correctly determine success when backend explicitly sets success=true", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: "24451955-377a-4995-82f0-b033f696350f",
                        createdAt: "2025-06-13T14:38:51.869283083Z",
                        success: true,
                        errorMessage: null,
                        validationResult: {
                            domain: "example1.com",
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: {
                                timesSeen: 1,
                                firstPartyAdvertisersCount: 1,
                                impactedCreatives: {
                                    clicks: 1,
                                    impressions: 1,
                                    safeCreativesCount: 1,
                                    unsafeCreativesCount: 1
                                }
                            }
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([mockSignatureInputs[0]]);

            expect(result.signatureEntries).toHaveLength(1);
            expect(result.signatureEntries[0]).toEqual({
                domain: "example1.com",
                signatureId: "24451955-377a-4995-82f0-b033f696350f",
                createdAt: "2025-06-13T14:38:51.869283083Z",
                success: true,
                errorMessage: undefined,
                validationResult: {
                    domain: "example1.com",
                    status: "APPROVED",
                    reason: undefined,
                    impactAssessment: {
                        timesSeen: 1,
                        firstPartyAdvertisersCount: 1,
                        impactedCreatives: {
                            clicks: 1,
                            impressions: 1,
                            safeCreativesCount: 1,
                            unsafeCreativesCount: 1
                        }
                    }
                }
            });
        });

        it("should determine success=true when validation is approved even if success field is missing", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: "some-id",
                        createdAt: "2025-06-13T14:38:51.869283083Z",
                        errorMessage: null,
                        validationResult: {
                            domain: "example1.com",
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([mockSignatureInputs[0]]);

            expect(result.signatureEntries[0].success).toBe(true);
            expect(result.signatureEntries[0].validationResult.status).toBe("APPROVED");
        });

        it("should determine success=false when validation is rejected", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: null,
                        createdAt: null,
                        success: false,
                        errorMessage: "Validation failed: Domain not suitable for blocking",
                        validationResult: {
                            domain: "example1.com",
                            status: "REJECTED",
                            reason: "Domain not suitable for blocking",
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([mockSignatureInputs[0]]);

            expect(result.signatureEntries[0].success).toBe(false);
            expect(result.signatureEntries[0].validationResult.status).toBe("REJECTED");
            expect(result.signatureEntries[0].errorMessage).toBe("Validation failed: Domain not suitable for blocking");
        });

        it("should handle approved validation but signature creation failure", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: null,
                        createdAt: null,
                        success: false,
                        errorMessage: "Signature creation failed due to system error",
                        validationResult: {
                            domain: "example1.com",
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([mockSignatureInputs[0]]);

            expect(result.signatureEntries[0].success).toBe(false);
            expect(result.signatureEntries[0].validationResult.status).toBe("APPROVED");
            expect(result.signatureEntries[0].errorMessage).toBe("Signature creation failed due to system error");
        });

        it("should handle API errors gracefully", async () => {
            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockRejectedValue(new Error("Service unavailable"))
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures(mockSignatureInputs);

            expect(result.signatureEntries).toHaveLength(2);
            result.signatureEntries.forEach((entry, index) => {
                expect(entry).toEqual({
                    domain: mockSignatureInputs[index].domain,
                    signatureId: undefined,
                    createdAt: undefined,
                    success: false,
                    errorMessage: `[${mockSignatureInputs[index].domain}] Service unavailable`,
                    validationResult: {
                        domain: mockSignatureInputs[index].domain,
                        status: "N/A",
                        reason: `[${mockSignatureInputs[index].domain}] Service unavailable`,
                        impactAssessment: undefined
                    }
                });
            });
        });

        it("should handle validation errors", async () => {
            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockRejectedValue(new Error("Validation failed: Invalid domain"))
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures(mockSignatureInputs);

            expect(result.signatureEntries).toHaveLength(2);
            result.signatureEntries.forEach((entry, index) => {
                expect(entry).toEqual({
                    domain: mockSignatureInputs[index].domain,
                    signatureId: undefined,
                    createdAt: undefined,
                    success: false,
                    errorMessage: `[${mockSignatureInputs[index].domain}] Validation failed: Invalid domain`,
                    validationResult: {
                        domain: mockSignatureInputs[index].domain,
                        status: "REJECTED",
                        reason: `[${mockSignatureInputs[index].domain}] Validation failed: Invalid domain`,
                        impactAssessment: undefined
                    }
                });
            });
        });

        it("should handle mixed results correctly", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: "success-id",
                        createdAt: "2025-06-13T14:38:51.869283083Z",
                        success: true,
                        errorMessage: null,
                        validationResult: {
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: null
                        }
                    },
                    {
                        domain: "example2.com",
                        signatureId: null,
                        createdAt: null,
                        success: false,
                        errorMessage: "Validation failed",
                        validationResult: {
                            status: "REJECTED",
                            reason: "Domain not suitable",
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures(mockSignatureInputs);

            expect(result.signatureEntries).toHaveLength(2);
            expect(result.signatureEntries[0].success).toBe(true);
            expect(result.signatureEntries[0].validationResult.status).toBe("APPROVED");
            expect(result.signatureEntries[1].success).toBe(false);
            expect(result.signatureEntries[1].validationResult.status).toBe("REJECTED");
        });

        it("should set status to N/A when validation result is missing or incomplete", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: null,
                        createdAt: null,
                        success: false,
                        errorMessage: "Request failed before validation"
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([mockSignatureInputs[0]]);

            expect(result.signatureEntries[0].success).toBe(false);
            expect(result.signatureEntries[0].validationResult.status).toBe("N/A");
            expect(result.signatureEntries[0].errorMessage).toBe("Request failed before validation");
        });

        it("should set status to N/A when validation result status is missing", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example1.com",
                        signatureId: null,
                        createdAt: null,
                        success: false,
                        errorMessage: "Validation service unavailable",
                        validationResult: {
                            domain: "example1.com",
                            reason: "Service unavailable",
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([mockSignatureInputs[0]]);

            expect(result.signatureEntries[0].success).toBe(false);
            expect(result.signatureEntries[0].validationResult.status).toBe("N/A");
            expect(result.signatureEntries[0].errorMessage).toBe("Validation service unavailable");
        });
    });

    describe("validateCsvContent", () => {
        it("should validate correct CSV content", () => {
            const csvContent =
                "Domain,Source,Description,Status,Decision\nexample.com,SYSTEM_TESTING,Test domain,shadow,threat";

            const result = validateCsvContent(csvContent);

            expect(result.valid).toBe(true);
            expect(result.signatures).toHaveLength(1);
            expect(result.errors).toHaveLength(0);
        });

        it("should reject CSV with invalid headers", () => {
            const csvContent = "Domain,Source,Description\nexample.com,SYSTEM_TESTING,Test domain";

            const result = validateCsvContent(csvContent);

            expect(result.valid).toBe(false);
            expect(result.errors[0].message).toContain("Invalid CSV headers");
        });

        it("should reject CSV with invalid status values", () => {
            const csvContent =
                "Domain,Source,Description,Status,Decision\nexample.com,SYSTEM_TESTING,Test domain,invalid_status,threat";

            const result = validateCsvContent(csvContent);

            expect(result.valid).toBe(false);
            expect(result.errors[0].message).toContain("Invalid SignatureStatus value");
        });
    });

    describe("User reported issue - exact API response", () => {
        it("should show success for the exact API response provided by user", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        "createdAt": "2025-06-13T15:20:39.423575941Z",
                        "domain": "example.com",
                        "errorMessage": null,
                        "signatureId": "a97e9b85-23ae-4ac2-a24c-7c222f9c8c7a",
                        "success": true,
                        "validationResult": {
                            "domain": "example.com",
                            "impactAssessment": {
                                "firstPartyAdvertisersCount": 1,
                                "impactedCreatives": {
                                    "clicks": 1,
                                    "impressions": 1,
                                    "safeCreativesCount": 1,
                                    "unsafeCreativesCount": 1
                                },
                                "timesSeen": 1
                            },
                            "reason": undefined,
                            "status": "APPROVED"
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const result = await api.createSignatures([
                {
                    domain: "example.com",
                    source: IntelligenceSource.SYSTEM_TESTING,
                    description: "Test domain",
                    status: "shadow",
                    decision: "threat",
                    lineage: undefined,
                    threatClassification: undefined
                }
            ]);

            expect(result.signatureEntries).toHaveLength(1);
            const entry = result.signatureEntries[0];

            expect(entry.success).toBe(true);
            expect(entry.validationResult.status).toBe("APPROVED");
            expect(entry.signatureId).toBe("a97e9b85-23ae-4ac2-a24c-7c222f9c8c7a");
            expect(entry.errorMessage).toBe(undefined);
        });
    });

    describe("Lineage and Threat Classification Fields", () => {
        it("should handle signatures with lineage and threat classification fields", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example.com",
                        signatureId: "test-id-123",
                        createdAt: "2025-06-13T14:38:51.869283083Z",
                        success: true,
                        errorMessage: null,
                        validationResult: {
                            domain: "example.com",
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const inputWithNewFields = {
                domain: "example.com",
                source: IntelligenceSource.SYSTEM_TESTING,
                description: "Test domain with new fields",
                status: "SHADOW",
                decision: "THREAT",
                lineage: IntelligenceLineage.AUTOMATED_DETECTION,
                threatClassification: ThreatClassification.MALICIOUS_EXECUTABLE
            };

            const result = await api.createSignatures([inputWithNewFields]);

            expect(result.signatureEntries).toHaveLength(1);
            expect(result.signatureEntries[0].success).toBe(true);
            expect(result.signatureEntries[0].domain).toBe("example.com");

            expect(mockClient.createSignatures).toHaveBeenCalledWith({
                signatures: [
                    {
                        domain: "example.com",
                        source: "SYSTEM_TESTING",
                        description: "Test domain with new fields",
                        status: "SHADOW",
                        decision: "THREAT",
                        lineage: IntelligenceLineage.AUTOMATED_DETECTION,
                        threatClassification: ThreatClassification.MALICIOUS_EXECUTABLE
                    }
                ]
            });
        });

        it("should handle signatures without optional lineage and threat classification fields", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example.com",
                        signatureId: "test-id-456",
                        createdAt: "2025-06-13T14:38:51.869283083Z",
                        success: true,
                        errorMessage: null,
                        validationResult: {
                            domain: "example.com",
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const inputWithoutNewFields = {
                domain: "example.com",
                source: IntelligenceSource.SYSTEM_TESTING,
                description: "Test domain without new fields",
                status: "SHADOW",
                decision: "THREAT",
                lineage: undefined,
                threatClassification: undefined
            };

            const result = await api.createSignatures([inputWithoutNewFields]);

            expect(result.signatureEntries).toHaveLength(1);
            expect(result.signatureEntries[0].success).toBe(true);

            expect(mockClient.createSignatures).toHaveBeenCalledWith({
                signatures: [
                    {
                        domain: "example.com",
                        source: "SYSTEM_TESTING",
                        description: "Test domain without new fields",
                        status: "SHADOW",
                        decision: "THREAT",
                        lineage: undefined,
                        threatClassification: undefined
                    }
                ]
            });
        });

        it("should handle undefined values for optional fields", async () => {
            const mockResponse = {
                createdSignatures: [
                    {
                        domain: "example.com",
                        signatureId: "test-id-789",
                        createdAt: "2025-06-13T14:38:51.869283083Z",
                        success: true,
                        errorMessage: null,
                        validationResult: {
                            domain: "example.com",
                            status: "APPROVED",
                            reason: undefined,
                            impactAssessment: null
                        }
                    }
                ]
            };

            const mockObservable = {
                pipe: jest.fn().mockReturnThis(),
                toPromise: jest.fn().mockResolvedValue(mockResponse)
            };
            mockClient.createSignatures.mockReturnValue(mockObservable);

            const inputWithUndefinedFields = {
                domain: "example.com",
                source: IntelligenceSource.SYSTEM_TESTING,
                description: "Test domain with undefined optional fields",
                status: "SHADOW",
                decision: "THREAT",
                lineage: undefined,
                threatClassification: undefined
            };

            const result = await api.createSignatures([inputWithUndefinedFields]);

            expect(result.signatureEntries).toHaveLength(1);
            expect(result.signatureEntries[0].success).toBe(true);

            expect(mockClient.createSignatures).toHaveBeenCalledWith({
                signatures: [
                    {
                        domain: "example.com",
                        source: "SYSTEM_TESTING",
                        description: "Test domain with undefined optional fields",
                        status: "SHADOW",
                        decision: "THREAT",
                        lineage: undefined,
                        threatClassification: undefined
                    }
                ]
            });
        });
    });
});
