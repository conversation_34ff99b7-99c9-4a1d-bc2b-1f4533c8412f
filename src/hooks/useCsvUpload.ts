import {useState, useCallback} from "react";
import {SignatureInput, SignatureUploadApi} from "../api/signature-upload-api";

export interface CsvUploadState {
    file: File | null;
    error: string | null;
    isValidating: boolean;
    validationResult: {
        valid: boolean;
        signatures?: SignatureInput[];
        errors?: string[];
    } | null;
}

export interface CsvUploadActions {
    handleFileUpload: (event: {detail: {value: File[]}}) => void;
    validateFile: () => Promise<void>;
    clearFile: () => void;
    downloadTemplate: () => void;
}

export const useCsvUpload = (api: SignatureUploadApi): [CsvUploadState, CsvUploadActions] => {
    const [state, setState] = useState<CsvUploadState>({
        file: null,
        error: null,
        isValidating: false,
        validationResult: null
    });

    const handleFileUpload = useCallback((event: {detail: {value: File[]}}) => {
        const files = event.detail.value;
        const file = files.length > 0 ? files[0] : null;

        setState(prev => ({
            ...prev,
            file,
            error: null,
            validationResult: null
        }));
    }, []);

    const validateFile = useCallback(async () => {
        if (!state.file) {
            setState(prev => ({...prev, error: "No file selected"}));
            return;
        }

        setState(prev => ({...prev, isValidating: true, error: null}));

        try {
            const result = await api.processCsvFile(state.file);
            setState(prev => ({
                ...prev,
                isValidating: false,
                validationResult: result,
                error: result.valid ? null : result.errors?.[0] || "Validation failed"
            }));
        } catch (error) {
            setState(prev => ({
                ...prev,
                isValidating: false,
                error: "Failed to process CSV file",
                validationResult: null
            }));
        }
    }, [state.file, api]);

    const clearFile = useCallback(() => {
        setState({
            file: null,
            error: null,
            isValidating: false,
            validationResult: null
        });
    }, []);

    const downloadTemplate = useCallback(() => {
        const csvContent =
            "Domain,Source,Description,Status,Decision\nexample.com,SYSTEM_TESTING,Test domain,SHADOW,THREAT";
        const blob = new Blob([csvContent], {type: "text/csv"});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "signature_template.csv";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }, []);

    return [
        state,
        {
            handleFileUpload,
            validateFile,
            clearFile,
            downloadTemplate
        }
    ];
};
