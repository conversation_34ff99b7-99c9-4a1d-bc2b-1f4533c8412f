import {useEffect} from "react";
import {applyMode, Mode} from "@cloudscape-design/global-styles";
import {useThemeStore} from "../store/themeStore";

export const useDarkMode = (): [boolean, () => void] => {
    const {isDarkMode, toggleTheme} = useThemeStore();

    useEffect(() => {
        applyMode(isDarkMode ? Mode.Dark : Mode.Light);
    }, [isDarkMode]);

    return [isDarkMode, toggleTheme];
};
