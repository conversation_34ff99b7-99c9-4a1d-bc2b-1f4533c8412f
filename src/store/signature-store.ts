import { create } from 'zustand';
import { SignatureListItem } from '../api/signature-search-api';

interface SourceCategory {
    source: string;
    count: number;
    isLoading: boolean;
}

interface SignatureStore {
    signatures: SignatureListItem[];
    allSignatures: SignatureListItem[];
    isLoading: boolean;
    selectedSource: string;
    currentPageIndex: number;
    error: string | null;
    sourceCategories: SourceCategory[];
    
    setSignatures: (signatures: SignatureListItem[]) => void;
    setAllSignatures: (signatures: SignatureListItem[]) => void;
    setIsLoading: (loading: boolean) => void;
    setSelectedSource: (source: string) => void;
    setCurrentPageIndex: (index: number) => void;
    setError: (error: string | null) => void;
    setSourceCategories: (categories: SourceCategory[]) => void;
    filterBySource: (source: string) => void;
    loadMockData: () => void;
}

const mockData: SignatureListItem[] = [{
    signatureId: "abc12345-def6-7890-ghij-klmnopqrstuv",
    content: "example.malicious-domain.com",
    signatureDecisionType: "THREAT",
    source: "VOILA",
    creator: "<EMAIL>",
    creationTime: "2024-01-15T10:30:00Z",
    lineage: "automated-detection",
    threatClassification: "MALWARE",
    description: "Suspicious domain hosting malware content",
    status: "LIVE_REFERRAL",
    auditor: "system"
}];

export const useSignatureStore = create<SignatureStore>((set, get) => ({
    signatures: [],
    allSignatures: [],
    isLoading: false,
    selectedSource: "ALL",
    currentPageIndex: 1,
    error: null,
    sourceCategories: [],
    
    setSignatures: (signatures) => set({ signatures }),
    setAllSignatures: (signatures) => set({ allSignatures: signatures }),
    setIsLoading: (loading) => set({ isLoading: loading }),
    setSelectedSource: (source) => set({ selectedSource: source }),
    setCurrentPageIndex: (index) => set({ currentPageIndex: index }),
    setError: (error) => set({ error }),
    setSourceCategories: (categories) => set({ sourceCategories: categories }),
    
    filterBySource: (source) => {
        const { allSignatures } = get();
        set({ selectedSource: source, currentPageIndex: 1 });
        
        if (source === "ALL") {
            set({ signatures: allSignatures });
        } else {
            const filtered = allSignatures.filter(sig => sig.source === source);
            set({ signatures: filtered });
        }
        
        localStorage.setItem("searchSignatures_selectedSource", source);
    },
    
    loadMockData: () => {
        set({ isLoading: true });
        
        const sourceMap = new Map<string, number>();
        mockData.forEach(item => {
            const source = item.source || "UNKNOWN";
            sourceMap.set(source, (sourceMap.get(source) || 0) + 1);
        });

        const categories: SourceCategory[] = [
            { source: "ALL", count: mockData.length, isLoading: false },
            ...Array.from(sourceMap.entries())
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([source, count]) => ({ source, count, isLoading: false }))
        ];

        set({
            allSignatures: mockData,
            signatures: mockData,
            sourceCategories: categories,
            isLoading: false
        });
    }
}));