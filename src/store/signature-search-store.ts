import {create} from "zustand";
import {
    SignatureListItem,
    SignatureStatus,
    SignatureSearchApi,
    SearchSignaturesResponse
} from "../api/signature-search-api";

interface SignatureSearchState {
    signatures: SignatureListItem[];
    loading: boolean;
    error: string | null;
    currentStatus: SignatureStatus;
    nextToken?: string;
    totalCount: number;
    hasMore: boolean;

    // Actions
    searchSignatures: (status: SignatureStatus, reset?: boolean) => Promise<void>;
    loadMore: () => Promise<void>;
    setStatus: (status: SignatureStatus) => void;
    clearError: () => void;
}

const signatureSearchApi = new SignatureSearchApi();

export const useSignatureSearchStore = create<SignatureSearchState>((set, get) => ({
    signatures: [],
    loading: false,
    error: null,
    currentStatus: SignatureStatus.PENDING_MANUAL_REVIEW,
    nextToken: undefined,
    totalCount: 0,
    hasMore: false,

    searchSignatures: async (status: SignatureStatus, reset = true) => {
        const state = get();

        if (state.loading) return;

        set({
            loading: true,
            error: null,
            ...(reset && {signatures: [], nextToken: undefined})
        });

        try {
            const response: SearchSignaturesResponse = await signatureSearchApi.searchSignatures({
                status,
                maxResults: 50,
                nextToken: reset ? undefined : state.nextToken
            });

            set(state => ({
                signatures: reset ? response.items : [...state.signatures, ...response.items],
                nextToken: response.nextToken,
                totalCount: response.totalCount,
                hasMore: !!response.nextToken,
                currentStatus: status,
                loading: false
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : "Failed to search signatures",
                loading: false
            });
        }
    },

    loadMore: async () => {
        const state = get();
        if (!state.hasMore || state.loading) return;

        await state.searchSignatures(state.currentStatus, false);
    },

    setStatus: (status: SignatureStatus) => {
        set({currentStatus: status});
    },

    clearError: () => {
        set({error: null});
    }
}));
