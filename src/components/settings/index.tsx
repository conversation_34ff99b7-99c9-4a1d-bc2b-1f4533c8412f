import React, {useState, useEffect} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>Bet<PERSON><PERSON>, <PERSON><PERSON>, Toggle, Container} from "@cloudscape-design/components";
import {useDarkMode} from "../../hooks/useDarkMode";
import {getIdentity} from "../../api";
import {Identity} from "../../api/model";
import Loading from "../loading";

const styles = {
    profileContainer: (isDarkMode: boolean) => ({
        display: "flex",
        alignItems: "center",
        padding: "16px",
        background: isDarkMode
            ? "linear-gradient(to right, #192534, #0f1b2a)"
            : "linear-gradient(to right, #f2f8fd, #ffffff)",
        borderRadius: "8px",
        border: `1px solid ${isDarkMode ? "#2e3c49" : "#eaeded"}`
    }),
    profileImage: (isDarkMode: boolean) => ({
        width: "56px",
        height: "56px",
        borderRadius: "50%",
        border: `3px solid ${isDarkMode ? "#539fe5" : "#0972d3"}`,
        boxShadow: isDarkMode ? "0 4px 8px rgba(0,0,0,0.3)" : "0 4px 8px rgba(0,0,0,0.15)",
        marginRight: "20px",
        objectFit: "cover" as const
    }),
    avatarPlaceholder: (isDarkMode: boolean) => ({
        width: "56px",
        height: "56px",
        borderRadius: "50%",
        background: isDarkMode
            ? "linear-gradient(135deg, #539fe5, #0972d3)"
            : "linear-gradient(135deg, #0972d3, #2ea2f8)",
        color: "white",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontWeight: "bold",
        fontSize: "24px",
        boxShadow: isDarkMode ? "0 4px 8px rgba(0,0,0,0.3)" : "0 4px 8px rgba(0,0,0,0.15)",
        marginRight: "20px"
    })
};

const Settings: React.FC = () => {
    const [visible, setVisible] = useState(false);
    const [isDarkMode, toggleTheme] = useDarkMode();
    const [identity, setIdentity] = useState<Identity | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        if (visible) {
            setLoading(true);
            setError(null);
            getIdentity()
                .then(identityData => {
                    setIdentity(identityData);
                })
                .catch(err => {
                    setError(err);
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    }, [visible]);

    const renderUserAvatar = () => {
        if (!identity) return null;

        const username = identity.username;

        return identity.icon ? (
            <img src={identity.icon} alt="User" style={styles.profileImage(isDarkMode)} />
        ) : (
            <div style={styles.avatarPlaceholder(isDarkMode)}>{username.charAt(0).toUpperCase()}</div>
        );
    };

    return (
        <>
            <Button iconName="settings" variant="link" ariaLabel="Settings" onClick={() => setVisible(true)}>
                Settings
            </Button>

            <Modal
                visible={visible}
                onDismiss={() => setVisible(false)}
                header={<Header variant="h2">Settings</Header>}
                size="medium"
                footer={
                    <Box float="right">
                        <Button variant="primary" onClick={() => setVisible(false)}>
                            Close
                        </Button>
                    </Box>
                }
            >
                <SpaceBetween size="l">
                    <Container header={<Header variant="h3">Appearance</Header>}>
                        <SpaceBetween size="m">
                            <Toggle checked={isDarkMode} onChange={toggleTheme}>
                                Dark Mode
                            </Toggle>
                        </SpaceBetween>
                    </Container>

                    <Container header={<Header variant="h3">User Profile</Header>}>
                        <SpaceBetween size="m">
                            {loading ? (
                                <Loading message="Loading user profile..." />
                            ) : error ? (
                                <Box color="text-status-error" fontSize="body-s">
                                    Could not load profile information
                                </Box>
                            ) : (
                                identity && (
                                    <div style={styles.profileContainer(isDarkMode)}>
                                        {renderUserAvatar()}
                                        <div>
                                            <Box variant="h3" padding="n" color="text-label" fontWeight="bold">
                                                {identity.username}
                                            </Box>
                                            <Box
                                                variant="p"
                                                padding="n"
                                                color="text-body-secondary"
                                                fontSize="body-s"
                                                margin={{top: "xs"}}
                                            >
                                                Authenticated with Amazon Cognito
                                            </Box>
                                        </div>
                                    </div>
                                )
                            )}
                        </SpaceBetween>
                    </Container>
                </SpaceBetween>
            </Modal>
        </>
    );
};

export default Settings;
