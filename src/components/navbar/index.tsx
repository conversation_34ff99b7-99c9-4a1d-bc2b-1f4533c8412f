import React, {useState} from "react";
import {useLocation} from "react-router-dom";
import {SideNavigation, AppLayout, SideNavigationProps, Box, Button} from "@cloudscape-design/components";
import Logo from "../logo";
import Settings from "../settings";

interface NavbarProps {
    children?: React.ReactNode;
}

const Navbar: React.FC<NavbarProps> = ({children}) => {
    const location = useLocation();

    const [navigationOpen, setNavigationOpen] = useState(true);

    const navigationItems: SideNavigationProps.Item[] = [
        {
            type: "link",
            text: "Home",
            href: "/"
        },
        {
            type: "link",
            text: "Upload Signature",
            href: "/upload-signature"
        },
        {
            type: "link",
            text: "Referred Signature",
            href: "/referred-signatures"
        },
        {
            type: "link",
            text: (
                <Box color="text-status-inactive">
                    Audit Log <i>(Coming Soon)</i>
                </Box>
            ) as unknown as string,
            href: "#"
        },
        {
            type: "link",
            text: "Creative Browser",
            href: "/creative-browser"
        }
    ];

    const toggleSidebar = () => {
        setNavigationOpen(!navigationOpen);
    };

    return (
        <AppLayout
            navigation={
                <div style={{display: "flex", flexDirection: "column", height: "100%"}}>
                    <div style={{flex: "1"}}>
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                padding: "8px 16px 8px 16px",
                                borderBottom: "1px solid #eaeded"
                            }}
                        >
                            <div
                                style={{
                                    display: "flex",
                                    alignItems: "center",
                                    flex: 1,
                                    margin: "-15px 0",
                                    lineHeight: 0.8
                                }}
                            >
                                <Logo size="large" />
                            </div>
                            <Button
                                iconName={navigationOpen ? "angle-left" : "menu"}
                                variant="icon"
                                ariaLabel={navigationOpen ? "Collapse sidebar" : "Expand sidebar"}
                                onClick={toggleSidebar}
                            />
                        </div>
                        <SideNavigation
                            activeHref={location.pathname}
                            onFollow={event => {
                                if (!event.detail.external) {
                                    event.preventDefault();
                                    if (event.detail.href === "#") {
                                        return;
                                    } else {
                                        window.location.href = `#${event.detail.href}`;
                                        window.location.reload();
                                    }
                                }
                            }}
                            items={navigationItems}
                        />
                    </div>
                    <div
                        style={{
                            padding: "12px",
                            borderTop: "1px solid #eaeded"
                        }}
                    >
                        <Settings />
                    </div>
                </div>
            }
            content={<div id="main-content">{children}</div>}
            toolsHide
            navigationOpen={navigationOpen}
            navigationWidth={280}
            onNavigationChange={({detail}) => setNavigationOpen(detail.open)}
        />
    );
};

export default Navbar;
