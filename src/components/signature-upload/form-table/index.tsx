import React, {useMemo} from "react";
import {<PERSON><PERSON>et<PERSON>en, <PERSON>, <PERSON><PERSON>, <PERSON>, Header, Alert} from "@cloudscape-design/components";
import {SignatureInput} from "../../../api/signature-upload-api";
import {SignatureStatus, SignatureDecision} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {useSignatureFormRowCells} from "../form-row";

interface FormTableProps {
    formRows: SignatureInput[];
    uploadMode: string;
    touchedFields: {[key: string]: boolean};
    fieldErrors: {[key: string]: boolean};
    isLoading: boolean;
    handleAddRow: () => void;
    handleRemoveRow: (index: number) => void;
    handleFormChange: (index: number, field: keyof SignatureInput, value: string) => void;
    handleFieldBlur: (index: number, field: keyof SignatureInput) => void;
    handleFormSubmit: () => void;
    handleReset: () => void;
    formErrors?: boolean; // Optional prop to track form validation errors
    duplicateDomainError?: string | null; // Optional prop to display duplicate domain error
    hasUpdatePermission?: boolean;
}

const FormTable: React.FC<FormTableProps> = ({
    formRows,
    uploadMode,
    touchedFields,
    fieldErrors,
    isLoading,
    handleAddRow,
    handleRemoveRow,
    handleFormChange,
    handleFieldBlur,
    handleFormSubmit,
    handleReset,
    formErrors,
    duplicateDomainError,
    hasUpdatePermission = true
}) => {
    const tableStyle = useMemo(
        () => ({
            width: "100%"
        }),
        []
    );

    const statusOptions = useMemo(
        () => [
            {label: "SHADOW", value: SignatureStatus.SHADOW},
            {label: "LIVE", value: SignatureStatus.LIVE}
        ],
        []
    );

    const decisionOptions = useMemo(
        () => [
            {label: "THREAT", value: SignatureDecision.THREAT},
            {label: "SAFETY", value: SignatureDecision.SAFETY}
        ],
        []
    );

    const {domainCell, sourceCell, descriptionCell, statusCell, decisionCell, lineageCell, threatClassificationCell} =
        useSignatureFormRowCells({
            formRows,
            touchedFields,
            fieldErrors,
            statusOptions,
            decisionOptions,
            handleFormChange,
            handleFieldBlur,
            handleRemoveRow
        });

    const columnDefinitions = useMemo(
        () => [
            {
                id: "domain",
                header: "Domain",
                sortingField: "domain",
                cell: domainCell,
                isRowHeader: true
            },
            {
                id: "source",
                header: "Source",
                sortingField: "source",
                cell: sourceCell
            },
            {
                id: "description",
                header: "Description",
                cell: descriptionCell
            },
            {
                id: "status",
                header: "Status",
                sortingField: "status",
                cell: statusCell
            },
            {
                id: "decision",
                header: "Decision",
                sortingField: "decision",
                cell: decisionCell
            },
            {
                id: "lineage",
                header: "Lineage",
                cell: lineageCell
            },
            {
                id: "threatClassification",
                header: "Threat Type",
                cell: threatClassificationCell
            },
            {
                id: "actions",
                header: "Actions",
                width: 60,
                minWidth: 60,
                cell: (item: SignatureInput) => {
                    const index = formRows.indexOf(item);
                    return (
                        <Button
                            iconName="remove"
                            variant="icon"
                            disabled={formRows.length <= 1}
                            onClick={() => handleRemoveRow(index)}
                            ariaLabel={`Remove row ${index + 1}`}
                        />
                    );
                }
            }
        ],
        [
            domainCell,
            sourceCell,
            descriptionCell,
            statusCell,
            decisionCell,
            lineageCell,
            threatClassificationCell,
            formRows,
            handleRemoveRow
        ]
    );

    return (
        <SpaceBetween size="l">
            <div style={tableStyle}>
                <Table
                    variant="full-page"
                    stripedRows
                    stickyHeader
                    wrapLines={false}
                    resizableColumns
                    columnDefinitions={columnDefinitions}
                    items={formRows}
                    loadingText="Loading signatures..."
                    empty={
                        <Box textAlign="center" color="inherit">
                            <SpaceBetween size="m">
                                <b>No signature entries</b>
                                <Box variant="p" color="inherit">
                                    Click &quot;Add row&quot; to create your first signature entry.
                                </Box>
                            </SpaceBetween>
                        </Box>
                    }
                    header={
                        <Header
                            variant="h2"
                            actions={
                                <Button onClick={handleAddRow} iconName="add-plus" variant="primary">
                                    Add row
                                </Button>
                            }
                        >
                            Signature Upload
                        </Header>
                    }
                />
            </div>

            {duplicateDomainError && <Alert type="warning">{duplicateDomainError}</Alert>}

            {!hasUpdatePermission && (
                <Alert type="info" dismissible={false}>
                    Upload disabled - You don&apos;t have permission to create signatures
                </Alert>
            )}

            <SpaceBetween direction="horizontal" size="xs">
                <Button
                    variant="primary"
                    loading={isLoading}
                    disabled={formRows.length === 0 || formErrors === true || !hasUpdatePermission}
                    onClick={handleFormSubmit}
                >
                    Upload
                </Button>
                <Button onClick={handleReset}>Clear All</Button>
            </SpaceBetween>
        </SpaceBetween>
    );
};

export default FormTable;
