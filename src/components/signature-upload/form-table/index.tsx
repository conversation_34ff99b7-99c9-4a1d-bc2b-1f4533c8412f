import React from "react";
import {<PERSON>Between, Table, <PERSON><PERSON>, FormField, RadioGroup, Box, Head<PERSON>, Alert} from "@cloudscape-design/components";
import {SignatureInput, Status, Decision} from "../../../api/signature-upload-api";
import {useSignatureFormRowCells} from "../form-row";

interface FormTableProps {
    formRows: SignatureInput[];
    uploadMode: string;
    touchedFields: {[key: string]: boolean};
    fieldErrors: {[key: string]: boolean};
    isLoading: boolean;
    handleAddRow: () => void;
    handleRemoveRow: (index: number) => void;
    handleFormChange: (index: number, field: keyof SignatureInput, value: string) => void;
    handleFieldBlur: (index: number, field: keyof SignatureInput) => void;
    setUploadMode: (mode: string) => void;
    handleFormSubmit: () => void;
    handleReset: () => void;
    formErrors?: boolean; // Optional prop to track form validation errors
    duplicateDomainError?: string | null; // Optional prop to display duplicate domain error
}

const FormTable: React.FC<FormTableProps> = ({
    formRows,
    uploadMode,
    touchedFields,
    fieldErrors,
    isLoading,
    handleAddRow,
    handleRemoveRow,
    handleFormChange,
    handleFieldBlur,
    setUploadMode,
    handleFormSubmit,
    handleReset,
    formErrors,
    duplicateDomainError
}) => {
    const tableStyle = {
        tableLayout: "fixed" as const,
        width: "100%"
    };

    const selectStyle = {
        width: "100%",
        minWidth: "120px"
    };

    const statusOptions = [
        {label: "SHADOW", value: Status.SHADOW},
        {label: "LIVE", value: Status.LIVE}
    ];

    const decisionOptions = [
        {label: "THREAT", value: Decision.THREAT},
        {label: "SAFETY", value: Decision.SAFETY}
    ];

    const {domainCell, sourceCell, descriptionCell, statusCell, decisionCell, actionsCell} = useSignatureFormRowCells({
        formRows,
        touchedFields,
        fieldErrors,
        statusOptions,
        decisionOptions,
        selectStyle,
        handleFormChange,
        handleFieldBlur,
        handleRemoveRow
    });

    return (
        <SpaceBetween size="l">
            <div style={tableStyle}>
                <Table
                    variant="embedded"
                    stripedRows
                    stickyHeader
                    wrapLines={false}
                    resizableColumns={false}
                    columnDefinitions={[
                        {
                            id: "domain",
                            header: "Domain",
                            width: 200,
                            cell: domainCell
                        },
                        {
                            id: "source",
                            header: "Source",
                            width: 200, // Fixed width for source column
                            cell: sourceCell
                        },
                        {
                            id: "description",
                            header: "Description",
                            width: 200,
                            cell: descriptionCell
                        },
                        {
                            id: "status",
                            header: "Status",
                            width: 120,
                            cell: statusCell
                        },
                        {
                            id: "decision",
                            header: "Decision",
                            width: 120,
                            cell: decisionCell
                        },
                        {
                            id: "actions",
                            header: "Action",
                            width: 80,
                            cell: actionsCell
                        }
                    ]}
                    items={formRows}
                    loadingText="Loading signatures"
                    empty={
                        <Box textAlign="center" color="inherit">
                            <b>No signatures</b>
                            <Box padding={{bottom: "s"}} variant="p" color="inherit">
                                Add a new signature to get started.
                            </Box>
                        </Box>
                    }
                    header={<Header actions={<Button onClick={handleAddRow}>Add row</Button>}>Signature Upload</Header>}
                />
            </div>

            {duplicateDomainError && <Alert type="warning">{duplicateDomainError}</Alert>}

            <FormField label="Upload Mode">
                <RadioGroup
                    items={[
                        {value: "add", label: "Add New Signatures"},
                        {
                            value: "update",
                            label: "Update Existing Signatures",
                            disabled: true
                        }
                    ]}
                    value={uploadMode}
                    onChange={event => setUploadMode(event.detail.value)}
                />
            </FormField>

            <SpaceBetween direction="horizontal" size="xs">
                <Button
                    variant="primary"
                    loading={isLoading}
                    disabled={formRows.length === 0 || formErrors}
                    onClick={handleFormSubmit}
                >
                    Upload
                </Button>
                <Button onClick={handleReset}>Clear All</Button>
            </SpaceBetween>
        </SpaceBetween>
    );
};

export default FormTable;
