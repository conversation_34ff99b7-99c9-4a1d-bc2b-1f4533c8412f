import React from "react";
import {render, screen, fireEvent} from "@testing-library/react";
import "@testing-library/jest-dom";
import ResultsModal, {ExtendedSignatureEntry} from "../index";
import {ValidationStatus} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

jest.mock("@cloudscape-design/components", () => ({
    Modal: ({children, visible, header, onDismiss}: any) =>
        visible ? (
            <div data-testid="modal" role="dialog">
                <div data-testid="modal-header">{header}</div>
                <div>{children}</div>
                <button onClick={onDismiss} data-testid="modal-dismiss">
                    Close
                </button>
            </div>
        ) : null,
    SpaceBetween: ({children}: any) => <div data-testid="space-between">{children}</div>,
    Alert: ({children, type}: any) => (
        <div data-testid={`alert-${type}`} role="alert">
            {children}
        </div>
    ),
    Button: ({children, onClick}: any) => (
        <button onClick={onClick} data-testid="button">
            {children}
        </button>
    ),
    ExpandableSection: ({children, headerText, expanded, onChange}: any) => (
        <div data-testid="expandable-section">
            <button onClick={() => onChange({detail: {expanded: !expanded}})} data-testid="expandable-header">
                {headerText}
            </button>
            {expanded && <div data-testid="expandable-content">{children}</div>}
        </div>
    ),
    Table: ({items, columnDefinitions}: any) => (
        <table data-testid="table">
            <thead>
                <tr>
                    {columnDefinitions.map((col: any, index: number) => (
                        <th key={index}>{col.header}</th>
                    ))}
                </tr>
            </thead>
            <tbody>
                {items.map((item: any, rowIndex: number) => (
                    <tr key={rowIndex} data-testid={`table-row-${rowIndex}`}>
                        {columnDefinitions.map((col: any, colIndex: number) => (
                            <td key={colIndex} data-testid={`cell-${rowIndex}-${colIndex}`}>
                                {col.cell(item)}
                            </td>
                        ))}
                    </tr>
                ))}
            </tbody>
        </table>
    )
}));

describe("ResultsModal", () => {
    const mockOnDismiss = jest.fn();

    beforeEach(() => {
        mockOnDismiss.mockClear();
    });

    const createMockEntry = (overrides: Partial<ExtendedSignatureEntry> = {}): ExtendedSignatureEntry => ({
        domain: "example.com",
        signatureId: "sig-123",
        createdAt: "2025-06-13T16:22:43.522Z",
        success: true,
        errorMessage: null,
        validationResult: {
            status: ValidationStatus.APPROVED,
            reason: null,
            impactAssessment: {
                timesSeen: 1,
                firstPartyAdvertisersCount: 1,
                impactedCreatives: {
                    clicks: 1,
                    impressions: 1,
                    safeCreativesCount: 1,
                    unsafeCreativesCount: 1
                }
            }
        },
        originalInput: {
            domain: "example.com",
            source: "HUMAN",
            description: "Test signature",
            status: "proposed",
            decision: "threat",
            lineage: "test-lineage",
            threatClassification: "malware"
        },
        ...overrides
    });

    describe("Modal Visibility", () => {
        it("should render when visible is true", () => {
            render(<ResultsModal visible={true} uploadResults={[]} onDismiss={mockOnDismiss} />);

            expect(screen.getByTestId("modal")).toBeInTheDocument();
            expect(screen.getByTestId("modal-header")).toHaveTextContent("Upload Results (0 total)");
        });

        it("should not render when visible is false", () => {
            render(<ResultsModal visible={false} uploadResults={[]} onDismiss={mockOnDismiss} />);

            expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
        });

        it("should call onDismiss when close button is clicked", () => {
            render(<ResultsModal visible={true} uploadResults={[]} onDismiss={mockOnDismiss} />);

            fireEvent.click(screen.getByTestId("modal-dismiss"));
            expect(mockOnDismiss).toHaveBeenCalledTimes(1);
        });
    });

    describe("Success Scenarios", () => {
        it("should show success alert for all approved validations", () => {
            const approvedEntries = [
                createMockEntry({domain: "example1.com"}),
                createMockEntry({domain: "example2.com"})
            ];

            render(<ResultsModal visible={true} uploadResults={approvedEntries} onDismiss={mockOnDismiss} />);

            expect(screen.getByTestId("alert-success")).toBeInTheDocument();
            expect(screen.getByTestId("alert-success")).toHaveTextContent(
                "All domains passed validation successfully! 2 of 2 domains were approved and 2 signatures were created."
            );
        });

        it("should display approved validations in expandable section", () => {
            const approvedEntry = createMockEntry();

            render(<ResultsModal visible={true} uploadResults={[approvedEntry]} onDismiss={mockOnDismiss} />);

            expect(screen.getByText("View Approved Validations (1 domains)")).toBeInTheDocument();
        });
    });

    describe("Failure Scenarios", () => {
        it("should show warning alert for mixed results", () => {
            const mixedResults = [
                createMockEntry({domain: "approved.com"}),
                createMockEntry({
                    domain: "rejected.com",
                    success: false,
                    validationResult: {
                        status: ValidationStatus.REJECTED,
                        reason: "High false positive rate",
                        impactAssessment: null
                    },
                    errorMessage: "Validation failed"
                })
            ];

            render(<ResultsModal visible={true} uploadResults={mixedResults} onDismiss={mockOnDismiss} />);

            expect(screen.getByTestId("alert-warning")).toBeInTheDocument();
            expect(screen.getByTestId("alert-warning")).toHaveTextContent(
                "Upload completed with mixed results. 1 domains passed validation (1 signatures created), and 1 failed validation or processing."
            );
        });

        it("should display error details for failed entries", () => {
            const failedEntry = createMockEntry({
                domain: "failed.com",
                success: false,
                validationResult: {
                    status: ValidationStatus.REJECTED,
                    reason: "High false positive rate",
                    impactAssessment: null
                },
                errorMessage: "Validation failed due to high false positive rate"
            });

            render(<ResultsModal visible={true} uploadResults={[failedEntry]} onDismiss={mockOnDismiss} />);

            expect(screen.getByText("View Error Details (1 failed entries)")).toBeInTheDocument();
        });

        it("should correctly categorize entries by validation status", () => {
            const mixedEntries = [
                createMockEntry({
                    domain: "approved.com",
                    validationResult: {...createMockEntry().validationResult, status: ValidationStatus.APPROVED}
                }),
                createMockEntry({
                    domain: "rejected.com",
                    validationResult: {...createMockEntry().validationResult, status: ValidationStatus.REJECTED},
                    success: false
                }),
                createMockEntry({
                    domain: "na.com",
                    validationResult: {...createMockEntry().validationResult, status: ValidationStatus.N_A},
                    success: false
                })
            ];

            render(<ResultsModal visible={true} uploadResults={mixedEntries} onDismiss={mockOnDismiss} />);

            expect(screen.getByText("View Approved Validations (1 domains)")).toBeInTheDocument();
            expect(screen.getByText("View Error Details (2 failed entries)")).toBeInTheDocument();
        });
    });

    describe("API Error Scenario", () => {
        it("should handle API error correctly", () => {
            const apiErrorEntry = createMockEntry({
                domain: "error.com",
                success: false,
                validationResult: {
                    status: ValidationStatus.N_A,
                    reason: "API call failed",
                    impactAssessment: null
                },
                errorMessage: "API Error: Cannot read properties of undefined (reading map)"
            });

            render(<ResultsModal visible={true} uploadResults={[apiErrorEntry]} onDismiss={mockOnDismiss} />);

            expect(screen.getByText("View Error Details (1 failed entries)")).toBeInTheDocument();

            expect(screen.queryByText("View Approved Validations")).not.toBeInTheDocument();
        });
    });

    describe("Header Text", () => {
        it("should show correct total count in header", () => {
            const entries = [
                createMockEntry({domain: "test1.com"}),
                createMockEntry({domain: "test2.com"}),
                createMockEntry({domain: "test3.com"})
            ];

            render(<ResultsModal visible={true} uploadResults={entries} onDismiss={mockOnDismiss} />);

            expect(screen.getByTestId("modal-header")).toHaveTextContent("Upload Results (3 total)");
        });
    });
});
