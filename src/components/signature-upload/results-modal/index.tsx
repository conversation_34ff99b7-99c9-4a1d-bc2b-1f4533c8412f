import React, {useState} from "react";
import {Mo<PERSON>, SpaceBet<PERSON>en, Alert, Button, ExpandableSection, Table} from "@cloudscape-design/components";
import {SignatureEntry} from "../../../api/signature-upload-api";
import {ValidationStatus} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

export interface OriginalInput {
    domain: string;
    source: string;
    description: string;
    status: string;
    decision: string;
    lineage?: string;
    threatClassification?: string;
}

export interface ExtendedSignatureEntry extends SignatureEntry {
    originalInput?: OriginalInput;
}

interface ResultsModalProps {
    visible: boolean;
    uploadResults: ExtendedSignatureEntry[];
    onDismiss: () => void;
}

const ResultsModal: React.FC<ResultsModalProps> = ({visible, uploadResults, onDismiss}) => {
    const [showErrorDetails, setShowErrorDetails] = useState(false);
    const [showSuccessDetails, setShowSuccessDetails] = useState(false);
    const approvedValidations = uploadResults.filter(entry => {
        return entry.validationResult?.status === ValidationStatus.APPROVED;
    });

    // Count entries with signatures created (only for approved validations)
    const entriesWithSignatures = approvedValidations.filter(entry => entry.signatureId);

    const actualFailures = uploadResults.filter(entry => {
        const isFailure =
            entry.validationResult?.status === ValidationStatus.REJECTED ||
            entry.validationResult?.status === ValidationStatus.N_A ||
            !entry.success;
        return isFailure;
    });

    return (
        <Modal
            visible={visible}
            onDismiss={onDismiss}
            header={`Upload Results (${uploadResults.length} total)`}
            size="large"
        >
            <SpaceBetween size="l">
                {uploadResults.length > 0 && (
                    <Alert type={actualFailures.length > 0 ? "warning" : "success"} dismissible={false}>
                        {actualFailures.length > 0 ? (
                            <span>
                                Upload completed with mixed results. <strong>{approvedValidations.length}</strong>{" "}
                                domains passed validation
                                {entriesWithSignatures.length > 0
                                    ? ` (${entriesWithSignatures.length} signatures created)`
                                    : ""}
                                , and <strong>{actualFailures.length}</strong> failed validation or processing.
                            </span>
                        ) : (
                            <span>
                                All domains passed validation successfully!{" "}
                                <strong>
                                    {approvedValidations.length} of {uploadResults.length}
                                </strong>{" "}
                                domains were approved
                                {entriesWithSignatures.length > 0
                                    ? ` and ${entriesWithSignatures.length} signatures were created`
                                    : ""}
                                .
                            </span>
                        )}
                    </Alert>
                )}

                {/* Approved Validations Section */}
                {approvedValidations.length > 0 && (
                    <ExpandableSection
                        headerText={`View Approved Validations (${approvedValidations.length} domains)`}
                        expanded={showSuccessDetails}
                        onChange={({detail}) => setShowSuccessDetails(detail.expanded)}
                    >
                        <Table
                            columnDefinitions={[
                                {
                                    id: "domain",
                                    header: "Domain",
                                    cell: item => item.domain || item.originalInput?.domain || "N/A"
                                },
                                {
                                    id: "status",
                                    header: "Validation Status",
                                    cell: item => (
                                        <span style={{color: "green", fontWeight: "bold"}}>
                                            {item.validationResult?.status || "N/A"}
                                        </span>
                                    )
                                },
                                {
                                    id: "reason",
                                    header: "Validation Reason",
                                    cell: item => item.validationResult?.reason || "Approved for processing"
                                },
                                {
                                    id: "signatureCreated",
                                    header: "Signature Created",
                                    cell: item => (item.signatureId ? "Yes" : "No")
                                }
                            ]}
                            items={approvedValidations}
                            variant="embedded"
                        />
                    </ExpandableSection>
                )}

                {/* Error Details Section */}
                {actualFailures.length > 0 && (
                    <ExpandableSection
                        headerText={`View Error Details (${actualFailures.length} failed entries)`}
                        expanded={showErrorDetails}
                        onChange={({detail}) => setShowErrorDetails(detail.expanded)}
                    >
                        <Table
                            columnDefinitions={[
                                {
                                    id: "domain",
                                    header: "Domain",
                                    cell: item => item.domain || item.originalInput?.domain || "N/A"
                                },
                                {
                                    id: "validationStatus",
                                    header: "Validation Status",
                                    cell: item => {
                                        const status = item.validationResult?.status || "N/A";
                                        const getStatusColor = () => {
                                            switch (status) {
                                                case ValidationStatus.APPROVED:
                                                    return "green";
                                                case ValidationStatus.REJECTED:
                                                    return "red";
                                                case ValidationStatus.N_A:
                                                    return "gray";
                                                default:
                                                    return "black";
                                            }
                                        };
                                        return (
                                            <span style={{color: getStatusColor(), fontWeight: "bold"}}>{status}</span>
                                        );
                                    }
                                },
                                {
                                    id: "error",
                                    header: "Error Message",
                                    cell: item => item.errorMessage || item.validationResult?.reason || "Unknown error"
                                }
                            ]}
                            items={actualFailures}
                            variant="embedded"
                        />
                    </ExpandableSection>
                )}

                {/* Download Failed Results Button */}
                {actualFailures.length > 0 && (
                    <Button
                        onClick={() => {
                            const headers =
                                "Domain,Source,Description,Status,Decision,Lineage,ThreatClassification,Validation_Status,Error_Message";
                            const rows = actualFailures.map(entry => {
                                const input = entry.originalInput || {
                                    domain: "",
                                    source: "",
                                    description: "",
                                    status: "",
                                    decision: "",
                                    lineage: "",
                                    threatClassification: ""
                                };
                                const domain = input.domain || entry.domain || "";
                                // Use actual enum values with underscores for CSV re-upload compatibility
                                const source = input.source || "";
                                const description = input.description || "";
                                const status = input.status || "";
                                const decision = input.decision || "";
                                const lineage = input.lineage || "";
                                const threatClassification = input.threatClassification || "";
                                const validationStatus = entry.validationResult?.status || "N/A";
                                const errorMessage = entry.errorMessage || entry.validationResult?.reason || "Failed";

                                // Escape quotes in all fields that might contain commas or quotes
                                const escapedDomain = domain.replace(/"/g, '""');
                                const escapedSource = source.replace(/"/g, '""');
                                const escapedDescription = description.replace(/"/g, '""');
                                const escapedStatus = status.replace(/"/g, '""');
                                const escapedDecision = decision.replace(/"/g, '""');
                                const escapedLineage = lineage.replace(/"/g, '""');
                                const escapedThreatClassification = threatClassification.replace(/"/g, '""');
                                const escapedValidationStatus = validationStatus.replace(/"/g, '""');
                                const escapedErrorMessage = errorMessage.replace(/"/g, '""');

                                return `"${escapedDomain}","${escapedSource}","${escapedDescription}","${escapedStatus}","${escapedDecision}","${escapedLineage}","${escapedThreatClassification}","${escapedValidationStatus}","${escapedErrorMessage}"`;
                            });
                            const csvContent = [headers, ...rows].join("\n");

                            const blob = new Blob([csvContent], {type: "text/csv"});
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement("a");
                            link.href = url;
                            link.download = "failed_uploads.csv";
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }}
                    >
                        Download Failed Results
                    </Button>
                )}

                <Button onClick={onDismiss}>Close</Button>
            </SpaceBetween>
        </Modal>
    );
};

export default ResultsModal;
