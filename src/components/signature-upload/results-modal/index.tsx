import React from "react";
import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, But<PERSON>} from "@cloudscape-design/components";
import {SignatureEntry} from "../../../api/signature-upload-api";

export interface OriginalInput {
    domain: string;
    source: string;
    description: string;
    status: string;
    decision: string;
}

export interface ExtendedSignatureEntry extends SignatureEntry {
    originalInput?: OriginalInput;
}

interface ResultsModalProps {
    visible: boolean;
    uploadResults: ExtendedSignatureEntry[];
    onDismiss: () => void;
}

const ResultsModal: React.FC<ResultsModalProps> = ({visible, uploadResults, onDismiss}) => {
    return (
        <Modal visible={visible} onDismiss={onDismiss} header="Upload Results" size="large">
            <Box padding="l">
                <SpaceBetween size="l">
                    <Alert type="warning">This feature is not yet live. Backend integration is pending.</Alert>

                    {uploadResults.length > 0 && (
                        <>
                            <Alert type="error">
                                <SpaceBetween size="xs">
                                    <div>Processing failed for {uploadResults.length} signature entries.</div>
                                    <div>
                                        {uploadResults.filter(entry => entry.success).length} successful,{" "}
                                        {uploadResults.filter(entry => !entry.success).length} failed
                                    </div>
                                </SpaceBetween>
                            </Alert>

                            {uploadResults.filter(entry => !entry.success).length > 0 && (
                                <div style={{marginTop: "20px"}}>
                                    <Button
                                        onClick={() => {
                                            const headers = "Domain,Source,Description,Status,Decision,Error";
                                            const rows = uploadResults
                                                .filter(entry => !entry.success)
                                                .map(entry => {
                                                    const input = entry.originalInput || {
                                                        domain: "",
                                                        source: "",
                                                        description: "",
                                                        status: "",
                                                        decision: ""
                                                    };
                                                    const domain = input.domain || entry.domain;
                                                    return `${domain},${input.source || ""},${
                                                        input.description || ""
                                                    },${input.status || ""},${input.decision || ""},"${
                                                        entry.errorMessage || "Failed"
                                                    }"`;
                                                });
                                            const csvContent = [headers, ...rows].join("\n");

                                            const blob = new Blob([csvContent], {type: "text/csv"});
                                            const url = URL.createObjectURL(blob);
                                            const link = document.createElement("a");
                                            link.href = url;
                                            link.download = "failed_uploads.csv";
                                            document.body.appendChild(link);
                                            link.click();
                                            document.body.removeChild(link);
                                        }}
                                    >
                                        Download Failed Results
                                    </Button>
                                </div>
                            )}
                        </>
                    )}

                    <Button onClick={onDismiss}>Close</Button>
                </SpaceBetween>
            </Box>
        </Modal>
    );
};

export default ResultsModal;
