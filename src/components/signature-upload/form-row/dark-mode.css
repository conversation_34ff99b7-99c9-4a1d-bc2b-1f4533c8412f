/* Dark mode styles for form fields */
.awsui-dark-mode .awsui-input-container input,
.awsui-dark-mode .awsui-select-container .awsui-select-option,
.awsui-dark-mode .awsui-select-container .awsui-select-trigger {
    color: inherit !important;
    background-color: inherit !important;
}

.awsui-dark-mode .awsui-input-container input::placeholder,
.awsui-dark-mode .awsui-select-container .awsui-select-placeholder {
    color: #aaa !important;
}
