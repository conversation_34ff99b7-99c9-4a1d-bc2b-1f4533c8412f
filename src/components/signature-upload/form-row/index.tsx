import React from "react";
import {FormField, Input, Select, But<PERSON>} from "@cloudscape-design/components";
import {SignatureInput, IntelligenceSource} from "../../../api/signature-upload-api";

interface SignatureFormRowHelperProps {
    formRows: SignatureInput[];
    touchedFields: {[key: string]: boolean};
    fieldErrors: {[key: string]: boolean};
    statusOptions: {label: string; value: string}[];
    decisionOptions: {label: string; value: string}[];
    selectStyle: {width: string; minWidth: string};
    handleFormChange: (index: number, field: keyof SignatureInput, value: string) => void;
    handleFieldBlur: (index: number, field: keyof SignatureInput) => void;
    handleRemoveRow: (index: number) => void;
}

export const useSignatureFormRowCells = ({
    formRows,
    touchedFields,
    fieldErrors,
    statusOptions,
    decisionOptions,
    selectStyle,
    handleFormChange,
    handleFieldBlur,
    handleRemoveRow
}: SignatureFormRowHelperProps) => {
    const sourceOptions = Object.entries(IntelligenceSource).map(([key, value]) => ({
        label: key,
        value: value,
        description: getSourceDescription(key)
    }));

    function getSourceDescription(key: string): string {
        switch (key) {
            case "SYSTEM_TESTING":
                return "System testing, e.g. Tigger's Canary";
            case "ADRISK_REVERSE_IP":
                return "Intelligence discovered by performing Reverse-IP lookups";
            case "ADRISK_WEB_THREAT_HUNTING":
                return "Intelligence discovered by hunting for threats online";
            case "ADRISK_SLICENDICE":
                return "Intelligence discovered using SliceNDice algorithm";
            case "ADRISK_ANOMSIM":
                return "Intelligence discovered using AnomSim algorithm";
            case "ADRISK_GALLERY_HUMAN_REVIEW":
                return "Intelligence from manual review during gallery update";
            case "ASA":
                return "UK Advertising Standards Agency";
            case "TAG":
                return "Trustworthy Accountability Group";
            case "GOOGLE":
                return "Google Malvertising Team Reach-Out";
            case "CONFIANT":
                return "Confiant publish blog posts and direct reach-out";
            case "GEOEDGE":
                return "Geoedge direct reach-out";
            case "TMT":
                return "The Media Trust";
            case "ADRISK_TDI":
                return "Threat Hunting using Threat Detection Index";
            case "ADRISK_MINHASH":
                return "Intelligence discovered using MinHash algorithm";
            case "ADRISK_DOM":
                return "Intelligence discovered using DOM modality";
            case "BOLTIVE":
                return "Intelligence acquired from Boltive Intelligence Provider";
            case "HUMAN":
                return "Intelligence acquired from Human Intelligence provider";
            default:
                return "";
        }
    }

    return {
        domainCell: (item: SignatureInput) => {
            const index = formRows.indexOf(item);
            return (
                <FormField stretch controlId={`domain-${index}`}>
                    <Input
                        value={item.domain}
                        onChange={event => handleFormChange(index, "domain", event.detail.value)}
                        onBlur={() => handleFieldBlur(index, "domain")}
                        placeholder="example.com"
                    />
                </FormField>
            );
        },
        sourceCell: (item: SignatureInput) => {
            const index = formRows.indexOf(item);
            return (
                <FormField stretch controlId={`source-${index}`}>
                    <div style={{minWidth: "280px"}}>
                        <Select
                            options={sourceOptions}
                            selectedOption={
                                item.source
                                    ? sourceOptions.find(option => option.value === item.source) || {
                                          label: item.source,
                                          value: item.source
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "source", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "source")}
                            placeholder="Select source"
                            expandToViewport
                        />
                    </div>
                </FormField>
            );
        },
        descriptionCell: (item: SignatureInput) => {
            const index = formRows.indexOf(item);
            return (
                <FormField stretch controlId={`description-${index}`}>
                    <Input
                        value={item.description || ""}
                        onChange={event => handleFormChange(index, "description", event.detail.value)}
                        placeholder="Description (required)"
                    />
                </FormField>
            );
        },
        statusCell: (item: SignatureInput) => {
            const index = formRows.indexOf(item);
            return (
                <FormField stretch controlId={`status-${index}`}>
                    <div style={selectStyle}>
                        <Select
                            options={statusOptions}
                            selectedOption={
                                item.status
                                    ? {
                                          label: String(item.status).toUpperCase(),
                                          value: String(item.status)
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "status", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "status")}
                            placeholder="Select status"
                            expandToViewport
                        />
                    </div>
                </FormField>
            );
        },
        decisionCell: (item: SignatureInput) => {
            const index = formRows.indexOf(item);
            return (
                <FormField stretch controlId={`decision-${index}`}>
                    <div style={selectStyle}>
                        <Select
                            options={decisionOptions}
                            selectedOption={
                                item.decision
                                    ? {
                                          label: String(item.decision).toUpperCase(),
                                          value: String(item.decision)
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "decision", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "decision")}
                            placeholder="Select decision"
                            expandToViewport
                        />
                    </div>
                </FormField>
            );
        },
        actionsCell: (item: SignatureInput) => {
            const index = formRows.indexOf(item);
            return (
                <Button
                    iconName="remove"
                    variant="icon"
                    disabled={formRows.length <= 1}
                    onClick={() => handleRemoveRow(index)}
                />
            );
        }
    };
};

export default useSignatureFormRowCells;
