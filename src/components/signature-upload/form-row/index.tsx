import React, {useMemo} from "react";
import {FormField, Input, Select} from "@cloudscape-design/components";
import {SignatureInput} from "../../../api/signature-upload-api";
import {
    IntelligenceSource,
    ThreatClassification,
    IntelligenceLineage
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

const getIntelligenceSourceOptions = () => {
    return Object.values(IntelligenceSource).map(value => ({
        label: String(value).replace(/_/g, " "),
        value
    }));
};

const getThreatTypeOptions = () => {
    return Object.values(ThreatClassification).map(value => ({
        label: String(value).replace(/_/g, " "),
        value
    }));
};

const getLineageOptions = () => {
    return Object.values(IntelligenceLineage).map(value => ({
        label: String(value).replace(/_/g, " "),
        value
    }));
};

interface SignatureFormRowHelperProps {
    formRows: SignatureInput[];
    touchedFields: {[key: string]: boolean};
    fieldErrors: {[key: string]: boolean};
    statusOptions: {label: string; value: string}[];
    decisionOptions: {label: string; value: string}[];
    handleFormChange: (index: number, field: keyof SignatureInput, value: string) => void;
    handleFieldBlur: (index: number, field: keyof SignatureInput) => void;
    handleRemoveRow: (index: number) => void;
}

export const useSignatureFormRowCells = ({
    formRows,
    touchedFields,
    fieldErrors,
    statusOptions,
    decisionOptions,
    handleFormChange,
    handleFieldBlur,
    handleRemoveRow
}: SignatureFormRowHelperProps) => {
    const sourceOptions = useMemo(() => getIntelligenceSourceOptions(), []);
    const lineageOptions = useMemo(() => getLineageOptions(), []);
    const threatTypeOptions = useMemo(() => getThreatTypeOptions(), []);

    return useMemo(
        () => ({
            domainCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`domain-${index}`}>
                        <Input
                            value={item.domain}
                            onChange={event => handleFormChange(index, "domain", event.detail.value)}
                            onBlur={() => handleFieldBlur(index, "domain")}
                            placeholder="example.com"
                        />
                    </FormField>
                );
            },
            sourceCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`source-${index}`}>
                        <Select
                            options={sourceOptions}
                            selectedOption={
                                item.source
                                    ? sourceOptions.find(option => option.value === item.source) || {
                                          label: item.source,
                                          value: item.source
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "source", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "source")}
                            placeholder="Select source"
                            expandToViewport
                        />
                    </FormField>
                );
            },
            descriptionCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`description-${index}`}>
                        <Input
                            value={item.description || ""}
                            onChange={event => handleFormChange(index, "description", event.detail.value)}
                            placeholder="Description (required)"
                        />
                    </FormField>
                );
            },
            statusCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`status-${index}`}>
                        <Select
                            options={statusOptions}
                            selectedOption={
                                item.status
                                    ? {
                                          label: String(item.status).toUpperCase(),
                                          value: String(item.status)
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "status", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "status")}
                            placeholder="Select status"
                            expandToViewport
                        />
                    </FormField>
                );
            },
            decisionCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`decision-${index}`}>
                        <Select
                            options={decisionOptions}
                            selectedOption={
                                item.decision
                                    ? {
                                          label: String(item.decision).toUpperCase(),
                                          value: String(item.decision)
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "decision", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "decision")}
                            placeholder="Select decision"
                            expandToViewport
                        />
                    </FormField>
                );
            },
            lineageCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`lineage-${index}`}>
                        <Select
                            options={lineageOptions}
                            selectedOption={
                                item.lineage
                                    ? lineageOptions.find(option => option.value === item.lineage) || {
                                          label: item.lineage,
                                          value: item.lineage
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(index, "lineage", event.detail.selectedOption?.value || "")
                            }
                            onBlur={() => handleFieldBlur(index, "lineage")}
                            placeholder="Select lineage"
                            expandToViewport
                        />
                    </FormField>
                );
            },
            threatClassificationCell: (item: SignatureInput) => {
                const index = formRows.indexOf(item);
                return (
                    <FormField stretch controlId={`threatClassification-${index}`}>
                        <Select
                            options={threatTypeOptions}
                            selectedOption={
                                item.threatClassification
                                    ? threatTypeOptions.find(option => option.value === item.threatClassification) || {
                                          label: item.threatClassification,
                                          value: item.threatClassification
                                      }
                                    : null
                            }
                            onChange={event =>
                                handleFormChange(
                                    index,
                                    "threatClassification",
                                    event.detail.selectedOption?.value || ""
                                )
                            }
                            onBlur={() => handleFieldBlur(index, "threatClassification")}
                            placeholder="Select threat type"
                            expandToViewport
                        />
                    </FormField>
                );
            }
        }),
        [
            formRows,
            statusOptions,
            decisionOptions,
            handleFormChange,
            handleFieldBlur,
            sourceOptions,
            lineageOptions,
            threatTypeOptions
        ]
    );
};

export default useSignatureFormRowCells;
