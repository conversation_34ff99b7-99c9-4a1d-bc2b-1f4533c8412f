import React from "react";
import {<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>} from "@cloudscape-design/components";
import {SignatureEntry} from "../../../api/signature-upload-api";
import {ValidationStatus} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {ExtendedSignatureEntry} from "../results-modal";

interface ResultsTableProps {
    uploadResults: ExtendedSignatureEntry[];
}

const ResultsTable: React.FC<ResultsTableProps> = ({uploadResults}) => {
    const actualFailures = uploadResults.filter(
        entry =>
            entry.validationResult?.status === ValidationStatus.REJECTED ||
            entry.validationResult?.status === ValidationStatus.N_A ||
            !entry.success
    );

    const downloadFailedEntries = () => {
        if (actualFailures.length === 0) return;

        const csvData = actualFailures.map(item => {
            const originalInput = item.originalInput;
            return {
                domain: item.domain || "",
                source: originalInput?.source || "",
                description: originalInput?.description || "",
                status: originalInput?.status || "",
                decision: originalInput?.decision || "",
                lineage: originalInput?.lineage || "",
                threatClassification: originalInput?.threatClassification || "",
                validationStatus: item.validationResult?.status || "N/A",
                errorMessage: item.errorMessage || item.validationResult?.reason || "Unknown error"
            };
        });

        const headers = Object.keys(csvData[0]).join(",");
        const rows = csvData.map(row =>
            Object.values(row)
                .map(value => `"${String(value).replace(/"/g, '""')}"`)
                .join(",")
        );

        const csvContent = [headers, ...rows].join("\n");
        const blob = new Blob([csvContent], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "failed_uploads.csv";
        link.click();
        URL.revokeObjectURL(url);
    };

    const getStatusDisplay = (item: SignatureEntry) => {
        const status = item.validationResult?.status;
        const hasSignature = Boolean(item.signatureId);

        if (status === ValidationStatus.APPROVED && hasSignature) {
            return <Box color="text-status-success">Approved & Created</Box>;
        }
        if (status === ValidationStatus.APPROVED && !hasSignature) {
            return <Box color="text-status-warning">Approved (No Signature)</Box>;
        }
        if (status === ValidationStatus.N_A || !status) {
            return <Box color="text-status-error">Failed (N/A)</Box>;
        }
        return <Box color="text-status-error">Failed</Box>;
    };

    const columnDefinitions = [
        {
            id: "domain",
            header: "Domain",
            cell: (item: SignatureEntry) => item.domain
        },
        {
            id: "status",
            header: "Status",
            cell: getStatusDisplay
        },
        {
            id: "signatureId",
            header: "Signature ID",
            cell: (item: SignatureEntry) => {
                if (!item.success || item.validationResult?.status !== ValidationStatus.APPROVED) {
                    return "-";
                }
                return item.signatureId || "-";
            }
        },
        {
            id: "createdAt",
            header: "Created At",
            cell: (item: SignatureEntry) => (item.createdAt ? new Date(item.createdAt).toLocaleString() : "-")
        },
        {
            id: "errorMessage",
            header: "Error Message",
            cell: (item: SignatureEntry) => item.errorMessage || item.validationResult?.reason || "-"
        }
    ];

    return (
        <Table
            columnDefinitions={columnDefinitions}
            items={uploadResults}
            header={
                <Header
                    actions={
                        actualFailures.length > 0 ? (
                            <Button onClick={downloadFailedEntries}>
                                Download Failed Entries ({actualFailures.length})
                            </Button>
                        ) : null
                    }
                >
                    Upload Results ({uploadResults.length} total)
                </Header>
            }
            empty="No results to display"
        />
    );
};

export default ResultsTable;
