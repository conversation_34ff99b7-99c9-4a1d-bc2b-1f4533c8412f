import React from "react";
import {<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>} from "@cloudscape-design/components";
import {SignatureEntry} from "../../../api/signature-upload-api";

interface ResultsTableProps {
    uploadResults: SignatureEntry[];
}

const ResultsTable: React.FC<ResultsTableProps> = ({uploadResults}) => {
    const handleDownloadFailed = () => {
        const failedEntries = uploadResults.filter(entry => !entry.success);
        if (failedEntries.length === 0) return;

        const headers = "Domain,Error\n";
        const rows = failedEntries.map(item => `${item.domain},${item.errorMessage || "Unknown error"}`).join("\n");
        const csvContent = headers + rows;

        const blob = new Blob([csvContent], {type: "text/csv"});
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "failed_uploads.csv";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const columnDefinitions = [
        {
            id: "domain",
            header: "Domain",
            cell: (item: SignatureEntry) => item.domain
        },
        {
            id: "status",
            header: "Status",
            cell: (item: SignatureEntry) => (
                <Box color={item.success ? "text-status-success" : "text-status-error"}>
                    {item.success ? "Success" : "Failed"}
                </Box>
            )
        },
        {
            id: "signatureId",
            header: "Signature ID",
            cell: (item: SignatureEntry) => item.signatureId || "-"
        },
        {
            id: "createdAt",
            header: "Created At",
            cell: (item: SignatureEntry) => (item.createdAt ? new Date(item.createdAt).toLocaleString() : "-")
        },
        {
            id: "error",
            header: "Error Message",
            cell: (item: SignatureEntry) => item.errorMessage || "-"
        }
    ];

    return (
        <Table
            columnDefinitions={columnDefinitions}
            items={uploadResults}
            header={
                <Header
                    actions={
                        uploadResults.filter(entry => !entry.success).length > 0 ? (
                            <Button onClick={handleDownloadFailed}>Download Failed Entries</Button>
                        ) : null
                    }
                >
                    Upload Results
                </Header>
            }
        />
    );
};

export default ResultsTable;
