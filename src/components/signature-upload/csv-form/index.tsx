import React from "react";
import {<PERSON>Between, Alert, Link, FileUpload, FormField, RadioGroup, Button} from "@cloudscape-design/components";

interface CsvFormProps {
    csvFile: File | null;
    csvError: string | null;
    uploadMode: string;
    isLoading: boolean;
    handleCsvUpload: (event: {detail: {value: File[]}}) => void;
    handleDownloadTemplate: () => void;
    setUploadMode: (mode: string) => void;
    handleCsvSubmit: () => void;
    handleReset: () => void;
}

const CsvForm: React.FC<CsvFormProps> = ({
    csvFile,
    csvError,
    uploadMode,
    isLoading,
    handleCsvUpload,
    handleDownloadTemplate,
    setUploadMode,
    handleCsvSubmit,
    handleReset
}) => {
    return (
        <SpaceBetween size="l">
            <Alert>
                Upload a CSV file with the following columns: Domain, Source, Description, Status, Decision.
                <br />
                <Link onFollow={handleDownloadTemplate}>Download CSV Template</Link>
            </Alert>

            <>
                <FileUpload
                    onChange={handleCsvUpload}
                    value={csvFile ? [csvFile] : []}
                    i18nStrings={{
                        uploadButtonText: e => "Choose file",
                        dropzoneText: e => "Drop CSV file here",
                        removeFileAriaLabel: e => `Remove file ${e + 1}`,
                        limitShowFewer: "Show fewer files",
                        limitShowMore: "Show more files",
                        errorIconAriaLabel: "Error"
                    }}
                    accept=".csv"
                    multiple={false}
                    constraintText="Only .csv files are supported"
                />

                {csvError && <Alert type="error">{csvError}</Alert>}
            </>

            <FormField label="Upload Mode">
                <RadioGroup
                    items={[
                        {value: "add", label: "Add New Signatures"},
                        {
                            value: "update",
                            label: "Update Existing Signatures",
                            disabled: true
                        }
                    ]}
                    value={uploadMode}
                    onChange={event => setUploadMode(event.detail.value)}
                />
            </FormField>

            <SpaceBetween direction="horizontal" size="xs">
                <Button
                    variant="primary"
                    loading={isLoading}
                    disabled={!csvFile || csvError !== null}
                    onClick={handleCsvSubmit}
                >
                    Upload
                </Button>
                <Button onClick={handleReset}>Clear</Button>
            </SpaceBetween>
        </SpaceBetween>
    );
};

export default CsvForm;
