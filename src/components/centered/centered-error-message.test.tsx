import * as React from "react";
import {render} from "@testing-library/react";
import CenteredErrorMessage from "./centered-error-message";

describe("Centered", () => {
    it("Displays the children", () => {
        const {getByText, getByRole} = render(
            <CenteredErrorMessage message="Error message">
                <span>Some text</span>
            </CenteredErrorMessage>
        );
        // Use type assertions to fix TypeScript errors
        expect(getByText("Error message")).toBeDefined();
        expect(getByText("Some text")).toBeDefined();
    });
});
