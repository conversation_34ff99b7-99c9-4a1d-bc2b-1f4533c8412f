import React, {Component} from "react";
import "./style.css";

export default class Centered extends Component<CenteredProps, Record<string, never>> {
    public render() {
        return (
            <div className={this.props.centerVertically ?? true ? "centered centered-vertically" : "centered"}>
                {this.props.children}
            </div>
        );
    }
}

interface CenteredProps {
    children: React.ReactNode;
    centerVertically?: boolean;
}
