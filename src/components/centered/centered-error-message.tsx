import React, {Component} from "react";
import Centered from "./centered";

export default class CenteredErrorMessage extends Component<CenteredErrorMessageProps, Record<string, never>> {
    public render() {
        return (
            <div>
                <Centered>
                    <img src="500.png" alt="" />
                    <div>
                        <strong>
                            {this.props.message ? this.props.message : "I'm sorry, but something is broken"}
                        </strong>
                    </div>
                    {this.props.children}
                </Centered>
            </div>
        );
    }
}

interface CenteredErrorMessageProps {
    children?: React.ReactNode;
    message?: string;
}
