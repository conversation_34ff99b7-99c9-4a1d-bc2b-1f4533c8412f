import * as React from "react";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, SpaceBetween} from "@cloudscape-design/components";

interface ProfileProps {
    className?: string;
}

const Profile: React.FC<ProfileProps> = ({className}) => {
    return (
        <Container className={className} header={<Header variant="h2">User Profile</Header>}>
            <SpaceBetween size="l">
                <Alert type="info">
                    <Box variant="h3">Coming Soon!</Box>
                    <Box variant="p">
                        The user profile feature is currently under development and will be available soon. This section
                        will allow you to view and manage your user information, preferences, and settings.
                    </Box>
                </Alert>
            </SpaceBetween>
        </Container>
    );
};

export default Profile;
