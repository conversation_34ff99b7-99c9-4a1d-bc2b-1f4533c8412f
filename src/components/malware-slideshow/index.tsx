import React, {useState, useEffect} from "react";
import {<PERSON>, Button, SpaceBet<PERSON>en, <PERSON>con, <PERSON><PERSON>, Header} from "@cloudscape-design/components";
import {IconProps} from "@cloudscape-design/components/icon";

interface MalwareFact {
    title: string;
    content: string;
    icon?: string;
}

// Malware facts
const malwareFacts: MalwareFact[] = [
    {
        title: "Malware Initial Access Techniques",
        content:
            "Malware often gains initial access through vectors like phishing emails (malicious attachments), weaponized documents (macros), drive-by downloads via compromised websites, and increasingly, social engineering through fake SaaS login portals.",
        icon: "envelope"
    },
    {
        title: "Payload Delivery Mechanisms",
        content:
            "Modern malware delivery mechanisms use multi-stage loaders: a lightweight dropper establishes foothold, downloads heavier secondary payloads (e.g., Cobalt Strike, remote access trojans), and dynamically executes them in-memory to avoid disk-based detection.",
        icon: "download"
    },
    {
        title: "Persistence Mechanisms",
        content:
            "Malware ensures persistence via scheduled tasks, registry keys (Run, RunOnce), services (service creation/modification), startup folders, or abusing legitimate system binaries (living off the land binaries, or LOLBins, like wscript.exe, regsvr32.exe).",
        icon: "calendar"
    },
    {
        title: "Command-and-Control (C2) Channels",
        content:
            "C2 traffic now often uses encrypted HTTPS, DNS tunneling, social media platforms (like Discord/Telegram bots), or cloud storage APIs (e.g., Google Drive abuse) to blend in with legitimate network traffic.",
        icon: "share"
    },
    {
        title: "Evasion Techniques",
        content:
            "Malware employs polymorphism (changing its own code on each infection), packer obfuscation (e.g., UPX, custom crypters), environment checks (sandbox evasion by delaying execution or detecting virtualized environments), and process hollowing to bypass static and behavioral analysis.",
        icon: "view-vertical"
    },
    {
        title: "Fileless Malware",
        content:
            "Fileless attacks operate entirely within memory by exploiting existing OS tools (PowerShell, WMI, MSHTA). Since they leave no obvious artifacts on disk, traditional antivirus signatures are ineffective.",
        icon: "file-open"
    },
    {
        title: "Credential Theft Techniques",
        content:
            "Info-stealers target browser credential stores, memory dumps (lsass.exe for Windows login credentials via Mimikatz-like behavior), clipboard monitors, and API token caches from cloud CLI tools (e.g., AWS CLI, Azure CLI).",
        icon: "key"
    },
    {
        title: "Lateral Movement Strategies",
        content:
            "After gaining foothold, malware often exploits SMB shares, reuses stolen credentials (pass-the-hash attacks), exploits remote desktop protocols (RDP brute force), or utilizes Windows Admin Shares to propagate across an internal network.",
        icon: "expand"
    },
    {
        title: "Exfiltration Techniques",
        content:
            "Exfiltrated data is often compressed, encrypted, and hidden in outbound traffic. Attackers exfiltrate via direct HTTPS POST requests, staged uploads to public file-sharing platforms (Dropbox, Pastebin), or hide stolen data inside benign protocols like DNS.",
        icon: "upload"
    },
    {
        title: "Encryption/Obfuscation Techniques",
        content:
            "Modern malware encrypts payloads using lightweight ciphers (XOR, AES), packs modules separately, and can embed payloads within seemingly harmless formats (e.g., PNG image steganography) to evade content filtering.",
        icon: "lock-private"
    },
    {
        title: "Types of Malware Families",
        content:
            "Ransomware (encryption/extortion), Trojans (stealth backdoors, loaders), Rootkits (kernel-level hiding), Keyloggers (credential harvesting), Banking Trojans (credential theft + session hijack), Cryptominers (resource hijacking), Wipers (destruction). Each family evolves constantly with cross-pollination of tactics.",
        icon: "folder"
    },
    {
        title: "Malware-as-a-Service (MaaS) Ecosystem",
        content:
            "Cybercriminals no longer need technical skills: full ransomware, phishing kits, info-stealers, and initial access brokers are sold as subscription services on dark web markets, drastically lowering the barrier for attacks.",
        icon: "shopping-cart"
    },
    {
        title: "Domain Generation Algorithms (DGAs)",
        content:
            "Malware can generate hundreds of random domain names per day (e.g., ghudrkwv.biz) for C2 callbacks, frustrating static block lists. Detection requires algorithmic prediction or real-time DNS anomaly monitoring.",
        icon: "random"
    },
    {
        title: "Supply Chain Compromises",
        content:
            "Attackers increasingly inject malware into software supply chains — compromising libraries (e.g., NPM, PyPI packages), build systems (SolarWinds incident), or dependency repositories to attack downstream targets invisibly.",
        icon: "boxes"
    },
    {
        title: "Defense Evasion via Legitimate Cloud Services",
        content:
            'Malware operators increasingly abuse cloud platforms like AWS, Azure, Google Drive to stage payloads, host C2 servers, or launder stolen data, complicating traditional detection approaches that rely on "bad IP" block lists.',
        icon: "cloud"
    }
];

const MalwareSlideshow: React.FC = () => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [fadeIn, setFadeIn] = useState(true);

    // Helper function to handle slide transitions with fade effect
    const changeSlide = (getNextSlide: (prev: number) => number) => {
        setFadeIn(false);
        setTimeout(() => {
            setCurrentSlide(getNextSlide);
            setFadeIn(true);
        }, 300); // Wait for fade out animation
    };

    // Auto-rotate slides every 7 seconds
    useEffect(() => {
        const timer = setTimeout(() => {
            changeSlide(prev => (prev + 1) % malwareFacts.length);
        }, 7000);

        return () => clearTimeout(timer);
    }, [currentSlide]);

    const goToNextSlide = () => {
        changeSlide(prev => (prev + 1) % malwareFacts.length);
    };

    const goToPrevSlide = () => {
        changeSlide(prev => (prev - 1 + malwareFacts.length) % malwareFacts.length);
    };

    const currentFact = malwareFacts[currentSlide];

    // Function to highlight keywords in the content
    const highlightKeywords = (text: string) => {
        // Define keywords to highlight
        const keywords = [
            "phishing",
            "weaponized",
            "drive-by",
            "social engineering",
            "multi-stage",
            "dropper",
            "in-memory",
            "persistence",
            "registry keys",
            "LOLBins",
            "C2",
            "DNS tunneling",
            "polymorphism",
            "packer",
            "sandbox evasion",
            "process hollowing",
            "Fileless",
            "PowerShell",
            "WMI",
            "credential",
            "Mimikatz",
            "pass-the-hash",
            "RDP",
            "exfiltrate",
            "steganography",
            "Ransomware",
            "Trojans",
            "Rootkits",
            "Keyloggers",
            "Cryptominers",
            "Malware-as-a-Service",
            "Domain Generation",
            "Supply Chain"
        ];

        let highlightedText = text;
        keywords.forEach(keyword => {
            const regex = new RegExp(`(${keyword})`, "gi");
            highlightedText = highlightedText.replace(regex, "<strong>$1</strong>");
        });

        return <span dangerouslySetInnerHTML={{__html: highlightedText}} />;
    };

    return (
        <Cards
            cardDefinition={{
                header: item => (
                    <Header
                        variant="h3"
                        actions={
                            <SpaceBetween direction="horizontal" size="xs">
                                <Button
                                    iconName="angle-left"
                                    variant="icon"
                                    onClick={goToPrevSlide}
                                    ariaLabel="Previous slide"
                                />
                                <Box variant="small" color="text-body-secondary">
                                    {currentSlide + 1} / {malwareFacts.length}
                                </Box>
                                <Button
                                    iconName="angle-right"
                                    variant="icon"
                                    onClick={goToNextSlide}
                                    ariaLabel="Next slide"
                                />
                            </SpaceBetween>
                        }
                    >
                        {item.icon && <Icon name={item.icon as IconProps["name"]} />} {item.title}
                    </Header>
                ),
                sections: [
                    {
                        id: "content",
                        content: item => (
                            <div
                                style={{
                                    opacity: fadeIn ? 1 : 0,
                                    transition: "opacity 0.3s ease"
                                }}
                            >
                                <Box variant="p" fontSize="body-m">
                                    {highlightKeywords(item.content)}
                                </Box>
                            </div>
                        )
                    }
                ]
            }}
            cardsPerRow={[{cards: 1}]}
            items={[currentFact]}
            loadingText="Loading malware insights"
            empty={
                <Box textAlign="center" color="inherit">
                    <b>No malware insights available</b>
                    <Box padding={{bottom: "s"}} variant="p" color="inherit">
                        No insights to display.
                    </Box>
                </Box>
            }
            trackBy="title"
        />
    );
};

export default MalwareSlideshow;
