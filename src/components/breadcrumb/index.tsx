import React, {useEffect, useState, useMemo} from "react";
import {useLocation} from "react-router-dom";
import {BreadcrumbGroup} from "@cloudscape-design/components";

interface BreadcrumbItem {
    text: string;
    href: string;
}

interface BreadcrumbNavigationProps {
    extraItems?: BreadcrumbItem[];
}

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({extraItems = []}) => {
    const location = useLocation();
    const [items, setItems] = useState<BreadcrumbItem[]>([]);

    const basePathToName = useMemo(
        () => ({
            "/": "Home",
            "/creative-browser": "Creative Browser",
            "/upload-signature": "Signature Upload"
        }),
        []
    );

    useEffect(() => {
        const newItems: BreadcrumbItem[] = [{text: "Home", href: "/"}];

        if (location.pathname !== "/") {
            const basePath = `/${location.pathname.split("/")[1]}`;

            const text =
                basePathToName[basePath] || basePath.charAt(1).toUpperCase() + basePath.slice(2).replace(/-/g, " ");

            newItems.push({text, href: basePath});
        }

        // Add any extra items (for dynamic pages)
        setItems([...newItems, ...extraItems]);
    }, [location.pathname, extraItems, basePathToName]);

    return (
        <BreadcrumbGroup
            items={items}
            onFollow={e => {
                e.preventDefault();
                if (e.detail.href === "#") {
                    // No action for disabled "Coming Soon" features
                } else {
                    // Use window.location.href to force a full page reload
                    window.location.href = `#${e.detail.href}`;
                    window.location.reload();
                }
            }}
            ariaLabel="Breadcrumbs"
        />
    );
};

export default BreadcrumbNavigation;
