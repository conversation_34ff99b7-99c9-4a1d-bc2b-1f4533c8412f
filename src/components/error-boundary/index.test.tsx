import * as React from "react";
import {cleanup, render} from "@testing-library/react";
import ErrorBoundary from ".";

describe("ErrorBoundary", () => {
    it("Renders its child directly", () => {
        const {getByText} = render(
            <ErrorBoundary>
                {" "}
                <span>children of error boundary</span>{" "}
            </ErrorBoundary>
        );
        expect(getByText("children of error boundary")).toBeDefined();
    });

    it("Displays a message when an error is caught", () => {
        const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
        const Child = () => {
            if (1 === 1) {
                throw new Error("Error for testing");
            }
            // Make typescript happy by returning something
            return <span />;
        };

        const {getByText} = render(
            <ErrorBoundary>
                {" "}
                <Child />{" "}
            </ErrorBoundary>
        );
        expect(getByText("Error for testing", {exact: false})).toBeDefined();
        consoleErrorSpy.mockRestore();
    });
});
