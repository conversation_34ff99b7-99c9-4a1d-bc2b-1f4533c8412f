/* eslint-disable no-restricted-globals */

import React, {Component} from "react";
import {Button} from "reactstrap";
import {CenteredErrorMessage} from "../centered";

const SIM_URL =
    "https://sim.amazon.com/issues/create?assignedFolder=e7d9390d-af05-4a9c-bfbe-502a2644df5f&labels[0][id]=5ca4f614-2725-4db0-a4dd-1ac6d236404a&labels[1][id]=0f129390-e605-4d75-8561-67c63491764f";

export default class ErrorBoundary extends Component<ErrorBoundaryProps, State> {
    public state: State = {};

    public render() {
        const {error} = this.state;
        if (error) {
            return (
                <CenteredErrorMessage message="Looks like something very unexpected happened.">
                    <p>Error message: {error.toString()}</p>
                    <Button href={this.buildSIMUrl()} color="danger">
                        Cut a SIM with the full details
                    </Button>
                </CenteredErrorMessage>
            );
        }
        return this.props.children;
    }

    static getDerivedStateFromError(error: Error) {
        return {error};
    }

    public componentDidCatch(error: Error, info: React.ErrorInfo) {
        this.setState({error, info});
    }

    private buildSIMUrl() {
        const {error, info} = this.state;
        if (!error || !info) {
            return SIM_URL;
        } else {
            const title = encodeURIComponent(`Exception: ${error.toString()}`);
            const description =
                encodeURIComponent(`<Please give a short summary of what you were doing before the error occurred>

------------------------------------------------------
Debug info:

URL: ${location.href}

Component Stack:
${info.componentStack}

${error.stack || error.toString()}`);
            return `${SIM_URL}&title=${title}&description=${description}`;
        }
    }
}

interface ErrorBoundaryProps {
    children: React.ReactNode;
}

interface State {
    error?: Error;
    info?: React.ErrorInfo;
}
