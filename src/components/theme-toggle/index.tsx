import React from "react";
import {Toggle} from "@cloudscape-design/components";
import {useDarkMode} from "../../hooks/useDarkMode";

const ThemeToggle: React.FC = () => {
    const [isDarkMode, toggleTheme] = useDarkMode();

    return (
        <Toggle checked={isDarkMode} onChange={toggleTheme} ariaLabel="Toggle dark mode">
            {isDarkMode ? "Dark" : "Light"} mode
        </Toggle>
    );
};

export default ThemeToggle;
