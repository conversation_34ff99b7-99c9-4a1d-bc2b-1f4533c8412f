import React from "react";
import {Box, SpaceBetween} from "@cloudscape-design/components";

interface LoadingProps {
    message?: string;
}

const Loading: React.FC<LoadingProps> = ({message = "Loading..."}) => {
    return (
        <Box textAlign="center" padding="l">
            <SpaceBetween size="m" direction="vertical" alignItems="center">
                <div
                    style={{
                        border: "4px solid rgba(0, 0, 0, 0.1)",
                        width: "36px",
                        height: "36px",
                        borderRadius: "50%",
                        borderLeftColor: "#09d",
                        animation: "spin 1s linear infinite"
                    }}
                />
                <Box fontSize="heading-m" fontWeight="normal" color="text-body-secondary">
                    {message}
                </Box>
            </SpaceBetween>
            <style>
                {`
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                `}
            </style>
        </Box>
    );
};

export default Loading;
