import React, {Component} from "react";
import Centered from "../centered/centered";
import "./style.css";

interface Props {
    message?: string;
    centerVertically?: boolean;
}

export default class Spinner extends Component<Props> {
    public render() {
        return (
            <Centered centerVertically={this.props.centerVertically}>
                <div className="spinner">
                    <i className="fa fa-spinner fa-spin" />
                </div>
                <div className="message">{this.props.message ? this.props.message : ""}</div>
            </Centered>
        );
    }
}
