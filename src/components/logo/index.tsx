import React from "react";
import {Link} from "react-router-dom";
import lightLogoImage from "../../assets/logo/light-theme-logo-img.png";
import darkLogoImage from "../../assets/logo/dark-theme-logo-img.png";
import {useDarkMode} from "../../hooks/useDarkMode";

interface LogoProps {
    size?: "small" | "medium" | "large";
}

const Logo: React.FC<LogoProps> = ({size = "medium"}) => {
    const [isDarkMode] = useDarkMode();

    // Select the appropriate logo based on the theme
    const logoImage = isDarkMode ? darkLogoImage : lightLogoImage;

    // Determine the height based on the size prop
    const getHeight = () => {
        switch (size) {
            case "small":
                return "64px";
            case "large":
                return "128px";
            case "medium":
            default:
                return "96px";
        }
    };

    return (
        <Link to="/" style={{textDecoration: "none", display: "inline-block", padding: 0, margin: 0}} className="logo">
            <img
                src={logoImage}
                alt="HumanEx Logo"
                style={{
                    height: getHeight(),
                    width: "auto",
                    objectFit: "contain",
                    display: "block",
                    padding: 0,
                    margin: "-25px 0", // More aggressive negative margin to trim empty space
                    verticalAlign: "middle"
                }}
            />
        </Link>
    );
};

export default Logo;
