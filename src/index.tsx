import React from "react";
import {createRoot} from "react-dom/client";
import {initialize} from "./api";

import App from "./App";
import SomethingBroken from "./pages/something-broken/SomethingBroken";

import "bootstrap/dist/css/bootstrap.min.css";
import "font-awesome/css/font-awesome.min.css";
import "./style.css";

// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const rootElement = document.getElementById("root")!;
initialize()
    .then(() => {
        const root = createRoot(rootElement);
        root.render(<App />);
    })
    .catch(e => {
        // eslint-disable-next-line no-console
        console.error(e);
        const root = createRoot(rootElement);
        root.render(<SomethingBroken />);
    });
