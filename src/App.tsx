import React from "react";
import {HashRouter, Routes, Route, useLocation} from "react-router-dom";
import ErrorBoundary from "./components/error-boundary";
import Navbar from "./components/navbar";
import HomePage from "./pages/home";
import CreativeBrowserPage from "./pages/creative-browser";
import GetCreativeRenders from "./pages/creative-browser/get-creative-renders";
import GetCreativeRender from "./pages/creative-browser/get-creative-render";
import SignatureUploadPage from "./pages/signature-upload";

const AppContent: React.FC = () => {
    const location = useLocation();

    return (
        <ErrorBoundary>
            <Navbar>
                <Routes key={location.pathname}>
                    <Route path="/" element={<HomePage />} />
                    <Route path="/upload-signature" element={<SignatureUploadPage />} />
                    <Route path="/creative-browser" element={<CreativeBrowserPage />} />
                    <Route
                        path="/creative/:creativeIDSpace?/:creativeID?/:variantID?"
                        element={<GetCreativeRenders />}
                    />
                    <Route path="/creative-render/:eriskayID/:renderID" element={<GetCreativeRender />} />
                </Routes>
            </Navbar>
        </ErrorBoundary>
    );
};

const App: React.FC = () => {
    return (
        <HashRouter>
            <AppContent />
        </HashRouter>
    );
};

export default App;
