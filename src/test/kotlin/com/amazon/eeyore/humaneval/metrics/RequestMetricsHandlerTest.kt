package com.amazon.eeyore.humaneval.metrics

import com.amazon.adrisk.util.RequestContext
import com.amazon.coral.metrics.Metrics
import com.amazon.coral.metrics.helper.Dimension
import com.amazon.coral.service.Job
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.hamcrest.Matchers.instanceOf
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.mockConstruction
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import javax.measure.unit.Unit

class RequestMetricsHandlerTest {
    private val metrics = mock<Metrics>()

    @Test
    fun `before records input request count`() {
        val handler = RequestMetricsHandler()
        val job = mock<Job>()

        whenever(job.metrics).thenReturn(metrics)

        handler.before(job)

        verify(metrics).addMetric(
            INPUT_REQUEST_COUNT.name,
            1.0,
            Unit.ONE,
            Dimension(Dimensions.Program, METRIC_DIMENSION_PROGRAM_HARRIS),
        )

        closeRequestContext(job)
    }

    @Test
    fun `before wraps metrics in ignore close handler`() {
        val handler = RequestMetricsHandler()
        val job = mock<Job>()

        whenever(job.metrics).thenReturn(metrics)
        RequestContext(metrics).use {
            mockConstruction(RequestContext::class.java) { _, c ->
                assertThat(
                    c.arguments()[0],
                    instanceOf(IgnoresCloseMetrics::class.java),
                )
            }.use {
                handler.before(job)

                assertThat(it.constructed(), hasSize(1))
            }
        }

        verify(job).setAttribute(eq(REQUEST_CONTEXT), any())
    }

    @Test
    fun `after closes request context`() {
        val handler = RequestMetricsHandler()
        val job = mock<Job>()
        val requestContext = mock<RequestContext>()
        whenever(job.getAttribute(REQUEST_CONTEXT)).thenReturn(requestContext)

        handler.after(job)

        verify(requestContext).close()
    }

    private fun closeRequestContext(mockedJob: Job) {
        val argumentCaptor = argumentCaptor<RequestContext>()
        verify(mockedJob).setAttribute(eq(REQUEST_CONTEXT), argumentCaptor.capture())

        argumentCaptor.firstValue.close()
    }
}
