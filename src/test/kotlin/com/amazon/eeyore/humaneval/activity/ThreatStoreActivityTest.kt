package com.amazon.eeyore.humaneval.activity

import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.guice.TestWoozleServiceModule
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humaneval.testutil.TestHumanEvalServiceConfiguration
import com.amazon.eeyore.humanevaluation.model.CreateSignaturesRequest
import com.amazon.eeyore.humanevaluation.model.ForbiddenException
import com.amazon.eeyore.humanevaluation.model.InvalidRequestParametersException
import com.amazon.eeyore.humanevaluation.model.SearchSignaturesRequest
import com.amazon.eeyore.humanevaluation.model.SignatureEntry
import com.amazon.eeyore.humanevaluation.model.UnauthorizedException
import com.google.inject.Guice
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInAnyOrder
import org.hamcrest.Matchers.containsString
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.hamcrest.Matchers.instanceOf
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class ThreatStoreActivityTest {

    private lateinit var threatStoreService: ThreatStoreService
    private lateinit var authService: AuthService
    private lateinit var threatStoreActivity: ThreatStoreActivity
    private lateinit var configuration: TestHumanEvalServiceConfiguration

    private var apiRequestContext: APIRequestContext? = null
    private var testUser = User("test-user", UserType.User)

    @BeforeEach
    fun setUp() {
        val injector = Guice.createInjector(TestWoozleServiceModule())
        threatStoreService = injector.getInstance(ThreatStoreService::class.java)

        authService = mock()
        whenever(authService.isAuthorized(any<User>(), any<ResourceType>())).thenReturn(true)

        configuration = TestHumanEvalServiceConfiguration()

        threatStoreActivity = ThreatStoreActivity(threatStoreService, authService, configuration)
    }

    @AfterEach
    fun teardown() {
        apiRequestContext?.close()
    }

    @Test
    fun `createSignature should process single signature successfully when validation approved`() {
        val signatureEntry = SignatureEntry.builder()
            .withDomain("example.com")
            .withSource("SYSTEM_TESTING")
            .withDescription("Test signature")
            .withStatus("shadow")
            .withDecision("threat")
            .build()

        val request = CreateSignaturesRequest.builder()
            .withSignatures(listOf(signatureEntry))
            .build()

        try {
            val response = threatStoreActivity.createSignature(request)

            assertThat(response.createdSignatures, notNullValue())
            assertThat(response.createdSignatures, hasSize(1))

            val createdEntry = response.createdSignatures[0]
            assertThat(createdEntry.domain, equalTo("example.com"))
        } catch (e: IllegalArgumentException) {
            assertThat(e.message, containsString("Current user is required"))
        } catch (e: UnauthorizedException) {
            assertThat(e.message, containsString("unauthorized"))
        }
    }

    @Test
    fun `test MockThreatStoreService validation logic for different domains`() {
        val approvedRequest = createValidateRequest("example.com")
        val approvedResult = threatStoreService.validateSignature(approvedRequest)
        assertThat(approvedResult, notNullValue())

        val rejectedRequest = createValidateRequest("malicious.com")
        val rejectedResult = threatStoreService.validateSignature(rejectedRequest)
        assertThat(rejectedResult, notNullValue())

        val createRequest = createSignatureRequest("test.com")
        val createResult = threatStoreService.createSignature(createRequest)
        assertThat(createResult, notNullValue())
    }

    @Test
    fun `test response structure for multiple signatures`() {
        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("good.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Good domain")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
            SignatureEntry.builder()
                .withDomain("malicious.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Bad domain")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        try {
            val response = threatStoreActivity.createSignature(request)

            assertThat(response.createdSignatures, notNullValue())
            assertThat(response.createdSignatures, hasSize(2))

            val domains = response.createdSignatures.map { it.domain }
            assertThat(domains, containsInAnyOrder("good.com", "malicious.com"))
        } catch (e: IllegalArgumentException) {
            assertThat(e.message, containsString("Current user is required"))
        } catch (e: UnauthorizedException) {
            assertThat(e.message, containsString("unauthorized"))
        }
    }

    private fun createValidateRequest(domain: String) =
        mock<com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest>().apply {
            val content = mock<com.amazonaws.services.woozlethreatstore.model.ValidateSignatureContent>()
            val domainContent = mock<com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent>()
            whenever(this.content).thenReturn(content)
            whenever(content.domainSignatureContent).thenReturn(domainContent)
            whenever(domainContent.domain).thenReturn(domain)
        }

    private fun createSignatureRequest(domain: String) =
        mock<com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest>().apply {
            val content = mock<com.amazonaws.services.woozlethreatstore.model.CreateSignatureContent>()
            val domainContent = mock<com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent>()
            whenever(this.content).thenReturn(content)
            whenever(content.domainSignatureContent).thenReturn(domainContent)
            whenever(domainContent.domain).thenReturn(domain)
        }

    @Test
    fun `test response structure matches UI expectations`() {
        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("test.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Test domain")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        try {
            val response = threatStoreActivity.createSignature(request)

            assertThat("Response should not be null", response, notNullValue())
            assertThat("Response should have createdSignatures field", response.createdSignatures, notNullValue())
            assertThat("createdSignatures should be a list", response.createdSignatures, instanceOf(List::class.java))
            assertThat("createdSignatures should have correct size", response.createdSignatures, hasSize(1))

            val entry = response.createdSignatures[0]
            assertThat("Entry should have domain", entry.domain, notNullValue())
            assertThat("Entry should have domain value", entry.domain, equalTo("test.com"))

            println("Response structure:")
            println("- createdSignatures: ${response.createdSignatures}")
            println("- Entry 0: domain=${entry.domain}, signatureId=${entry.signatureId}, success=${entry.isSuccess}")
        } catch (e: IllegalArgumentException) {
            assertThat(e.message, containsString("Current user is required"))
        } catch (e: UnauthorizedException) {
            assertThat(e.message, containsString("unauthorized"))
        } catch (e: Exception) {
            println("Exception during test: ${e.message}")
        }
    }

    @Test
    fun `createSignature throws UnauthorizedException when no user context`() {
        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("test.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Test signature")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        val exception = assertThrows<UnauthorizedException> {
            threatStoreActivity.createSignature(request)
        }

        assertThat(exception.message, equalTo("Client is unauthorized"))
    }

    @Test
    fun `createSignature throws ForbiddenException when user not authorized`() {
        withUser("unauthorized-user")
        whenever(authService.isAuthorized(any<User>(), any<ResourceType>())).thenReturn(false)

        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("test.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Test signature")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        val exception = assertThrows<ForbiddenException> {
            threatStoreActivity.createSignature(request)
        }

        assertThat(exception.message, equalTo("Unauthorized to access resource: SIGNATURE_UPDATE"))
    }

    @Test
    fun `createSignature handles successful validation and creation`() {
        withUser("test-user")

        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("good.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Good domain")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        val response = threatStoreActivity.createSignature(request)

        assertThat(response.createdSignatures, hasSize(1))
        val entry = response.createdSignatures[0]
        assertThat(entry.domain, equalTo("good.com"))
        assertThat(entry.isSuccess, equalTo(true))
        assertThat(entry.signatureId, notNullValue())
        assertThat(entry.validationResult.domain, equalTo("good.com"))
    }

    @Test
    fun `createSignature handles validation rejection`() {
        withUser("test-user")

        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("malicious.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Malicious domain")
                .withStatus("shadow")
                .withDecision("threat")
                .withLineage("AUTOMATED_DETECTION")
                .withThreatClassification("MALWARE")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        val response = threatStoreActivity.createSignature(request)

        assertThat(response.createdSignatures, hasSize(1))
        val entry = response.createdSignatures[0]
        assertThat(entry.domain, equalTo("malicious.com"))
        assertThat(entry.isSuccess, equalTo(false))
        assertThat(entry.errorMessage, containsString("Validation failed"))
        assertThat(entry.validationResult.status, equalTo("REJECTED"))
    }

    @Test
    fun `createSignature handles exception during processing`() {
        withUser("test-user")

        val mockThreatStoreService = mock<ThreatStoreService>()
        whenever(mockThreatStoreService.validateSignature(any()))
            .thenThrow(InvalidRequestParametersException("Service error"))

        val activityWithMockService = ThreatStoreActivity(mockThreatStoreService, authService, configuration)

        val signatures = listOf(
            SignatureEntry.builder()
                .withDomain("error.com")
                .withSource("SYSTEM_TESTING")
                .withDescription("Error domain")
                .withStatus("shadow")
                .withDecision("threat")
                .build(),
        )

        val request = CreateSignaturesRequest.builder()
            .withSignatures(signatures)
            .build()

        val response = activityWithMockService.createSignature(request)

        assertThat(response.createdSignatures, hasSize(1))
        val entry = response.createdSignatures[0]
        assertThat(entry.domain, equalTo("error.com"))
        assertThat(entry.isSuccess, equalTo(false))
        assertThat(entry.errorMessage, containsString("Service error"))
    }

    @Test
    fun `createSignature handles empty signatures list`() {
        withUser("test-user")

        val request = CreateSignaturesRequest.builder()
            .withSignatures(emptyList())
            .build()

        val response = threatStoreActivity.createSignature(request)

        assertThat(response.createdSignatures, hasSize(0))
    }

    @Test
    fun `searchSignatures should return signatures successfully`() {
        withUser("test-user")

        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .withMaxResults(10)
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response, notNullValue())
        assertThat(response.items, notNullValue())
        assertThat(response.totalCount, notNullValue())

        assertThat(response.items.size, equalTo(response.totalCount))

        if (response.items.isNotEmpty()) {
            response.items.forEach { item ->
                assertThat("All returned items should have LIVE status", item.status, equalTo("LIVE"))
            }
        }
    }

    @Test
    fun `searchSignatures should handle pagination`() {
        withUser("test-user")

        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .withMaxResults(2)
            .withNextToken("test-token")
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response, notNullValue())
        assertThat(response.items, notNullValue())

        assertThat(response.items.size, equalTo(response.totalCount))
    }

    @Test
    fun `searchSignatures should include lineage and threat classification`() {
        withUser("test-user")

        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .withMaxResults(10)
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response.items, notNullValue())
        if (response.items.isNotEmpty()) {
            val firstItem = response.items[0]

            assertThat(firstItem.signatureId, notNullValue())
            assertThat(firstItem.status, notNullValue())
            assertThat(firstItem.signatureDecisionType, notNullValue())
        }
    }

    @Test
    fun `searchSignatures throws UnauthorizedException when no user context`() {
        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .withMaxResults(10)
            .build()

        val exception = assertThrows<UnauthorizedException> {
            threatStoreActivity.searchSignatures(request)
        }

        assertThat(exception.message, equalTo("Client is unauthorized"))
    }

    @Test
    fun `searchSignatures throws ForbiddenException when user not authorized`() {
        withUser("unauthorized-user")
        whenever(authService.isAuthorized(any<User>(), any<ResourceType>())).thenReturn(false)

        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .withMaxResults(10)
            .build()

        val exception = assertThrows<ForbiddenException> {
            threatStoreActivity.searchSignatures(request)
        }

        assertThat(exception.message, equalTo("Unauthorized to access resource: SIGNATURE_READ"))
    }

    @Test
    fun `searchSignatures handles different status values`() {
        withUser("test-user")

        val statuses = listOf("LIVE", "SHADOW", "LIVE_REFERRAL", "PENDING_MANUAL_REVIEW", "REJECTED")

        statuses.forEach { status ->
            val request = SearchSignaturesRequest.builder()
                .withStatus(status)
                .withMaxResults(10)
                .build()

            val response = threatStoreActivity.searchSignatures(request)
            assertThat("Response should not be null for status $status", response, notNullValue())
            assertThat("Items should not be null for status $status", response.items, notNullValue())

            if (response.items.isNotEmpty()) {
                response.items.forEach { item ->
                    assertThat("All returned items should have status $status", item.status, equalTo(status))
                }
            }
        }
    }

    @Test
    fun `searchSignatures filters results by status correctly`() {
        withUser("test-user")

        val request = SearchSignaturesRequest.builder()
            .withStatus("SHADOW")
            .withMaxResults(10)
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response, notNullValue())
        assertThat(response.items, notNullValue())

        if (response.items.isNotEmpty()) {
            assertThat("Should return exactly 1 SHADOW signature", response.items.size, equalTo(1))
            val shadowItem = response.items[0]
            assertThat("Returned item should have SHADOW status", shadowItem.status, equalTo("SHADOW"))
            assertThat(
                "Should contain malicious-site.net domain",
                shadowItem.content,
                containsString("malicious-site.net"),
            )
        }
    }

    @Test
    fun `searchSignatures returns correct count for each status`() {
        withUser("test-user")

        val expectedCounts = mapOf(
            "LIVE" to 1,
            "SHADOW" to 1,
            "LIVE_REFERRAL" to 1,
            "PENDING_MANUAL_REVIEW" to 1,
            "REJECTED" to 1,
        )

        expectedCounts.forEach { (status, expectedCount) ->
            val request = SearchSignaturesRequest.builder()
                .withStatus(status)
                .withMaxResults(10)
                .build()

            val response = threatStoreActivity.searchSignatures(request)

            assertThat("Response should not be null for status $status", response, notNullValue())
            assertThat(
                "Should return $expectedCount item(s) for status $status",
                response.items.size,
                equalTo(expectedCount),
            )
            assertThat(
                "Total count should match items size for status $status",
                response.totalCount,
                equalTo(expectedCount),
            )
        }
    }

    @Test
    fun `searchSignatures uses default maxResults when not provided`() {
        withUser("test-user")

        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response, notNullValue())
        assertThat(response.items, notNullValue())
    }

    private fun withUser(username: String = "test-user") {
        testUser = User(username, UserType.User)
        apiRequestContext = APIRequestContext(testUser)
    }
}
