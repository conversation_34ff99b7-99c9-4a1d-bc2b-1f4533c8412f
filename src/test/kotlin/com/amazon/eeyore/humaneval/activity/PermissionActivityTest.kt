package com.amazon.eeyore.humaneval.activity

import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humanevaluation.model.GetPermissionsRequest
import com.amazon.eeyore.humanevaluation.model.GetPermissionsResponse
import com.amazon.eeyore.humanevaluation.model.PermissionID
import com.amazon.eeyore.humanevaluation.model.PermissionObject
import com.amazon.eeyore.humanevaluation.model.UnauthorizedException
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.instanceOf
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.mockito.kotlin.whenever

class PermissionActivityTest {
    private val authService: AuthService = mock()
    private val permissionActivity = PermissionActivity(authService)

    private var apiRequestContext: APIRequestContext? = null
    private var testUser = User("test-user", UserType.User)

    @AfterEach
    fun teardown() {
        apiRequestContext?.close()
    }

    @Test
    fun `getPermissions returns empty list when user has no permissions`() {
        withUser()
        whenever(authService.isAuthorized(eq(testUser), any())).thenReturn(false)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(result, equalTo(GetPermissionsResponse.builder().withPermissions(listOf()).build()))
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions returns permissions when user has signature read permission only`() {
        withUser()
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_READ))).thenReturn(true)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_UPDATE))).thenReturn(false)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_OVERRIDE))).thenReturn(false)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(
            result,
            equalTo(
                GetPermissionsResponse.builder()
                    .withPermissions(
                        listOf(PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_READ).build()),
                    )
                    .build(),
            ),
        )
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions returns permissions when user has signature update permission only`() {
        withUser()
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_READ))).thenReturn(false)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_UPDATE))).thenReturn(true)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_OVERRIDE))).thenReturn(false)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(
            result,
            equalTo(
                GetPermissionsResponse.builder()
                    .withPermissions(
                        listOf(PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_UPDATE).build()),
                    )
                    .build(),
            ),
        )
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions returns permissions when user has signature override permission only`() {
        withUser()
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_READ))).thenReturn(false)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_UPDATE))).thenReturn(false)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_OVERRIDE))).thenReturn(true)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(
            result,
            equalTo(
                GetPermissionsResponse.builder()
                    .withPermissions(
                        listOf(PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_OVERRIDE).build()),
                    )
                    .build(),
            ),
        )
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions returns multiple permissions when user has read and update permissions`() {
        withUser()
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_READ))).thenReturn(true)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_UPDATE))).thenReturn(true)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_OVERRIDE))).thenReturn(false)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(
            result,
            equalTo(
                GetPermissionsResponse.builder()
                    .withPermissions(
                        listOf(
                            PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_READ).build(),
                            PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_UPDATE).build(),
                        ),
                    )
                    .build(),
            ),
        )
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions returns all permissions when user has all permissions`() {
        withUser()
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_READ))).thenReturn(true)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_UPDATE))).thenReturn(true)
        whenever(authService.isAuthorized(eq(testUser), eq(ResourceType.SIGNATURE_OVERRIDE))).thenReturn(true)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(
            result,
            equalTo(
                GetPermissionsResponse.builder()
                    .withPermissions(
                        listOf(
                            PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_READ).build(),
                            PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_UPDATE).build(),
                            PermissionObject.builder().withPermissionID(PermissionID.SIGNATURE_OVERRIDE).build(),
                        ),
                    )
                    .build(),
            ),
        )
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions throws UnauthorizedException when there is no user`() {
        val exception = assertThrows<UnauthorizedException> {
            permissionActivity.getPermissions(GetPermissionsRequest())
        }
        assertThat(exception, instanceOf(UnauthorizedException::class.java))
        assertThat(exception.message, equalTo("Client is unauthorized"))
    }

    @Test
    fun `getPermissions works with different user types`() {
        withUser("cognito-user", UserType.Cognito)
        whenever(authService.isAuthorized(any(), eq(ResourceType.SIGNATURE_READ))).thenReturn(true)
        whenever(authService.isAuthorized(any(), eq(ResourceType.SIGNATURE_UPDATE))).thenReturn(false)
        whenever(authService.isAuthorized(any(), eq(ResourceType.SIGNATURE_OVERRIDE))).thenReturn(false)

        val result = permissionActivity.getPermissions(GetPermissionsRequest())

        assertThat(result.permissions.size, equalTo(1))
        assertThat(result.permissions[0].permissionID, equalTo(PermissionID.SIGNATURE_READ))
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(User("cognito-user", UserType.Cognito), it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions logs user information correctly`() {
        withUser("test-user-123")
        whenever(authService.isAuthorized(any(), any())).thenReturn(false)

        permissionActivity.getPermissions(GetPermissionsRequest())

        // Verify that the user context is properly set
        assertThat(APIRequestContext.currentUser()?.name, equalTo("test-user-123"))
        ResourceType.entries.forEach {
            verify(authService).isAuthorized(testUser, it)
        }
        verifyNoMoreInteractions(authService)
    }

    @Test
    fun `getPermissions maps all ResourceType values correctly`() {
        withUser("test-user")

        // Test each ResourceType individually to ensure mapping works
        ResourceType.entries.forEach { targetResourceType ->
            // Reset all mocks for each iteration
            ResourceType.entries.forEach { resourceType ->
                whenever(authService.isAuthorized(eq(testUser), eq(resourceType)))
                    .thenReturn(resourceType == targetResourceType)
            }

            val result = permissionActivity.getPermissions(GetPermissionsRequest())

            assertThat(result.permissions.size, equalTo(1))

            val expectedPermissionID = when (targetResourceType) {
                ResourceType.SIGNATURE_READ -> PermissionID.SIGNATURE_READ
                ResourceType.SIGNATURE_UPDATE -> PermissionID.SIGNATURE_UPDATE
                ResourceType.SIGNATURE_OVERRIDE -> PermissionID.SIGNATURE_OVERRIDE
            }

            assertThat(result.permissions[0].permissionID, equalTo(expectedPermissionID))
        }
    }

    @Test
    fun `getPermissions handles all ResourceType combinations`() {
        withUser("test-user")

        // Test all possible combinations of permissions
        val combinations = listOf(
            // Single permissions
            setOf(ResourceType.SIGNATURE_READ),
            setOf(ResourceType.SIGNATURE_UPDATE),
            setOf(ResourceType.SIGNATURE_OVERRIDE),
            // Pairs
            setOf(ResourceType.SIGNATURE_READ, ResourceType.SIGNATURE_UPDATE),
            setOf(ResourceType.SIGNATURE_READ, ResourceType.SIGNATURE_OVERRIDE),
            setOf(ResourceType.SIGNATURE_UPDATE, ResourceType.SIGNATURE_OVERRIDE),
            // All three
            setOf(ResourceType.SIGNATURE_READ, ResourceType.SIGNATURE_UPDATE, ResourceType.SIGNATURE_OVERRIDE),
            // None
            emptySet<ResourceType>(),
        )

        combinations.forEach { authorizedTypes ->
            // Reset mocks
            ResourceType.entries.forEach { resourceType ->
                whenever(authService.isAuthorized(eq(testUser), eq(resourceType)))
                    .thenReturn(authorizedTypes.contains(resourceType))
            }

            val result = permissionActivity.getPermissions(GetPermissionsRequest())

            assertThat(result.permissions.size, equalTo(authorizedTypes.size))

            val actualPermissionIDs = result.permissions.map { it.permissionID }.toSet()
            val expectedPermissionIDs = authorizedTypes.map { resourceType ->
                when (resourceType) {
                    ResourceType.SIGNATURE_READ -> PermissionID.SIGNATURE_READ
                    ResourceType.SIGNATURE_UPDATE -> PermissionID.SIGNATURE_UPDATE
                    ResourceType.SIGNATURE_OVERRIDE -> PermissionID.SIGNATURE_OVERRIDE
                }
            }.toSet()

            assertThat(actualPermissionIDs, equalTo(expectedPermissionIDs))
        }
    }

    @Test
    fun `getPermissions verifies auth service called for all resource types`() {
        withUser("test-user")
        whenever(authService.isAuthorized(any(), any())).thenReturn(false)

        permissionActivity.getPermissions(GetPermissionsRequest())

        // Verify that isAuthorized is called exactly once for each ResourceType
        ResourceType.entries.forEach { resourceType ->
            verify(authService).isAuthorized(testUser, resourceType)
        }
        verifyNoMoreInteractions(authService)
    }

    private fun withUser(username: String = "test-user", userType: UserType = UserType.User) {
        testUser = User(username, userType)
        apiRequestContext = APIRequestContext(testUser)
    }
}
