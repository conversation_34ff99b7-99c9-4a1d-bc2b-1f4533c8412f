package com.amazon.eeyore.humaneval.activity

import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.guice.TestWoozleServiceModule
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humaneval.testutil.TestHumanEvalServiceConfiguration
import com.amazon.eeyore.humanevaluation.model.SearchSignaturesRequest
import com.google.inject.Guice
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class StatusFilteringVerificationTest {

    private lateinit var threatStoreService: ThreatStoreService
    private lateinit var authService: AuthService
    private lateinit var threatStoreActivity: ThreatStoreActivity
    private lateinit var configuration: TestHumanEvalServiceConfiguration

    private var apiRequestContext: APIRequestContext? = null
    private var testUser = User("test-user", UserType.User)

    @BeforeEach
    fun setUp() {
        val injector = Guice.createInjector(TestWoozleServiceModule())
        threatStoreService = injector.getInstance(ThreatStoreService::class.java)

        authService = mock()
        whenever(authService.isAuthorized(any<User>(), any<ResourceType>())).thenReturn(true)

        configuration = TestHumanEvalServiceConfiguration()

        threatStoreActivity = ThreatStoreActivity(threatStoreService, authService, configuration)

        testUser = User("test-user", UserType.User)
        apiRequestContext = APIRequestContext(testUser)
    }

    @AfterEach
    fun teardown() {
        apiRequestContext?.close()
    }

    @Test
    fun `verify status filtering works correctly for all statuses`() {
        val statusesToTest = listOf("LIVE", "SHADOW", "LIVE_REFERRAL", "PENDING_MANUAL_REVIEW", "REJECTED")

        statusesToTest.forEach { status ->
            println("Testing status: $status")

            val request = SearchSignaturesRequest.builder()
                .withStatus(status)
                .withMaxResults(10)
                .build()

            val response = threatStoreActivity.searchSignatures(request)

            assertThat("Response should not be null for status $status", response, notNullValue())
            assertThat("Items should not be null for status $status", response.items, notNullValue())

            println("Status $status returned ${response.items.size} items")

            if (response.items.isNotEmpty()) {
                response.items.forEach { item ->
                    println("  - Item status: ${item.status}, content: ${item.content}")
                    assertThat("All returned items should have status $status", item.status, equalTo(status))
                }
                assertThat("Should return exactly 1 item for status $status", response.items.size, equalTo(1))
            } else {
                println("  - No items returned for status $status")
            }
        }
    }

    @Test
    fun `verify LIVE status returns correct signature`() {
        val request = SearchSignaturesRequest.builder()
            .withStatus("LIVE")
            .withMaxResults(10)
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response, notNullValue())
        assertThat(response.items.size, equalTo(1))

        val liveItem = response.items[0]
        assertThat(liveItem.status, equalTo("LIVE"))
        assertThat(liveItem.signatureDecisionType, equalTo("THREAT"))
        println("LIVE signature content: ${liveItem.content}")
    }

    @Test
    fun `verify SHADOW status returns correct signature`() {
        val request = SearchSignaturesRequest.builder()
            .withStatus("SHADOW")
            .withMaxResults(10)
            .build()

        val response = threatStoreActivity.searchSignatures(request)

        assertThat(response, notNullValue())
        assertThat(response.items.size, equalTo(1))

        val shadowItem = response.items[0]
        assertThat(shadowItem.status, equalTo("SHADOW"))
        assertThat(shadowItem.signatureDecisionType, equalTo("THREAT"))
        println("SHADOW signature content: ${shadowItem.content}")
    }
}
