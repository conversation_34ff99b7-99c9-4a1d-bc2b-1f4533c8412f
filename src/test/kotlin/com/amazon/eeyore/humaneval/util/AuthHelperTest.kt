package com.amazon.eeyore.humaneval.util

import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humanevaluation.model.ForbiddenException
import com.amazon.eeyore.humanevaluation.model.UnauthorizedException
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

class AuthHelperTest {
    private val authService = mock(AuthService::class.java)
    private val user = User("testUser", UserType.Cognito)

    @AfterEach
    fun cleanupContext() {
        val currentUser = APIRequestContext.currentUser()
        if (currentUser != null) {
            APIRequestContext(currentUser).close()
        }
    }

    @Test
    fun `assertCanAccess throws UnauthorizedException when no user is authenticated`() {
        val exception = assertThrows<UnauthorizedException> {
            AuthHelper.assertCanAccess(authService, ResourceType.SIGNATURE_READ)
        }

        assertThat(exception.message, equalTo("Client is unauthorized"))
    }

    @Test
    fun `assertCanAccess throws ForbiddenException when user doesn't have access`() {
        val context = APIRequestContext(user)

        `when`(authService.isAuthorized(user, ResourceType.SIGNATURE_READ)).thenReturn(false)

        val exception = assertThrows<ForbiddenException> {
            AuthHelper.assertCanAccess(authService, ResourceType.SIGNATURE_READ)
        }

        assertThat(exception.message, equalTo("Access denied to resource: SIGNATURE_READ"))

        context.close()
    }

    @Test
    fun `assertCanAccess succeeds when user has access`() {
        val context = APIRequestContext(user)

        `when`(authService.isAuthorized(user, ResourceType.SIGNATURE_READ)).thenReturn(true)

        AuthHelper.assertCanAccess(authService, ResourceType.SIGNATURE_READ)

        verify(authService).isAuthorized(user, ResourceType.SIGNATURE_READ)

        context.close()
    }

    @Test
    fun `assertCanReadSignature calls assertCanAccess with SIGNATURE_READ`() {
        val context = APIRequestContext(user)

        `when`(authService.isAuthorized(user, ResourceType.SIGNATURE_READ)).thenReturn(true)

        AuthHelper.assertCanReadSignature(authService)

        verify(authService).isAuthorized(user, ResourceType.SIGNATURE_READ)

        context.close()
    }

    @Test
    fun `assertCanUpdateSignature calls assertCanAccess with SIGNATURE_UPDATE`() {
        val context = APIRequestContext(user)

        `when`(authService.isAuthorized(user, ResourceType.SIGNATURE_UPDATE)).thenReturn(true)

        AuthHelper.assertCanUpdateSignature(authService)

        verify(authService).isAuthorized(user, ResourceType.SIGNATURE_UPDATE)

        context.close()
    }

    @Test
    fun `assertCanOverrideSignature calls assertCanAccess with SIGNATURE_OVERRIDE`() {
        val context = APIRequestContext(user)

        `when`(authService.isAuthorized(user, ResourceType.SIGNATURE_OVERRIDE)).thenReturn(true)

        AuthHelper.assertCanOverrideSignature(authService)

        verify(authService).isAuthorized(user, ResourceType.SIGNATURE_OVERRIDE)

        context.close()
    }
}
