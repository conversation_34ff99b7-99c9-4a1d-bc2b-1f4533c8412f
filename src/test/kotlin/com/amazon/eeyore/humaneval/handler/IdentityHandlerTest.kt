package com.amazon.eeyore.humaneval.handler

import com.amazon.coral.service.HttpConstant
import com.amazon.coral.service.Job
import com.amazon.coral.service.Request
import com.amazon.coral.service.http.HttpHeaders
import com.amazon.eeyore.humaneval.api.APIRequestContext
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.nullValue
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

// Constants from IdentityParser
private const val HTTP_HEADER_COGNITO_AUTHENTICATION_PROVIDER = "x-eeyore-cognito-configuration-provider"
private const val HTTP_HEADER_USER_ARN = "x-eeyore-user-arn"

class IdentityHandlerTest {
    private val identityHandler = IdentityHandler()

    @AfterEach
    fun cleanupContext() {
        val currentUser = APIRequestContext.currentUser()
        if (currentUser != null) {
            APIRequestContext(currentUser).close()
        }
    }

    @Test
    fun `IdentityHandler can parse Cognito Authentication`() {
        val request = requestWithHeaders(
            mapOf(
                HTTP_HEADER_COGNITO_AUTHENTICATION_PROVIDER to "arn:aws:iam::123412341234:oidc-provider:test-user",
            ),
        )
        val job = mock<Job> {
            whenever(it.request).thenReturn(request)
        }

        identityHandler.before(job)

        val currentUser = APIRequestContext.currentUser()
        assertThat(currentUser?.name, equalTo("test-user"))
        assertThat(currentUser?.type, equalTo(UserType.Cognito))
        verify(job).setAttribute(eq(IDENTITY_CONTEXT), any())
    }

    @Test
    fun `IdentityHandler can parse User ARN Authentication`() {
        val request = requestWithHeaders(mapOf(HTTP_HEADER_USER_ARN to "arn:aws:iam::123412341234:user/iam-user"))
        val job = mock<Job> {
            whenever(it.request).thenReturn(request)
        }

        identityHandler.before(job)

        val currentUser = APIRequestContext.currentUser()
        assertThat(currentUser?.name, equalTo("iam-user"))
        assertThat(currentUser?.type, equalTo(UserType.User))
        verify(job).setAttribute(eq(IDENTITY_CONTEXT), any())
    }

    @Test
    fun `IdentityHandler does not set context on missing authentication`() {
        val request = requestWithHeaders(mapOf())
        val job = mock<Job> {
            whenever(it.request).thenReturn(request)
        }

        identityHandler.before(job)

        assertThat(APIRequestContext.currentUser(), nullValue())
        verify(job, never()).setAttribute(any(), any<APIRequestContext>())
    }

    @Test
    fun `IdentityHandler cleans up after the job`() {
        val apiContext = mock<APIRequestContext>()
        val job = mock<Job> {
            whenever(it.getAttribute(IDENTITY_CONTEXT)).thenReturn(apiContext)
        }

        identityHandler.after(job)

        verify(apiContext).close()
    }

    @Test
    fun `IdentityHandler cleanup does nothing when theres no attribute`() {
        val job = mock<Job>()

        identityHandler.after(job)

        // Nothing to assert, but by virtue of not throwing an exception we know the scenario is correct.
    }

    private fun requestWithHeaders(headers: Map<String, String>): Request {
        val httpHeaders = mock<HttpHeaders>()
        headers.forEach {
            whenever(httpHeaders.getValue(it.key)).thenReturn(it.value)
            whenever(httpHeaders.headerNames).thenReturn(headers.keys)
        }

        return mock<Request> {
            whenever(it.getAttribute(HttpConstant.HTTP_HEADERS)).thenReturn(httpHeaders)
        }
    }
}
