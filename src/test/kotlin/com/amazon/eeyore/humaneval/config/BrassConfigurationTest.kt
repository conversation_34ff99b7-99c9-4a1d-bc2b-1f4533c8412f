package com.amazon.eeyore.humaneval.config

import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test

class BrassConfigurationTest {

    private class TestBrassConfiguration(override val brassCoralQualifier: String) : BrassConfiguration

    @Test
    fun `BrassConfiguration interface can be implemented`() {
        val expectedQualifier = "test-qualifier"

        val config = TestBrassConfiguration(expectedQualifier)

        assertThat(config.brassCoralQualifier, equalTo(expectedQualifier))
    }
}
