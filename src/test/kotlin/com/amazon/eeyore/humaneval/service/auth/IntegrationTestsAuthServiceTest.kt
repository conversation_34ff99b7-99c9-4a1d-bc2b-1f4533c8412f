package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test

class IntegrationTestsAuthServiceTest {
    private val service = IntegrationTestsAuthService()

    @Test
    fun `always returns true for authorization checks`() {
        val user = User("testUser", UserType.Test)

        // Test with different resource types
        assertThat(service.isAuthorized(user, ResourceType.SIGNATURE_READ), equalTo(true))
        assertThat(service.isAuthorized(user, ResourceType.SIGNATURE_UPDATE), equalTo(true))
        assertThat(service.isAuthorized(user, ResourceType.SIGNATURE_OVERRIDE), equalTo(true))
    }
}
