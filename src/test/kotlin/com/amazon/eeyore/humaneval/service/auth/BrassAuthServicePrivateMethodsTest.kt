package com.amazon.eeyore.humaneval.service.auth

import com.amazon.brass.coral.calls.BrassServiceClient
import com.amazon.eeyore.humaneval.model.ResourceType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.nullValue
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import java.lang.reflect.Method

class BrassAuthServicePrivateMethodsTest {
    private val brassServiceClient = mock(BrassServiceClient::class.java)

    private val bindleLockMapping = mapOf(
        ResourceType.SIGNATURE_READ to "test-signature-read-lock",
        ResourceType.SIGNATURE_UPDATE to "test-signature-update-lock",
        ResourceType.SIGNATURE_OVERRIDE to "test-signature-override-lock",
    )

    private val emptyBindleLockMapping = emptyMap<ResourceType, String>()

    private val authService = BrassAuthService(brassServiceClient, bindleLockMapping)
    private val emptyAuthService = BrassAuthService(brassServiceClient, emptyBindleLockMapping)

    private fun getPrivateMethod(methodName: String, vararg parameterTypes: Class<*>): Method {
        val method = BrassAuthService::class.java.getDeclaredMethod(methodName, *parameterTypes)
        method.isAccessible = true
        return method
    }

    @Test
    fun `resourceToBindleLockName returns correct lock name for known resource type`() {
        val method = getPrivateMethod("resourceToBindleLockName", ResourceType::class.java)

        val result = method.invoke(authService, ResourceType.SIGNATURE_READ) as String?

        assertThat(result, equalTo("test-signature-read-lock"))
    }

    @Test
    fun `resourceToBindleLockName returns null for unknown resource type`() {
        val method = getPrivateMethod("resourceToBindleLockName", ResourceType::class.java)

        val result = method.invoke(emptyAuthService, ResourceType.SIGNATURE_READ) as String?

        assertThat(result, nullValue())
    }
}
