package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.inOrder
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.whenever
import org.mockito.verification.VerificationMode

class SystemsIntegrationTestAuthServiceTest {

    private val userAuthServiceMock: AuthService = mock()
    private val testAuthServiceMock: AuthService = mock()
    private val inOrder = inOrder(userAuthServiceMock, testAuthServiceMock)
    private val authService = SystemsIntegrationTestAuthService(userAuthServiceMock, testAuthServiceMock)

    @AfterEach
    fun `no more interactions`() {
        inOrder.verifyNoMoreInteractions()
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `uses user auth service for cognito users`(expectedResult: Boolean) {
        val user = User("user", UserType.Cognito)
        val resourceType = ResourceType.SIGNATURE_READ
        whenever(userAuthServiceMock.isAuthorized(user, resourceType)).thenReturn(expectedResult)

        val result = authService.isAuthorized(user, resourceType)
        assertThat(result, equalTo(expectedResult))

        verify(userAuthServiceMock).isAuthorized(user, resourceType)
        verify(testAuthServiceMock, never()).isAuthorized(any(), any())
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `uses test auth service for test users`(expectedResult: Boolean) {
        val user = User("user", UserType.Test)
        val resourceType = ResourceType.SIGNATURE_READ
        whenever(testAuthServiceMock.isAuthorized(user, resourceType)).thenReturn(expectedResult)

        val result = authService.isAuthorized(user, resourceType)
        assertThat(result, equalTo(expectedResult))

        verify(userAuthServiceMock, never()).isAuthorized(any(), any())
        verify(testAuthServiceMock).isAuthorized(user, resourceType)
    }

    @Test
    fun `returns false for non test and non cognito users without calling any auth service`() {
        val resourceType = ResourceType.SIGNATURE_READ
        whenever(userAuthServiceMock.isAuthorized(any(), any())).thenReturn(true)
        whenever(testAuthServiceMock.isAuthorized(any(), any())).thenReturn(true)

        val unsupportedTypes = UserType.entries.filterNot { it == UserType.Cognito || it == UserType.Test }

        val expectedResults = mapOf(
            UserType.Role to false,
            UserType.User to false,
            UserType.AssumedRole to false,
        )

        val results = unsupportedTypes.associateWith { type ->
            val user = User("user", type)
            authService.isAuthorized(user, resourceType)
        }

        assertThat(results, equalTo(expectedResults))
    }

    private fun <T> verify(
        mock: T,
        verificationMode: VerificationMode = times(1),
    ) = inOrder.verify(mock, verificationMode)
}
