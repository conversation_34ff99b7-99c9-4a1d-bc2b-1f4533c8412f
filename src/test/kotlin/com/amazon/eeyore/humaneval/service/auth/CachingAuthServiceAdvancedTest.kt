package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

class CachingAuthServiceAdvancedTest {
    private val delegateAuthService = Mockito.mock(AuthService::class.java)
    private val user1 = User("user1", UserType.Cognito)
    private val user2 = User("user2", UserType.Cognito)
    private val resourceType = ResourceType.SIGNATURE_READ

    @Test
    fun `cache stores different entries for different users`() {
        val cachingAuthService = CachingAuthService(delegateAuthService)
        `when`(delegateAuthService.isAuthorized(user1, resourceType)).thenReturn(true)
        `when`(delegateAuthService.isAuthorized(user2, resourceType)).thenReturn(false)

        val result1 = cachingAuthService.isAuthorized(user1, resourceType)
        val result2 = cachingAuthService.isAuthorized(user2, resourceType)

        assertThat(result1, equalTo(true))
        assertThat(result2, equalTo(false))
        verify(delegateAuthService).isAuthorized(user1, resourceType)
        verify(delegateAuthService).isAuthorized(user2, resourceType)
    }

    @Test
    fun `cache stores different entries for different resource types`() {
        val cachingAuthService = CachingAuthService(delegateAuthService)
        val resourceType1 = ResourceType.SIGNATURE_READ
        val resourceType2 = ResourceType.SIGNATURE_UPDATE
        `when`(delegateAuthService.isAuthorized(user1, resourceType1)).thenReturn(true)
        `when`(delegateAuthService.isAuthorized(user1, resourceType2)).thenReturn(false)

        val result1 = cachingAuthService.isAuthorized(user1, resourceType1)
        val result2 = cachingAuthService.isAuthorized(user1, resourceType2)

        assertThat(result1, equalTo(true))
        assertThat(result2, equalTo(false))
        verify(delegateAuthService).isAuthorized(user1, resourceType1)
        verify(delegateAuthService).isAuthorized(user1, resourceType2)
    }

    @Test
    fun `cache reuses entries for same user and resource type`() {
        val cachingAuthService = CachingAuthService(delegateAuthService)
        `when`(delegateAuthService.isAuthorized(user1, resourceType)).thenReturn(true)

        cachingAuthService.isAuthorized(user1, resourceType)
        cachingAuthService.isAuthorized(user1, resourceType)
        cachingAuthService.isAuthorized(user1, resourceType)

        verify(delegateAuthService, times(1)).isAuthorized(user1, resourceType)
    }

    @Test
    fun `cache handles exceptions from delegate`() {
        val cachingAuthService = CachingAuthService(delegateAuthService)
        val exception = RuntimeException("Test exception")
        `when`(delegateAuthService.isAuthorized(user1, resourceType)).thenThrow(exception)

        val thrown1 = assertThrows<RuntimeException> {
            cachingAuthService.isAuthorized(user1, resourceType)
        }
        assertThat(thrown1.message, equalTo("Test exception"))

        val thrown2 = assertThrows<RuntimeException> {
            cachingAuthService.isAuthorized(user1, resourceType)
        }
        assertThat(thrown2.message, equalTo("Test exception"))

        verify(delegateAuthService, times(2)).isAuthorized(user1, resourceType)
    }
}
