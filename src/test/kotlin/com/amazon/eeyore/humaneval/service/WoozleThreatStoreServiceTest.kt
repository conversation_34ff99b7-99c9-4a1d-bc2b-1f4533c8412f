package com.amazon.eeyore.humaneval.service

import com.amazonaws.services.woozlethreatstore.WoozleThreatStore
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.sameInstance
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class WoozleThreatStoreServiceTest {
    private val mockClient: WoozleThreatStore = mock()
    private val service = WoozleThreatStoreService(mockClient)

    @Test
    fun `createSignature delegates to client correctly`() {
        val request = mock<CreateSignatureRequest>()
        val expectedResult = mock<CreateSignatureResult>()
        whenever(mockClient.createSignature(request)).thenReturn(expectedResult)

        val result = service.createSignature(request)

        assertThat(result, sameInstance(expectedResult))
        verify(mockClient).createSignature(request)
    }

    @Test
    fun `createSignatures delegates to client for each request`() {
        val request1 = mock<CreateSignatureRequest>()
        val request2 = mock<CreateSignatureRequest>()
        val request3 = mock<CreateSignatureRequest>()
        val requests = listOf(request1, request2, request3)

        val result1 = mock<CreateSignatureResult>()
        val result2 = mock<CreateSignatureResult>()
        val result3 = mock<CreateSignatureResult>()

        whenever(mockClient.createSignature(request1)).thenReturn(result1)
        whenever(mockClient.createSignature(request2)).thenReturn(result2)
        whenever(mockClient.createSignature(request3)).thenReturn(result3)

        val results = service.createSignatures(requests)

        assertThat(results.size, equalTo(3))
        assertThat(results[0], sameInstance(result1))
        assertThat(results[1], sameInstance(result2))
        assertThat(results[2], sameInstance(result3))
        verify(mockClient, times(3)).createSignature(any())
    }

    @Test
    fun `createSignatures handles empty list`() {
        val results = service.createSignatures(emptyList())

        assertThat(results.size, equalTo(0))
        verify(mockClient, times(0)).createSignature(any())
    }

    @Test
    fun `validateSignature delegates to client correctly`() {
        val request = mock<ValidateSignatureRequest>()
        val expectedResult = mock<ValidateSignatureResult>()
        whenever(mockClient.validateSignature(request)).thenReturn(expectedResult)

        val result = service.validateSignature(request)

        assertThat(result, sameInstance(expectedResult))
        verify(mockClient).validateSignature(request)
    }

    @Test
    fun `validateSignatures delegates to client for each request`() {
        val request1 = mock<ValidateSignatureRequest>()
        val request2 = mock<ValidateSignatureRequest>()
        val requests = listOf(request1, request2)

        val result1 = mock<ValidateSignatureResult>()
        val result2 = mock<ValidateSignatureResult>()

        whenever(mockClient.validateSignature(request1)).thenReturn(result1)
        whenever(mockClient.validateSignature(request2)).thenReturn(result2)

        val results = service.validateSignatures(requests)

        assertThat(results.size, equalTo(2))
        assertThat(results[0], sameInstance(result1))
        assertThat(results[1], sameInstance(result2))
        verify(mockClient, times(2)).validateSignature(any())
    }

    @Test
    fun `validateSignatures handles empty list`() {
        val results = service.validateSignatures(emptyList())

        assertThat(results.size, equalTo(0))
        verify(mockClient, times(0)).validateSignature(any())
    }

    @Test
    fun `createSignature propagates client exceptions`() {
        val request = mock<CreateSignatureRequest>()
        val expectedException = RuntimeException("Client error")
        whenever(mockClient.createSignature(request)).thenThrow(expectedException)

        val exception = assertThrows<RuntimeException> {
            service.createSignature(request)
        }

        assertThat(exception, sameInstance(expectedException))
        verify(mockClient).createSignature(request)
    }

    @Test
    fun `validateSignature propagates client exceptions`() {
        val request = mock<ValidateSignatureRequest>()
        val expectedException = RuntimeException("Validation error")
        whenever(mockClient.validateSignature(request)).thenThrow(expectedException)

        val exception = assertThrows<RuntimeException> {
            service.validateSignature(request)
        }

        assertThat(exception, sameInstance(expectedException))
        verify(mockClient).validateSignature(request)
    }

    @Test
    fun `createSignatures handles partial failures`() {
        val request1 = mock<CreateSignatureRequest>()
        val request2 = mock<CreateSignatureRequest>()
        val request3 = mock<CreateSignatureRequest>()
        val requests = listOf(request1, request2, request3)

        val result1 = mock<CreateSignatureResult>()
        val result3 = mock<CreateSignatureResult>()

        whenever(mockClient.createSignature(request1)).thenReturn(result1)
        whenever(mockClient.createSignature(request2)).thenThrow(RuntimeException("Failed"))
        whenever(mockClient.createSignature(request3)).thenReturn(result3)

        val exception = assertThrows<RuntimeException> {
            service.createSignatures(requests)
        }

        assertThat(exception.message, equalTo("Failed"))
        verify(mockClient).createSignature(request1)
        verify(mockClient).createSignature(request2)
        // request3 should not be called due to failure on request2
    }
}
