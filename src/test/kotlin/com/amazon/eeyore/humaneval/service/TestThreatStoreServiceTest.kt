package com.amazon.eeyore.humaneval.service

import com.amazon.eeyore.humaneval.guice.TestThreatStoreService
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureContent
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureContent
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.instanceOf
import org.hamcrest.CoreMatchers.notNullValue
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class TestThreatStoreServiceTest {
    private val service = TestThreatStoreService()

    @Test
    fun `createSignature generates valid signature ID`() {
        val request = createMockCreateSignatureRequest("example.com")

        val result = service.createSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(CreateSignatureResult::class.java))
    }

    @Test
    fun `createSignature handles null content gracefully`() {
        val request = mock<CreateSignatureRequest>()
        whenever(request.content).thenReturn(null)

        val result = service.createSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(CreateSignatureResult::class.java))
    }

    @Test
    fun `createSignature handles null domain signature content`() {
        val request = mock<CreateSignatureRequest>()
        val content = mock<CreateSignatureContent>()
        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(null)

        val result = service.createSignature(request)

        assertThat(result, notNullValue())
    }

    @Test
    fun `createSignatures processes multiple requests correctly`() {
        val requests = listOf(
            createMockCreateSignatureRequest("domain1.com"),
            createMockCreateSignatureRequest("domain2.com"),
            createMockCreateSignatureRequest("domain3.com"),
        )

        val results = service.createSignatures(requests)

        assertThat(results.size, equalTo(3))
        results.forEach { result ->
            assertThat(result, notNullValue())
            assertThat(
                result,
                instanceOf(CreateSignatureResult::class.java),
            )
        }
    }

    @Test
    fun `createSignatures handles empty list`() {
        val results = service.createSignatures(emptyList())

        assertThat(results.size, equalTo(0))
    }

    @ParameterizedTest
    @ValueSource(strings = ["example.com", "test.org", "valid-domain.net", "subdomain.example.com"])
    fun `validateSignature approves normal domains`(domain: String) {
        val request = createMockValidateSignatureRequest(domain)

        val result = service.validateSignature(request)

        assertThat(result, notNullValue())
        assertThat(
            result,
            instanceOf(ValidateSignatureResult::class.java),
        )
    }

    @ParameterizedTest
    @ValueSource(strings = ["malicious.com", "spam.example.com", "test-malicious-domain.org"])
    fun `validateSignature rejects malicious domains`(domain: String) {
        val request = createMockValidateSignatureRequest(domain)

        val result = service.validateSignature(request)

        assertThat(result, notNullValue())
        assertThat(
            result,
            instanceOf(ValidateSignatureResult::class.java),
        )
    }

    @ParameterizedTest
    @ValueSource(strings = ["spam.com", "test-spam.org", "spammy-domain.net"])
    fun `validateSignature rejects spam domains`(domain: String) {
        val request = createMockValidateSignatureRequest(domain)

        val result = service.validateSignature(request)

        assertThat(result, notNullValue())
    }

    @Test
    fun `validateSignature rejects test-reject domains`() {
        val request = createMockValidateSignatureRequest("test-reject.com")

        val result = service.validateSignature(request)

        assertThat(result, notNullValue())
    }

    @Test
    fun `validateSignature rejects blank domains`() {
        val request = createMockValidateSignatureRequest("")

        val result = service.validateSignature(request)

        assertThat(result, notNullValue())
    }

    @Test
    fun `validateSignature handles null content gracefully`() {
        val request = mock<ValidateSignatureRequest>()
        whenever(request.content).thenReturn(null)

        val result = service.validateSignature(request)

        assertThat(result, notNullValue())
    }

    @Test
    fun `validateSignatures processes multiple requests correctly`() {
        val requests = listOf(
            createMockValidateSignatureRequest("good.com"),
            createMockValidateSignatureRequest("malicious.com"),
            createMockValidateSignatureRequest("spam.com"),
        )

        val results = service.validateSignatures(requests)

        assertThat(results.size, equalTo(3))
        results.forEach { result ->
            assertThat(result, notNullValue())
            assertThat(
                result,
                instanceOf(ValidateSignatureResult::class.java),
            )
        }
    }

    @Test
    fun `validateSignatures handles empty list`() {
        val results = service.validateSignatures(emptyList())

        assertThat(results.size, equalTo(0))
    }

    @Test
    fun `service implements ThreatStoreService interface correctly`() {
        assertThat(service, instanceOf(ThreatStoreService::class.java))
    }

    @Test
    fun `searchSignatures returns valid result`() {
        val request = createMockSearchSignaturesRequest("LIVE", 10, null)

        val result = service.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
    }

    @ParameterizedTest
    @ValueSource(strings = ["LIVE", "SHADOW", "LIVE_REFERRAL", "PENDING_MANUAL_REVIEW", "REJECTED"])
    fun `searchSignatures filters by status correctly`(status: String) {
        val request = createMockSearchSignaturesRequest(status, 10, null)

        val result = service.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
        result.items.forEach { item ->
            assertThat(item.status, equalTo(status))
        }
    }

    @Test
    fun `searchSignatures handles pagination with nextToken`() {
        val request = createMockSearchSignaturesRequest("LIVE", 5, "test-token")

        val result = service.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
    }

    @Test
    fun `searchSignatures respects maxResults limit`() {
        val request = createMockSearchSignaturesRequest("LIVE", 1, null)

        val result = service.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
        assertThat(result.items.size <= 1, equalTo(true))
    }

    @Test
    fun `searchSignatures handles edge cases`() {
        val request1 = createMockSearchSignaturesRequest("LIVE", 0, null)
        val result1 = service.searchSignatures(request1)
        assertThat(result1, notNullValue())

        val request2 = createMockSearchSignaturesRequest("LIVE", 1000, null)
        val result2 = service.searchSignatures(request2)
        assertThat(result2, notNullValue())
    }

    private fun createMockCreateSignatureRequest(domain: String): CreateSignatureRequest {
        val request = mock<CreateSignatureRequest>()
        val content = mock<CreateSignatureContent>()
        val domainContent = mock<DomainSignatureContent>()

        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(domainContent)
        whenever(domainContent.domain).thenReturn(domain)

        return request
    }

    private fun createMockValidateSignatureRequest(domain: String): ValidateSignatureRequest {
        val request = mock<ValidateSignatureRequest>()
        val content = mock<ValidateSignatureContent>()
        val domainContent = mock<DomainSignatureContent>()

        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(domainContent)
        whenever(domainContent.domain).thenReturn(domain)

        return request
    }

    private fun createMockSearchSignaturesRequest(
        status: String,
        maxResults: Int,
        nextToken: String?,
    ): SearchSignaturesRequest {
        val request = mock<SearchSignaturesRequest>()
        whenever(request.status).thenReturn(status)
        whenever(request.maxResults).thenReturn(maxResults)
        whenever(request.nextToken).thenReturn(nextToken)
        return request
    }
}
