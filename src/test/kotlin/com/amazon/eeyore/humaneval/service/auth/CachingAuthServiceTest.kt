package com.amazon.eeyore.humaneval.service.auth

import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.mockito.Mockito.verify

class CachingAuthServiceTest {
    private val user = User("testUser", UserType.Cognito)
    private val delegateAuthService = Mockito.mock(AuthService::class.java)
    private val cachingAuthService = CachingAuthService(delegateAuthService)

    @Test
    fun `isAuthorized returns result from delegate`() {
        val resourceType = ResourceType.SIGNATURE_READ
        Mockito.`when`(delegateAuthService.isAuthorized(user, resourceType))
            .thenReturn(true)

        val result = cachingAuthService.isAuthorized(user, resourceType)

        assertThat(result, equalTo(true))
    }

    @Test
    fun `isAuthorized caches results`() {
        val resourceType = ResourceType.SIGNATURE_READ
        Mockito.`when`(delegateAuthService.isAuthorized(user, resourceType))
            .thenReturn(true)

        cachingAuthService.isAuthorized(user, resourceType)
        cachingAuthService.isAuthorized(user, resourceType)
        cachingAuthService.isAuthorized(user, resourceType)

        verify(delegateAuthService, times(1)).isAuthorized(user, resourceType)
    }

    @Test
    fun `isAuthorized uses separate cache entries for different resource types`() {
        val readResourceType = ResourceType.SIGNATURE_READ
        val updateResourceType = ResourceType.SIGNATURE_UPDATE

        Mockito.`when`(delegateAuthService.isAuthorized(user, readResourceType))
            .thenReturn(true)
        Mockito.`when`(delegateAuthService.isAuthorized(user, updateResourceType))
            .thenReturn(false)

        val readResult = cachingAuthService.isAuthorized(user, readResourceType)
        val updateResult = cachingAuthService.isAuthorized(user, updateResourceType)

        assertThat(readResult, equalTo(true))
        assertThat(updateResult, equalTo(false))
        verify(delegateAuthService, times(1)).isAuthorized(user, readResourceType)
        verify(delegateAuthService, times(1)).isAuthorized(user, updateResourceType)
    }

    @Test
    fun `isAuthorized propagates exceptions from delegate`() {
        val resourceType = ResourceType.SIGNATURE_READ
        val exception = RuntimeException("Test exception")
        Mockito.`when`(delegateAuthService.isAuthorized(user, resourceType))
            .thenThrow(exception)

        val thrownException = assertThrows<RuntimeException> {
            cachingAuthService.isAuthorized(user, resourceType)
        }
        assertThat(thrownException.message, equalTo("Test exception"))
    }
}
