package com.amazon.eeyore.humaneval.service.auth

import com.amazon.brass.coral.calls.BrassServiceClient
import com.amazon.brass.coral.calls.IsAuthorizedResponse
import com.amazon.eeyore.humaneval.identity.User
import com.amazon.eeyore.humaneval.identity.UserType
import com.amazon.eeyore.humaneval.model.ResourceType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.mock
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.`when`

class BrassAuthServiceTest {
    private val bindleLockMapping =
        mapOf(
            ResourceType.SIGNATURE_READ to "test-signature-read-lock",
            ResourceType.SIGNATURE_UPDATE to "test-signature-update-lock",
            ResourceType.SIGNATURE_OVERRIDE to "test-signature-override-lock",
        )

    private val brassServiceClient = mock(BrassServiceClient::class.java)
    private val authService = BrassAuthService(brassServiceClient, bindleLockMapping)

    @Test
    fun `isAuthorized returns true when BRASS authorizes`() {
        val user = User("testUser", UserType.Cognito)
        val resourceType = ResourceType.SIGNATURE_READ
        val response = mock(IsAuthorizedResponse::class.java)
        `when`(response.isAuthorized).thenReturn(true)
        `when`(brassServiceClient.callIsAuthorized(any())).thenReturn(response)

        val result = authService.isAuthorized(user, resourceType)

        assertThat(result, equalTo(true))
    }

    @Test
    fun `isAuthorized returns false when BRASS denies`() {
        val user = User("testUser", UserType.Cognito)
        val resourceType = ResourceType.SIGNATURE_UPDATE
        val response = mock(IsAuthorizedResponse::class.java)
        `when`(response.isAuthorized).thenReturn(false)
        `when`(brassServiceClient.callIsAuthorized(any())).thenReturn(response)

        val result = authService.isAuthorized(user, resourceType)

        assertThat(result, equalTo(false))
    }

    @Test
    fun `isAuthorized returns false for non-Cognito users`() {
        val user = User("testUser", UserType.Test)
        val resourceType = ResourceType.SIGNATURE_READ

        val result = authService.isAuthorized(user, resourceType)

        verifyNoInteractions(brassServiceClient)
        assertThat(result, equalTo(false))
    }

    @Test
    fun `isAuthorized returns false when bindle lock not found`() {
        val user = User("testUser", UserType.Cognito)
        val emptyAuthService = BrassAuthService(
            brassServiceClient,
            emptyMap(),
        )

        val result = emptyAuthService.isAuthorized(user, ResourceType.SIGNATURE_READ)

        assertThat(result, equalTo(false))
    }
}
