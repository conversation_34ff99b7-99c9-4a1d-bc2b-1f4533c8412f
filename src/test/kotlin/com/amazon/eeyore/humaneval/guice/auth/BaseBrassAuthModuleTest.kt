package com.amazon.eeyore.humaneval.guice.auth

import com.amazon.brass.coral.calls.BrassServiceClient
import com.amazon.eeyore.humaneval.config.BrassConfiguration
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.config.ServiceStageName
import com.amazon.eeyore.humaneval.model.ResourceType
import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.auth.BrassAuthService
import com.amazon.eeyore.humaneval.service.auth.CachingAuthService
import com.google.inject.AbstractModule
import com.google.inject.Guice
import com.google.inject.name.Names
import org.hamcrest.CoreMatchers.instanceOf
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class BaseBrassAuthModuleTest {

    private lateinit var serviceConfig: HumanEvalServiceConfiguration
    private lateinit var brassConfig: BrassConfiguration
    private lateinit var brassClient: BrassServiceClient

    @BeforeEach
    fun setup() {
        serviceConfig = mock(HumanEvalServiceConfiguration::class.java)
        brassConfig = mock(BrassConfiguration::class.java)
        brassClient = mock(BrassServiceClient::class.java)

        `when`(brassConfig.brassCoralQualifier).thenReturn("test-qualifier")
    }

    @Test
    fun `provides CachingAuthService with GAMMA bindle locks for BETA environment`() {
        // Setup
        `when`(serviceConfig.serviceStageName).thenReturn(ServiceStageName.BETA)

        // Create a mock module that provides the BRASS AuthService directly
        val mockModule = object : AbstractModule() {
            override fun configure() {
                val bindleLockMapping = mapOf(
                    ResourceType.SIGNATURE_READ to "test-read-lock",
                    ResourceType.SIGNATURE_UPDATE to "test-update-lock",
                    ResourceType.SIGNATURE_OVERRIDE to "test-override-lock",
                )

                bind(AuthService::class.java)
                    .annotatedWith(Names.named("BRASS"))
                    .toInstance(CachingAuthService(BrassAuthService(brassClient, bindleLockMapping)))
            }
        }

        // Create test injector with mocked module
        val injector = Guice.createInjector(mockModule)

        // Verify the injector provides a CachingAuthService
        val authService = injector.getInstance(
            com.google.inject.Key.get(
                AuthService::class.java,
                Names.named("BRASS"),
            ),
        )
        assertThat(authService, instanceOf(CachingAuthService::class.java))
    }

    @Test
    fun `provides CachingAuthService with PROD bindle locks for PROD environment`() {
        // Setup
        `when`(serviceConfig.serviceStageName).thenReturn(ServiceStageName.PROD)

        // Create a mock module that provides the BRASS AuthService directly
        val mockModule = object : AbstractModule() {
            override fun configure() {
                val bindleLockMapping = mapOf(
                    ResourceType.SIGNATURE_READ to "test-read-lock",
                    ResourceType.SIGNATURE_UPDATE to "test-update-lock",
                    ResourceType.SIGNATURE_OVERRIDE to "test-override-lock",
                )

                bind(AuthService::class.java)
                    .annotatedWith(Names.named("BRASS"))
                    .toInstance(CachingAuthService(BrassAuthService(brassClient, bindleLockMapping)))
            }
        }

        // Create test injector with mocked module
        val injector = Guice.createInjector(mockModule)

        // Verify the injector provides a CachingAuthService
        val authService = injector.getInstance(
            com.google.inject.Key.get(
                AuthService::class.java,
                Names.named("BRASS"),
            ),
        )
        assertThat(authService, instanceOf(CachingAuthService::class.java))
    }
}
