package com.amazon.eeyore.humaneval.guice

import com.amazon.coral.metrics.MetricsFactory
import com.amazon.eeyore.humaneval.service.BobcatService
import com.amazon.eeyore.humaneval.testutil.TestHumanEvalServiceConfiguration
import com.google.inject.AbstractModule
import com.google.inject.Guice
import com.google.inject.Provides
import com.google.inject.Singleton
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class CoralServerModuleTest {
    @Test
    fun `can provide instance of BobcatService`() {
        val injector = Guice.createInjector(
            TestDependenciesModule(),
            CoralServerModule(TestHumanEvalServiceConfiguration()),
        )

        injector.getInstance(BobcatService::class.java)
    }

    private class TestDependenciesModule : AbstractModule() {
        private val metricsFactory: MetricsFactory = mock()

        @Singleton
        @Provides
        fun metricsFactory() = metricsFactory
    }
}
