package com.amazon.eeyore.humaneval.guice

import com.amazon.eeyore.humaneval.config.ServiceStageName
import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazon.eeyore.humaneval.testutil.TestHumanEvalServiceConfiguration
import com.google.inject.AbstractModule
import com.google.inject.Guice
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.instanceOf
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Test

class HumanEvalServerModuleTest {

    @Test
    fun `HumanEvalServerModule can be instantiated`() {
        val configuration = TestHumanEvalServiceConfiguration()
        val serverModule = HumanEvalServerModule(configuration)

        assertDoesNotThrow { serverModule.toString() }
    }

    @Test
    fun `ALPHA stage uses correct modules`() {
        val configuration = TestHumanEvalServiceConfiguration(serviceStageName = ServiceStageName.ALPHA)
        val serverModule = HumanEvalServerModule(configuration)
        assertDoesNotThrow { serverModule.toString() }
    }

    @Test
    fun `BETA stage uses correct modules`() {
        val configuration = TestHumanEvalServiceConfiguration(serviceStageName = ServiceStageName.BETA)
        val serverModule = HumanEvalServerModule(configuration)
        assertDoesNotThrow { serverModule.toString() }
    }

    @Test
    fun `GAMMA stage uses correct modules`() {
        val configuration = TestHumanEvalServiceConfiguration(serviceStageName = ServiceStageName.GAMMA)
        val serverModule = HumanEvalServerModule(configuration)
        assertDoesNotThrow { serverModule.toString() }
    }

    @Test
    fun `PROD stage uses correct modules`() {
        val configuration = TestHumanEvalServiceConfiguration(serviceStageName = ServiceStageName.PROD)
        val serverModule = HumanEvalServerModule(configuration)
        assertDoesNotThrow { serverModule.toString() }
    }

    @Test
    fun `ALPHA stage uses AlphaServiceModule with mock ThreatStoreService`() {
        val configuration = TestHumanEvalServiceConfiguration(serviceStageName = ServiceStageName.ALPHA)

        val injector = Guice.createInjector(
            AlphaServiceModule(configuration),
            TestDependenciesModule(),
        )

        val threatStoreService = injector.getInstance(ThreatStoreService::class.java)

        assertThat(threatStoreService, instanceOf(TestThreatStoreService::class.java))
    }

    private class TestDependenciesModule : AbstractModule() {
        // Empty module - TestWoozleServiceModule doesn't need any dependencies
    }
}
