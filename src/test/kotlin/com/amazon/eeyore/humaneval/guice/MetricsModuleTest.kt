package com.amazon.eeyore.humaneval.guice

import com.amazon.adrisk.util.RequestContext
import com.amazon.coral.metrics.MetricsFactory
import com.amazon.eeyore.humaneval.testutil.TestHumanEvalServiceConfiguration
import com.google.inject.Guice
import org.junit.jupiter.api.Test

class MetricsModuleTest {
    private val configuration = TestHumanEvalServiceConfiguration()

    @Test
    fun `Fargate Metrics Module can create a metrics factory`() {
        val injector = Guice.createInjector(HumanEvalFargateMetricsModule(configuration))

        injector.getInstance(MetricsFactory::class.java)
    }

    @Test
    fun `Fargate Metrics Module can create a one-pod metrics factory`() {
        val injector =
            Guice.createInjector(HumanEvalFargateMetricsModule(TestHumanEvalServiceConfiguration(isOnePod = true)))

        injector.getInstance(MetricsFactory::class.java)
    }

    @Test
    fun `Fargate Metrics Module can create a RequestContext`() {
        val injector = Guice.createInjector(HumanEvalFargateMetricsModule(configuration))

        // Need to remember to close this otherwise other tests will fail!
        injector.getInstance(RequestContext::class.java).use { }
    }
}
