package com.amazon.eeyore.humaneval.guice.auth

import com.amazon.eeyore.humaneval.service.AuthService
import com.amazon.eeyore.humaneval.service.auth.SystemsIntegrationTestAuthService
import com.amazon.eeyore.humaneval.testutil.TestAwsModule
import com.amazon.eeyore.humaneval.testutil.TestBrassConfiguration
import com.amazon.eeyore.humaneval.testutil.TestHumanEvalServiceConfiguration
import com.google.inject.Guice
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.instanceOf
import org.junit.jupiter.api.Test

class SystemsIntegrationTestsAuthModuleTest {
    private val injector =
        Guice.createInjector(
            SystemsIntegrationTestsAuthModule(TestHumanEvalServiceConfiguration(), TestBrassConfiguration()),
            TestAwsModule(),
        )

    @Test
    fun authorizer() {
        assertThat(
            injector.getInstance(AuthService::class.java),
            instanceOf(SystemsIntegrationTestAuthService::class.java),
        )
    }
}
