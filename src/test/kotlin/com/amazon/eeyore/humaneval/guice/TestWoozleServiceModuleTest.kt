package com.amazon.eeyore.humaneval.guice

import com.amazon.eeyore.humaneval.service.ThreatStoreService
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureContent
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.CreateSignatureResult
import com.amazonaws.services.woozlethreatstore.model.DomainSignatureContent
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesRequest
import com.amazonaws.services.woozlethreatstore.model.SearchSignaturesResult
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureContent
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureRequest
import com.amazonaws.services.woozlethreatstore.model.ValidateSignatureResult
import com.google.inject.Guice
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.instanceOf
import org.hamcrest.CoreMatchers.notNullValue
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class TestWoozleServiceModuleTest {

    private lateinit var testWoozleServiceModule: TestWoozleServiceModule
    private lateinit var testThreatStoreService: TestThreatStoreService

    @BeforeEach
    fun setUp() {
        testWoozleServiceModule = TestWoozleServiceModule()
        testThreatStoreService = TestThreatStoreService()
    }

    @Test
    fun `MockWoozleServiceModule provides ThreatStoreService instance`() {
        val injector = Guice.createInjector(testWoozleServiceModule)

        val threatStoreService = injector.getInstance(ThreatStoreService::class.java)

        assertThat(threatStoreService, notNullValue())
        assertThat(threatStoreService, instanceOf(TestThreatStoreService::class.java))
    }

    @Test
    fun `MockWoozleServiceModule provides singleton ThreatStoreService`() {
        val injector = Guice.createInjector(testWoozleServiceModule)

        val service1 = injector.getInstance(ThreatStoreService::class.java)
        val service2 = injector.getInstance(ThreatStoreService::class.java)

        assertThat(service1, equalTo(service2))
    }

    @Test
    fun `createSignature returns CreateSignatureResult for valid request`() {
        val request = createMockCreateSignatureRequest("example.com")

        val result = testThreatStoreService.createSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(CreateSignatureResult::class.java))
    }

    @Test
    fun `createSignature handles request with null content`() {
        val request = mock<CreateSignatureRequest>()
        whenever(request.content).thenReturn(null)

        val result = testThreatStoreService.createSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(CreateSignatureResult::class.java))
    }

    @Test
    fun `createSignature handles request with null domain signature content`() {
        val request = mock<CreateSignatureRequest>()
        val content = mock<CreateSignatureContent>()
        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(null)

        val result = testThreatStoreService.createSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(CreateSignatureResult::class.java))
    }

    @Test
    fun `createSignature handles request with null domain`() {
        val request = mock<CreateSignatureRequest>()
        val content = mock<CreateSignatureContent>()
        val domainContent = mock<DomainSignatureContent>()
        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(domainContent)
        whenever(domainContent.domain).thenReturn(null)

        val result = testThreatStoreService.createSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(CreateSignatureResult::class.java))
    }

    @Test
    fun `createSignatures processes multiple requests`() {
        val requests = listOf(
            createMockCreateSignatureRequest("example1.com"),
            createMockCreateSignatureRequest("example2.com"),
            createMockCreateSignatureRequest("example3.com"),
        )

        val results = testThreatStoreService.createSignatures(requests)

        assertThat(results.size, equalTo(3))
        results.forEach { result ->
            assertThat(result, notNullValue())
            assertThat(result, instanceOf(CreateSignatureResult::class.java))
        }
    }

    @Test
    fun `createSignatures handles empty list`() {
        val requests = emptyList<CreateSignatureRequest>()

        val results = testThreatStoreService.createSignatures(requests)

        assertThat(results.size, equalTo(0))
    }

    @Test
    fun `validateSignature returns ValidateSignatureResult for valid request`() {
        val request = createMockValidateSignatureRequest("example.com")

        val result = testThreatStoreService.validateSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(ValidateSignatureResult::class.java))
    }

    @ParameterizedTest
    @ValueSource(strings = ["malicious.com", "spam.com", "test-reject.com", "example.com", "safe-domain.com"])
    fun `validateSignature applies correct validation logic for different domains`(domain: String) {
        val request = createMockValidateSignatureRequest(domain)

        val result = testThreatStoreService.validateSignature(request)

        assertThat(result, notNullValue())
    }

    @Test
    fun `validateSignature handles blank domain`() {
        val request = createMockValidateSignatureRequest("")

        val result = testThreatStoreService.validateSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(ValidateSignatureResult::class.java))
    }

    @Test
    fun `validateSignature handles request with null content`() {
        val request = mock<ValidateSignatureRequest>()
        whenever(request.content).thenReturn(null)

        val result = testThreatStoreService.validateSignature(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(ValidateSignatureResult::class.java))
    }

    @Test
    fun `validateSignatures processes multiple requests`() {
        val requests = listOf(
            createMockValidateSignatureRequest("example1.com"),
            createMockValidateSignatureRequest("malicious.com"),
            createMockValidateSignatureRequest("test-reject.com"),
        )

        val results = testThreatStoreService.validateSignatures(requests)

        assertThat(results.size, equalTo(3))
        results.forEach { result ->
            assertThat(result, notNullValue())
            assertThat(result, instanceOf(ValidateSignatureResult::class.java))
        }
    }

    @Test
    fun `validateSignatures handles empty list`() {
        val requests = emptyList<ValidateSignatureRequest>()

        val results = testThreatStoreService.validateSignatures(requests)

        assertThat(results.size, equalTo(0))
    }

    @Test
    fun `searchSignatures returns SearchSignaturesResult for valid request`() {
        val request = createMockSearchSignaturesRequest("LIVE", 10, null)

        val result = testThreatStoreService.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result, instanceOf(SearchSignaturesResult::class.java))
        assertThat(result.items, notNullValue())
    }

    @ParameterizedTest
    @ValueSource(strings = ["LIVE", "SHADOW", "LIVE_REFERRAL", "PENDING_MANUAL_REVIEW", "REJECTED"])
    fun `searchSignatures handles different status values`(status: String) {
        val request = createMockSearchSignaturesRequest(status, 10, null)

        val result = testThreatStoreService.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
    }

    @Test
    fun `searchSignatures handles pagination with nextToken`() {
        val request = createMockSearchSignaturesRequest("LIVE", 5, "test-token")

        val result = testThreatStoreService.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
    }

    @Test
    fun `searchSignatures handles large maxResults`() {
        val request = createMockSearchSignaturesRequest("LIVE", 100, null)

        val result = testThreatStoreService.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
    }

    @Test
    fun `searchSignatures handles zero maxResults`() {
        val request = createMockSearchSignaturesRequest("LIVE", 0, null)

        val result = testThreatStoreService.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())
    }

    @Test
    fun `searchSignatures includes signature properties`() {
        val request = createMockSearchSignaturesRequest("LIVE", 10, null)

        val result = testThreatStoreService.searchSignatures(request)

        assertThat(result, notNullValue())
        assertThat(result.items, notNullValue())

        if (result.items.isNotEmpty()) {
            val signature = result.items[0]
            assertThat(signature.signatureId, notNullValue())
            assertThat(signature.status, notNullValue())
            assertThat(signature.signatureDecisionType, notNullValue())
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["malicious", "spam"])
    fun `validateSignature rejects domains containing malicious keywords`(keyword: String) {
        val domain = "test-$keyword-domain.com"
        val request = createMockValidateSignatureRequest(domain)

        val result = testThreatStoreService.validateSignature(request)

        assertThat(result, notNullValue())
    }

    private fun createMockCreateSignatureRequest(domain: String): CreateSignatureRequest {
        val request = mock<CreateSignatureRequest>()
        val content = mock<CreateSignatureContent>()
        val domainContent = mock<DomainSignatureContent>()

        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(domainContent)
        whenever(domainContent.domain).thenReturn(domain)

        return request
    }

    private fun createMockValidateSignatureRequest(domain: String): ValidateSignatureRequest {
        val request = mock<ValidateSignatureRequest>()
        val content = mock<ValidateSignatureContent>()
        val domainContent = mock<DomainSignatureContent>()

        whenever(request.content).thenReturn(content)
        whenever(content.domainSignatureContent).thenReturn(domainContent)
        whenever(domainContent.domain).thenReturn(domain)

        return request
    }

    private fun createMockSearchSignaturesRequest(
        status: String,
        maxResults: Int,
        nextToken: String?,
    ): SearchSignaturesRequest {
        val request = mock<SearchSignaturesRequest>()
        whenever(request.status).thenReturn(status)
        whenever(request.maxResults).thenReturn(maxResults)
        whenever(request.nextToken).thenReturn(nextToken)
        return request
    }
}
