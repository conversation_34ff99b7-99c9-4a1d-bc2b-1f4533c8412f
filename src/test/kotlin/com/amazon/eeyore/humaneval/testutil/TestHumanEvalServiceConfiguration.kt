package com.amazon.eeyore.humaneval.testutil

import com.amazon.adrisk.util.modules.SingletonModule
import com.amazon.eeyore.humaneval.config.BrassConfiguration
import com.amazon.eeyore.humaneval.config.HumanEvalServiceConfiguration
import com.amazon.eeyore.humaneval.config.ServiceStageName

class TestHumanEvalServiceConfiguration(
    override val allowedOrigins: Set<String> = setOf("https://test.example.com/"),
    override val serviceStageName: ServiceStageName = ServiceStageName.ALPHA,
    override val isOnePod: Boolean = false,
) : HumanEvalServiceConfiguration {
    override val applicationName: String = "EeyoreHumanEvaluation"
    override val region: String = "us-east-1"
    override val awsAccountID = "************"

    override fun eriskayClientModule(): SingletonModule = TestEriskayClientModule()
    override fun woozleClientModule(): SingletonModule = TestWoozleClientModule()
    override fun awsModule(): SingletonModule = TestAwsModule()

    override fun isRunningInDeveloperPersonalAccount(): Boolean = false
}

class TestBrassConfiguration : BrassConfiguration {
    override val brassCoralQualifier = "AWSAuth.Alpha"
}
