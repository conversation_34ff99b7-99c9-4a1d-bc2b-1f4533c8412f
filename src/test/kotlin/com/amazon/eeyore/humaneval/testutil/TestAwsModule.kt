package com.amazon.eeyore.humaneval.testutil

import com.amazon.adrisk.util.modules.SingletonModule
import com.google.inject.Provides
import com.google.inject.Singleton
import org.mockito.kotlin.mock
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.presigner.S3Presigner

class TestAwsModule : SingletonModule() {
    @Provides
    @Singleton
    fun s3Client(): S3Client = mock()

    @Provides
    @Singleton
    fun s3Presigner(): S3Presigner = mock()

    @Provides
    @Singleton
    fun awsCredentialsProvider(): AwsCredentialsProvider = mock()
}
