package com.amazon.eeyore.humaneval.health

import com.amazon.coral.service.HealthCheckStrategy
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class DeepHealthCheckTest {

    @Test
    fun `returns true when delegate is healthy`() {
        val delegate = mock(HealthCheckStrategy::class.java)
        `when`(delegate.isHealthy).thenReturn(true)

        val deepHealthCheck = DeepHealthCheck(delegate)

        assertThat(deepHealthCheck.isHealthy(), equalTo(true))
    }

    @Test
    fun `returns false when delegate is unhealthy`() {
        val delegate = mock(HealthCheckStrategy::class.java)
        `when`(delegate.isHealthy).thenReturn(false)

        val deepHealthCheck = DeepHealthCheck(delegate)

        assertThat(deepHealthCheck.isHealthy(), equalTo(false))
    }
}
