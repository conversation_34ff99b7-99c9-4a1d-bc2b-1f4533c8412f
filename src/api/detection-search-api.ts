import { mockDetections } from "./mock-data";
import {
    DetectionDetails as Detection,
    DetectionType,
    DetectionStatus,
    MatchType
} from "./detection-types";

export type { Detection };
export {
    DetectionType,
    DetectionStatus,
    MatchType
};

export interface SearchDetectionsRequest {
    signatureId: string;
    detectionType: DetectionType;
    maxResults?: number;
    nextToken?: string;
    createdAfter?: string;
    createdBefore?: string;
}

export interface SearchDetectionsResponse {
    items: Detection[];
    nextToken?: string;
}

export class DetectionSearchApi {
    private generateMockDetections(signatureId: string, count: number): Detection[] {
        const detections: Detection[] = [];
        const landingPages = ["malicious-site.com/landing", "phishing-domain.net/page", "suspicious-ads.org/redirect"];

        for (let i = 0; i < count; i++) {
            detections.push({
                detectionId: `det-${Math.random().toString(36).substr(2, 9)}`,
                detectionType: DetectionType.SIMULATED,
                eriskayId: `eri-${Math.random().toString(36).substr(2, 8)}`,
                signatureId,
                landingPage: landingPages[i % landingPages.length],
                renderId: `render-${Math.random().toString(36).substr(2, 8)}`,
                status: [DetectionStatus.PENDING_VERIFICATION, DetectionStatus.UNSAFE, DetectionStatus.FALSE_POSITIVE][
                    i % 3
                ],
                creationTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
                lastUpdatedTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
                matchType: Math.random() > 0.5 ? MatchType.EXACT : MatchType.SIMILARITY,
                metadata: `Detection metadata for ${signatureId}`,
                validationDetails: {
                    "confidence": (Math.random() * 100).toFixed(2),
                    "source": "automated_scan"
                }
            });
        }

        return detections.sort((a, b) => new Date(b.creationTime).getTime() - new Date(a.creationTime).getTime());
    }

    async searchDetections(request: SearchDetectionsRequest): Promise<SearchDetectionsResponse> {
        await new Promise(resolve => setTimeout(resolve, 300));

        const maxResults = request.maxResults || 20;
        
        // Always use SIMULATED type
        const detectionType = DetectionType.SIMULATED;
        console.log(`Using detection type: ${detectionType}`);
        
        // Use mock data if available for this signature ID, otherwise generate random data
        const data = mockDetections[request.signatureId] || 
                    this.generateMockDetections(request.signatureId, 5);
        
        // Ensure all detections have SIMULATED type
        const simulatedData = data.map(detection => ({
            ...detection,
            detectionType
        }));
        
        console.log(`Loading detections for ${request.signatureId}:`, simulatedData.length);
        
        const startIndex = request.nextToken ? parseInt(request.nextToken) : 0;
        const endIndex = Math.min(startIndex + maxResults, simulatedData.length);
        const items = simulatedData.slice(startIndex, endIndex);

        const nextToken = endIndex < simulatedData.length ? endIndex.toString() : undefined;

        return {items, nextToken};
    }
}