import {SignatureInput} from "../signature-upload-api";
import {
    SignatureStatus,
    SignatureDecision,
    IntelligenceSource,
    ThreatClassification,
    IntelligenceLineage
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

export interface ValidationError {
    field?: string;
    message: string;
    row?: number;
    severity?: "error" | "warning";
}

export interface CsvValidationResult {
    valid: boolean;
    signatures: SignatureInput[];
    errors: ValidationError[];
}

export interface SignatureValidationResult {
    valid: boolean;
    errors: ValidationError[];
}

export function validateSignature(signature: SignatureInput, row?: number): SignatureValidationResult {
    const errors: ValidationError[] = [];

    if (!signature.domain?.trim()) {
        errors.push({field: "domain", message: "Domain is required", row});
    } else if (!validateDomainFormat(signature.domain)) {
        errors.push({field: "domain", message: "Invalid domain format", row});
    }

    if (!signature.source?.trim()) {
        errors.push({field: "source", message: "Source is required", row});
    }

    if (!signature.description?.trim()) {
        errors.push({field: "description", message: "Description is required", row});
    }

    if (!signature.status?.trim()) {
        errors.push({field: "status", message: "Status is required", row});
    } else {
        const allowedStatuses = [SignatureStatus.SHADOW, SignatureStatus.LIVE];
        if (!allowedStatuses.includes(signature.status.toUpperCase() as SignatureStatus)) {
            errors.push({
                field: "status",
                message: `Invalid SignatureStatus value. Must be one of: ${allowedStatuses.join(", ")}`,
                row
            });
        }
    }

    if (!signature.decision?.trim()) {
        errors.push({field: "decision", message: "Decision is required", row});
    } else if (!Object.values(SignatureDecision).includes(signature.decision.toUpperCase() as SignatureDecision)) {
        errors.push({
            field: "decision",
            message: `Invalid SignatureDecision value. Must be one of: ${Object.values(SignatureDecision).join(", ")}`,
            row
        });
    }

    if (!signature.source?.trim()) {
        errors.push({field: "source", message: "Source is required", row});
    } else if (!Object.values(IntelligenceSource).includes(signature.source as IntelligenceSource)) {
        errors.push({
            field: "source",
            message: `Invalid source. Must be one of: ${Object.values(IntelligenceSource).join(", ")}`,
            row
        });
    }

    if (signature.lineage && !Object.values(IntelligenceLineage).includes(signature.lineage)) {
        errors.push({
            field: "lineage",
            message: `Invalid Lineage value. Must be one of: ${Object.values(IntelligenceLineage).join(", ")}`,
            row
        });
    }

    if (
        signature.threatClassification &&
        !Object.values(ThreatClassification).includes(signature.threatClassification)
    ) {
        errors.push({
            field: "threatClassification",
            message: `Invalid ThreatType value. Must be one of: ${Object.values(ThreatClassification).join(", ")}`,
            row
        });
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

function parseCsvLine(line: string): string[] {
    const result: string[] = [];
    let current = "";
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                current += '"';
                i++; // Skip next quote
            } else {
                inQuotes = !inQuotes;
            }
        } else if (char === "," && !inQuotes) {
            result.push(current.trim());
            current = "";
        } else {
            current += char;
        }
    }

    result.push(current.trim());
    return result;
}

export function validateCsvContent(csvContent: string): CsvValidationResult {
    const lines = csvContent
        .trim()
        .split("\n")
        .filter(line => line.trim());

    if (lines.length === 0) {
        return {
            valid: false,
            signatures: [],
            errors: [{row: 0, message: "CSV file is empty"}]
        };
    }

    const headers = parseCsvLine(lines[0]);
    const requiredHeaders = ["Domain", "Source", "Description", "Status", "Decision"];

    if (!requiredHeaders.every(header => headers.includes(header))) {
        return {
            valid: false,
            signatures: [],
            errors: [
                {
                    row: 0,
                    message:
                        "Invalid CSV headers. Required headers: Domain, Source, Description, Status, Decision. Optional headers: Lineage, ThreatClassification"
                }
            ]
        };
    }

    const signatures: SignatureInput[] = [];
    const errors: ValidationError[] = [];

    for (let i = 1; i < lines.length; i++) {
        const values = parseCsvLine(lines[i]);
        if (values.length < requiredHeaders.length) {
            errors.push({row: i, message: "Missing required columns"});
            continue;
        }

        const lineageIndex = headers.indexOf("Lineage");
        const threatClassificationIndex = headers.indexOf("ThreatClassification");

        const lineageValue = lineageIndex >= 0 ? values[lineageIndex]?.trim() : undefined;
        const threatClassificationValue =
            threatClassificationIndex >= 0 ? values[threatClassificationIndex]?.trim() : undefined;

        const signature: SignatureInput = {
            domain: values[headers.indexOf("Domain")]?.trim() || "",
            source: values[headers.indexOf("Source")]?.trim() as IntelligenceSource,
            description: values[headers.indexOf("Description")]?.trim() || "",
            status: values[headers.indexOf("Status")]?.trim() || "",
            decision: values[headers.indexOf("Decision")]?.trim() || "",
            lineage: lineageValue && lineageValue !== "" ? (lineageValue as IntelligenceLineage) : undefined,
            threatClassification:
                threatClassificationValue && threatClassificationValue !== ""
                    ? (threatClassificationValue as ThreatClassification)
                    : undefined
        };

        const validationResult = validateSignature(signature, i);
        if (!validationResult.valid) {
            errors.push(...validationResult.errors);
            continue;
        }

        signatures.push(signature);
    }

    return {
        valid: errors.length === 0,
        signatures,
        errors
    };
}

export function validateDomainFormat(domain: string): boolean {
    return domain.includes(".") && !domain.includes(" ");
}
