import {ValidationStatus} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";

/**
 * Safely converts string to ValidationStatus with fallback
 * Woozle APIs return APPROVED/REJECTED, N_A used for failures
 */
export function toValidationStatus(status: string): ValidationStatus {
    const upperStatus = status.toUpperCase();
    return Object.values(ValidationStatus).includes(upperStatus as ValidationStatus)
        ? (upperStatus as ValidationStatus)
        : ValidationStatus.N_A;
}
