import {SignatureEntry, ImpactAssessment} from "../signature-upload-api";
import {CreatedSignatureEntry, ValidationStatus} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {toValidationStatus} from "./validation-helpers";

export function transformCreatedSignatureEntry(entry: CreatedSignatureEntry): SignatureEntry {
    if (!entry || !entry.domain) {
        return createErrorEntry("Unknown", "Invalid entry data received from server");
    }

    const domain = entry.domain;
    const signatureId = entry.signatureId;
    const createdAt = entry.createdAt;
    const errorMessage = entry.errorMessage;
    const validationResult = entry.validationResult;

    let validationStatus: ValidationStatus = ValidationStatus.N_A;
    let validationReason: string | undefined;
    let impactAssessment: ImpactAssessment | undefined;

    if (validationResult && typeof validationResult === "object" && validationResult !== null) {
        const validationData = validationResult as unknown as Record<string, unknown>;

        if (validationData.status && typeof validationData.status === "string") {
            validationStatus = toValidationStatus(validationData.status);
        }

        validationReason = typeof validationData.reason === "string" ? validationData.reason : undefined;

        if (validationData.impactAssessment) {
            impactAssessment = transformImpactAssessment(validationData.impactAssessment);
        }
    }

    const success =
        typeof entry.success === "boolean"
            ? entry.success
            : validationStatus === ValidationStatus.APPROVED && !!signatureId;

    const finalErrorMessage = determineErrorMessage(success, validationStatus, errorMessage, validationReason);

    return {
        domain,
        signatureId,
        createdAt,
        success,
        errorMessage: finalErrorMessage,
        validationResult: {
            domain,
            status: validationStatus,
            ...(validationReason && {reason: validationReason}),
            ...(impactAssessment && {impactAssessment})
        }
    };
}

export function createErrorEntry(domain: string, errorMessage: string): SignatureEntry {
    const domainSpecificErrorMessage = `[${domain}] ${errorMessage}`;

    return {
        domain,
        signatureId: undefined,
        createdAt: undefined,
        success: false,
        errorMessage: domainSpecificErrorMessage,
        validationResult: {
            domain,
            status: ValidationStatus.N_A,
            reason: domainSpecificErrorMessage
        }
    };
}

function determineErrorMessage(
    success: boolean,
    validationStatus: ValidationStatus,
    errorMessage: string | undefined,
    validationReason: string | undefined
): string | undefined {
    if (success) return undefined;

    if (validationStatus === ValidationStatus.APPROVED && errorMessage) {
        return errorMessage;
    } else if (validationStatus === ValidationStatus.REJECTED) {
        return errorMessage || validationReason || "Validation rejected";
    } else if (validationStatus === ValidationStatus.N_A) {
        return errorMessage || "Processing failed";
    } else {
        return errorMessage || "Unknown error";
    }
}

function transformImpactAssessment(assessment: unknown): ImpactAssessment | undefined {
    if (!assessment || typeof assessment !== "object" || assessment === null) {
        return undefined;
    }

    const assessmentData = assessment as Record<string, unknown>;

    const timesSeen = typeof assessmentData.timesSeen === "number" ? assessmentData.timesSeen : 0;
    const firstPartyAdvertisersCount =
        typeof assessmentData.firstPartyAdvertisersCount === "number" ? assessmentData.firstPartyAdvertisersCount : 0;

    let impactedCreatives = {
        safeCreativesCount: 0,
        unsafeCreativesCount: 0,
        clicks: 0,
        impressions: 0
    };

    if (assessmentData.impactedCreatives && typeof assessmentData.impactedCreatives === "object") {
        const creatives = assessmentData.impactedCreatives as Record<string, unknown>;
        impactedCreatives = {
            safeCreativesCount: typeof creatives.safeCreativesCount === "number" ? creatives.safeCreativesCount : 0,
            unsafeCreativesCount:
                typeof creatives.unsafeCreativesCount === "number" ? creatives.unsafeCreativesCount : 0,
            clicks: typeof creatives.clicks === "number" ? creatives.clicks : 0,
            impressions: typeof creatives.impressions === "number" ? creatives.impressions : 0
        };
    }

    return {
        timesSeen,
        firstPartyAdvertisersCount,
        impactedCreatives
    };
}
