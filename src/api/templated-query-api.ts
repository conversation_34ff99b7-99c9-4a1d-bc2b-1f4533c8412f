import {getHumanEvaluationClient} from "./index";
import {DebugTemplatedQueryResultResponse} from "./model";

export class Templated<PERSON>ueryApi {
    async templatedQueryStart(templateId: string, params: object): Promise<string> {
        const startQueryResult = await getHumanEvaluationClient()
            .debugTemplatedQueryStart({
                templateId,
                params: JSON.stringify(params)
            })
            .toPromise();

        return startQueryResult.queryExecutionId;
    }

    async templatedQueryResult<T>(
        queryExecutionId: string,
        continuationToken?: string
    ): Promise<DebugTemplatedQueryResultResponse<T>> {
        const templatedQueryResult = await getHumanEvaluationClient()
            .debugTemplatedQueryResult({
                queryExecutionId,
                continuationToken
            })
            .toPromise();

        const result = JSON.parse(templatedQueryResult.result) as T;

        return {
            result,
            completionDateTime: templatedQueryResult.completionDateTime,
            continuationToken: templatedQueryResult.continuationToken
        };
    }
}
