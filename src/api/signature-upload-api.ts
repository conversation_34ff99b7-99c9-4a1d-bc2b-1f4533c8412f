import {getIdentity} from "./index";

export enum Status {
    SHADOW = "shadow",
    LIVE = "live"
}

export enum Decision {
    THREAT = "threat",
    SAFETY = "safety"
}

export enum IntelligenceSource {
    // System testing
    SYSTEM_TESTING = "SYSTEM_TESTING",

    // First Party Intelligence
    ADRISK_REVERSE_IP = "ADRISK_REVERSE_IP",
    ADRISK_WEB_THREAT_HUNTING = "ADRISK_WEB_THREAT_HUNTING",
    ADRISK_SLICENDICE = "ADRISK_SLICENDICE",
    ADRISK_ANOMSIM = "ADRISK_ANOMSIM",
    ADRISK_GALLERY_HUMAN_REVIEW = "ADRISK_GALLERY_HUMAN_REVIEW",

    // Third Party Intelligence Sources
    ASA = "ASA",
    TAG = "TAG",
    GOOGLE = "GOOGLE",
    CONFIANT = "CONFIANT",
    GEOEDGE = "GEOEDGE",
    TMT = "TMT",

    // Additional sources
    ADRISK_TDI = "ADRISK_TDI",
    ADRISK_MINHASH = "ADRISK_MINHASH",
    ADRISK_DOM = "ADRISK_DOM",
    BOLTIVE = "BOLTIVE",
    HUMAN = "HUMAN"
}

export interface SignatureInput {
    readonly domain: string;
    readonly source: string;
    readonly description: string;
    readonly status: Status | string;
    readonly decision: Decision | string;
}

export interface Signature extends SignatureInput {
    readonly creator: string;
    readonly auditor: string;
}

export interface ValidationDetails {
    readonly status: "APPROVED" | "REJECTED";
    readonly reason: string | null;
    readonly impactAssessment: {
        readonly timesSeen: number;
        readonly firstPartyAdvertisersCount: number;
        readonly impactedCreatives: {
            readonly safeCreativesCount: number;
            readonly unsafeCreativesCount: number;
            readonly clicks: number;
            readonly impressions: number;
        };
    } | null;
}

export interface SignatureEntry {
    readonly domain: string;
    readonly signatureId: string | null;
    readonly createdAt: string | null;
    readonly success: boolean;
    readonly errorMessage: string | null;
    readonly validationDetails: ValidationDetails;
}

export interface SignatureUploadResponse {
    readonly signatureEntries: SignatureEntry[];
}

export interface FailedUpload {
    readonly signature: Signature;
    readonly errorMessage: string;
}

export class SignatureUploadApi {
    async createSignatures(signatureInputs: SignatureInput[]): Promise<SignatureUploadResponse> {
        const identity = await getIdentity();
        const username = identity.username || "unknown-user";

        // Create signatures with creator and auditor
        const signatures = signatureInputs.map(input => ({
            ...input,
            creator: username,
            auditor: username
        }));

        // MOCK: This is a mock implementation until the backend API is ready
        // TODO: The real API integration will return actual impact assessment data from the backend
        // The mock generates random values for demonstration purposes only
        return {
            signatureEntries: signatures.map(signature => ({
                domain: signature.domain,
                signatureId: null,
                createdAt: new Date().toISOString(),
                success: true,
                errorMessage: null,
                validationDetails: {
                    status: "APPROVED",
                    reason: null,
                    impactAssessment: {
                        timesSeen: Math.floor(Math.random() * 100),
                        firstPartyAdvertisersCount: Math.floor(Math.random() * 10),
                        impactedCreatives: {
                            safeCreativesCount: Math.floor(Math.random() * 20),
                            unsafeCreativesCount: 0,
                            clicks: Math.floor(Math.random() * 2000),
                            impressions: Math.floor(Math.random() * 100000)
                        }
                    }
                }
            }))
        };
    }

    checkDomainsExist(domains: string[]): Promise<string[]> {
        // MOCK: This is a mock implementation until the backend API is ready
        // TODO: The real API integration will check if domains already exist in the database
        return Promise.resolve(domains);
    }

    async validateCsvContent(csvContent: string): Promise<{
        valid: boolean;
        signatures: Signature[];
        errors: {row: number; message: string}[];
    }> {
        const lines = csvContent.trim().split("\n");
        const headers = lines[0].split(",");

        const requiredHeaders = ["Domain", "Source", "Description", "Status", "Decision"];
        const validHeaders = requiredHeaders.every(header => headers.includes(header));

        if (!validHeaders) {
            return Promise.resolve({
                valid: false,
                signatures: [],
                errors: [
                    {
                        row: 0,
                        message: "Invalid CSV headers. Required headers: Domain, Source, Description, Status, Decision"
                    }
                ]
            });
        }

        const validStatusValues = ["shadow", "live"];
        const validDecisionValues = ["threat", "safety"];

        const signatures: SignatureInput[] = [];
        const errors: {row: number; message: string}[] = [];

        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(",");
            if (values.length !== headers.length) {
                errors.push({row: i, message: "Invalid number of columns"});
                continue;
            }

            const signature: SignatureInput = {
                domain: values[headers.indexOf("Domain")],
                source: values[headers.indexOf("Source")],
                description: values[headers.indexOf("Description")],
                status: values[headers.indexOf("Status")],
                decision: values[headers.indexOf("Decision")]
            };

            if (!signature.domain) {
                errors.push({row: i, message: "Domain is required"});
                continue;
            }

            if (!signature.source) {
                errors.push({row: i, message: "Source is required"});
                continue;
            } else if (!Object.values(IntelligenceSource).includes(signature.source as IntelligenceSource)) {
                errors.push({
                    row: i,
                    message: "Invalid Source value. Must be one of the valid intelligence sources."
                });
                continue;
            }

            if (!signature.description) {
                errors.push({row: i, message: "Description is required"});
                continue;
            }

            if (!signature.status) {
                errors.push({row: i, message: "Status is required"});
                continue;
            } else if (!validStatusValues.includes(signature.status.toLowerCase())) {
                errors.push({row: i, message: `Invalid Status value. Must be one of: ${validStatusValues.join(", ")}`});
                continue;
            }

            if (!signature.decision) {
                errors.push({row: i, message: "Decision is required"});
                continue;
            } else if (!validDecisionValues.includes(signature.decision.toLowerCase())) {
                errors.push({
                    row: i,
                    message: `Invalid Decision value. Must be one of: ${validDecisionValues.join(", ")}`
                });
                continue;
            }

            signatures.push(signature);
        }

        // Get the current user's identity to add as creator and auditor
        const identity = await getIdentity();
        const username = identity.username || "unknown-user";

        // Add creator and auditor to each signature
        const signaturesWithUser = signatures.map(signature => ({
            ...signature,
            creator: username,
            auditor: username
        }));

        // MOCK: This is a mock implementation until the backend API is ready
        // The real API will validate the CSV content against the backend database
        return {
            valid: errors.length === 0,
            signatures: signaturesWithUser,
            errors
        };
    }
}
