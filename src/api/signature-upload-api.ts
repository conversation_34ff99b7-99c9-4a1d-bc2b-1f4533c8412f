import {getHumanEvaluationClient} from "./index";
import {
    CreateSignaturesRequest,
    CreatedSignatureEntry as ModelCreatedSignatureEntry,
    IntelligenceSource,
    ThreatClassification,
    IntelligenceLineage,
    ValidationStatus
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {validateCsvContent} from "./utils/csv-validation";
import {transformCreatedSignatureEntry} from "./utils/data-transformation";

// ValidationStatus dependency: Woozle impact assessment APIs return APPROVED/REJECTED.
// N_A is used locally when API calls fail or validation is not applicable.
// All other Enum values are defined in upstream model - WoozleThreatStoreModel [https://code.amazon.com/packages/WoozleThreatStoreModel/trees/mainline]

// TODO: Update @amzn/coral_com-amazon-eeyore-humanevaluation-model with EeyoreHumanEvaluationLambdaModel package if upstream changes

export interface SignatureInput {
    readonly domain: string;
    readonly source: IntelligenceSource;
    readonly description: string;
    readonly status: string;
    readonly decision: string;
    readonly lineage?: IntelligenceLineage;
    readonly threatClassification?: ThreatClassification;
}

export interface ImpactAssessment {
    readonly timesSeen: number;
    readonly firstPartyAdvertisersCount: number;
    readonly impactedCreatives: {
        readonly safeCreativesCount: number;
        readonly unsafeCreativesCount: number;
        readonly clicks: number;
        readonly impressions: number;
    };
}

export interface ValidationResult {
    readonly domain: string;
    readonly status: ValidationStatus;
    readonly reason?: string;
    readonly impactAssessment?: ImpactAssessment;
}

export type SignatureEntry = ModelCreatedSignatureEntry;

export interface SignatureUploadResponse {
    readonly signatureEntries: SignatureEntry[];
}

export class SignatureUploadApi {
    async createSignatures(signatureInputs: SignatureInput[]): Promise<SignatureUploadResponse> {
        const client = getHumanEvaluationClient();

        const modelSignatures = signatureInputs.map(input => ({
            decision: input.decision,
            description: input.description,
            domain: input.domain,
            source: input.source,
            status: input.status,
            lineage: input.lineage,
            threatClassification: input.threatClassification
        }));

        const request: CreateSignaturesRequest = {
            signatures: modelSignatures
        };

        try {
            const response = await client.createSignatures(request).toPromise();
            return {
                signatureEntries: response.createdSignatures.map(transformCreatedSignatureEntry)
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to submit signatures";
            return {
                signatureEntries: signatureInputs.map(input => ({
                    domain: input.domain,
                    signatureId: undefined,
                    createdAt: undefined,
                    success: false,
                    errorMessage: `[${input.domain}] ${errorMessage}`,
                    validationResult: {
                        domain: input.domain,
                        status: errorMessage.toLowerCase().includes("validation")
                            ? ValidationStatus.REJECTED
                            : ValidationStatus.N_A,
                        reason: `[${input.domain}] ${errorMessage}`
                    }
                }))
            };
        }
    }

    async processCsvFile(file: File): Promise<{
        valid: boolean;
        signatures?: SignatureInput[];
        errors?: string[];
    }> {
        try {
            const content = await file.text();
            const validationResult = validateCsvContent(content);
            return validationResult.valid
                ? {valid: true, signatures: validationResult.signatures}
                : {valid: false, errors: validationResult.errors.map(e => e.message)};
        } catch (error) {
            return {
                valid: false,
                errors: [`Failed to process CSV file: ${error instanceof Error ? error.message : "Unknown error"}`]
            };
        }
    }
}
