export interface Identity {
    username: string;
    icon: string;
}

export interface HarFile {
    data: <PERSON>r<PERSON>ileLog;
}

export interface HarFileLog {
    log: {
        entries: HarFileEntry[];
    };
}

export interface HarFileEntry {
    request: HarFileEntryRequest;
    response: HarFileEntryResponse;
    initiator?: HarFileInitiator;
    startedDateTime: string;
    source: string;
    serverIPAddress: string;
    time: string;
    timings?: HarFileEntryTimings;
}

export interface HarFileEntryRequest {
    method: string;
    url: string;
    headers: Har<PERSON>ileHeader[];
    postData?: {
        text: string;
    };
    cookies?: string[];
}

export interface HarFileEntryResponse {
    headers: HarFileHeader[];
    status: number;
    content: {
        size: number;
        mimeType: string;
        text: string;
    };
}

export interface HarFileEntryTimings {
    connect: number;
    receive: number;
    send: number;
    ssl: number;
    wait: number;
}

export interface HarFileHeader {
    name: string;
    value: string;
}

export interface HarFileInitiator {
    initiator: string;
    stackTrace: HarFileStackFrame[];
    callType: string;
}

export interface HarFileStackFrame {
    functionName: string;
    scriptId: string;
    url: string;
}

export interface ConsoleLogEntry {
    level: string;
    text: string;
    url: string;
    timestamp: string;
    stackTrace?: ConsoleLogStackTrace;
    instrumentedCall?: ConsoleLogInstrumentedCall;
}

export interface ConsoleLogStackTrace {
    callFrames: ConsoleLogCallFrame[];
    parent?: ConsoleLogStackTrace;
    parentId?: {id: string};
    description?: string;
}

export interface ConsoleLogCallFrame {
    functionName: string;
    lineNumber: number;
    columnNumber: number;
    scriptId: string;
    url: string;
}

export interface ConsoleLogInstrumentedCall {
    description: string;
    args: string[];
    stack: string[];
    result?: string;
}

export interface DebugTemplatedQueryStartResponse {
    queryExecutionId: string;
}

export interface DebugTemplatedQueryResultResponse<T> {
    result: T;
    completionDateTime: string;
    continuationToken?: string;
}

export interface DebugGetSignatureCategoriesResponse {
    signatureCategories: string[];
}

export interface ScatterPlotStartPlotRequestRenderItem {
    creativeID: string;
    creativeIDSpace: string;
    renderID: string;
    embeddings?: number[];
}

export interface ScatterPlotInputDataFile {
    header: string[];
    data: string[][];
}

export interface ScatterPlotStartPlotResponse {
    outputLocation: string;
}

export interface ScatterPlotPlotProgressResponse {
    finished: boolean;
    landingPagesPending: boolean;
    plot?: ScatterPlotPlotProgressResponseItem[];
    plotLandingPages?: ScatterPlotPlotProgressResponseItem[];
    inputDataFile?: string;
}

export interface ScatterPlotPlotProgressResponseItem {
    creativeID: string;
    renderID: string;
    x: number;
    y: number;
    smallImage: string;
    largeImage: string;
}

export interface ScatterPlotRequestedPlot {
    outputLocation: string;
    requestStartTimestamp: string;
    user: string;
    tag: string;
}
