import {UnknownCoralError} from "@amzn/ihm-js-coral-client";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getMessageFromApiException(e: any, defaultMessage?: string): string {
    if (typeof e === "string") {
        return e;
    }
    try {
        if ("response" in e && "data" in e.response && "message" in e.response.data) {
            const message = e.response.data.message;
            if (message) {
                return message;
            }
        }
        if ((e as UnknownCoralError).receivedException !== undefined) {
            const message = e.receivedException.message;
            if (message) {
                return message;
            }
        }
    } catch (_) {
        // We ignore this exception as we always want to display some error message to prevent endless spinners.
    }
    return defaultMessage || e.toString();
}
