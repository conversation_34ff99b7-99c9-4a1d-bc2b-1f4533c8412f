import {EeyoreHumanEvaluationBackendApi} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {CoralHttpConfig} from "@amzn/ihm-js-coral-client";
import {EeyoreHumanEvaluationBackendApiClientFactory} from "./eeyore-human-evaluation-backend-api-client-factory";
import {Identity} from "./model";
import {RenderDataApi} from "./render-data-api";
import {TemplatedQueryApi} from "./templated-query-api";
import {PermissionsApi} from "./permissions-api";

let client: EeyoreHumanEvaluationBackendApi;

export const renderDataApi = new RenderDataApi();
export const templatedQueryApi = new TemplatedQueryApi();
export const permissionsApi = new PermissionsApi();

export function getHumanEvaluationClient(): EeyoreHumanEvaluationBackendApi {
    return client;
}

export async function initialize() {
    const settings = await fetchSettings();
    client = await buildClient(settings);
}

export function getIdentity(): Promise<Identity> {
    return client.getIdentity({}).toPromise();
}

interface Settings {
    apiEndpoint: string;
    cognitoPoolId: string;
    region: string;
}

function fetchSettings(): Promise<Settings> {
    return fetch("settings.json")
        .then(response => {
            return response.json();
        })
        .catch(_ => {
            return fetch("local-settings.json").then(response => {
                return response.json();
            });
        });
}

function buildClient(settings: Settings): EeyoreHumanEvaluationBackendApi {
    const config: CoralHttpConfig = {
        httpEndpoint: {
            url: `https://${settings.apiEndpoint}/`
        },
        httpSigning: {
            scheme: "aws4-hmac-sha256",
            service: "execute-api",
            region: settings.region
        },
        inputValidation: {},
        midwayIdentity: {
            cognitoIdentityPoolId: settings.cognitoPoolId
        }
    };

    return EeyoreHumanEvaluationBackendApiClientFactory.client(config);
}
