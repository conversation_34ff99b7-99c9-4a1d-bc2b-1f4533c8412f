import {
    Annotations,
    CreativeIDSpace,
    FailedRenderArtifacts,
    MalwareDetectionAnnotations,
    RenderArtifacts,
    RenderPartition,
    ScreenCaptureVideo,
    ScreencastFrame,
    Screenshot
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import pako from "pako";
import {getHumanEvaluationClient} from "./index";
import {HarFileLog} from "./model";
import * as zip from "@zip.js/zip.js";

export interface CreativeDetailsWithRenders {
    readonly creativeDetails: CreativeDetails;
    readonly renders: RenderSummaryDetails[];
    readonly continuationToken?: string | null;
}

export interface CreativeDetails {
    readonly sourceSystem: string;
    readonly sourceProgram: string;
    readonly creativeIDs: CreativeID[];
}

export interface CreativeID {
    readonly idSpace: string;
    readonly id: string;
    readonly variantID?: string;
}

export interface RenderSummaryDetails {
    readonly id: string;
    readonly timestamp: Date;
    readonly renderType: string;
    readonly device: string;
    readonly operatingSystem: string;
    readonly browser: string;
    readonly result: string;
    readonly hasLandingPage: boolean;
    readonly hasMalwareDetections: boolean;
    readonly hasScreenCaptureFrames: boolean;
    readonly hasScreenCaptureVideo: boolean;
    readonly imagePreviewUrl?: string;
}

export interface CreativeConfiguration {
    readonly creativeType: string;
    readonly markupUrl: string;
    readonly protocol: string;
    readonly publisherPageUrl?: string;
    readonly containerType?: string;
    readonly viewportSize?: {
        readonly width: number;
        readonly height: number;
    };
    readonly tcString?: string;
}

export interface TargetingConfiguration {
    readonly geoLocation: {
        readonly countryCode: string;
        readonly zipCode?: string;
    };
    readonly browser: string;
    readonly operatingSystem: string;
    readonly deviceLayout?: string;
    readonly deviceName?: string;
    readonly userAgent?: string;
}

export interface ClickConfiguration {
    readonly performClick?: boolean;
    readonly clickDelay?: number;
    readonly clickLocation?: {
        readonly x: number;
        readonly y: number;
    };
}

export interface ScreenCaptureConfiguration {
    readonly recordScreenCapture: boolean;
}
export interface RenderAttributes {
    readonly id: string;
    readonly startTime: Date;
    readonly completionTime?: Date;
    readonly renderType: string;
    readonly fidelity: string;
    readonly result: string;
    readonly detectedLandingPageUrl?: string;
}

export interface RenderDetails {
    readonly creativeDetails: CreativeDetails;
    readonly renderAttributes: RenderAttributes;
    readonly creativeConfiguration: CreativeConfiguration;
    readonly targetingConfiguration: TargetingConfiguration;
    readonly clickConfiguration?: ClickConfiguration;
    readonly screenCaptureConfiguration?: ScreenCaptureConfiguration;
    readonly screenshots: PartitionedScreenshots;

    readonly consoleLogLinesUrl?: string;
    readonly networkTraceUrl?: string;

    readonly annotations?: RenderAnnotations;
    readonly screencastFrames?: ScreencastFrame[];
    readonly screenCaptureVideos?: ScreenCaptureVideo[];
    readonly successfulRenderArtifacts?: RenderArtifacts;
    readonly failedRenderArtifacts?: FailedRenderArtifacts[];
}

export interface RenderAnnotations {
    readonly malwareDetections?: MalwareDetectionAnnotation[];
}

export interface MalwareDetectionAnnotation {
    readonly incidentID: string;
    readonly description: string;
    readonly detectionSourceSystem: string;
    readonly enforcementType: string;
}

export interface PartitionedScreenshots {
    readonly creativeScreenshots?: Array<Screenshot>;
    readonly landingPageScreenshot?: Array<Screenshot>;
}

export interface ScreencastFrameData {
    readonly filename: string;
    readonly url: string;
}

export interface ScreencastFrameMetadata {
    tabID: number;
    filename: string;
    timestamp: number;
}

export class RenderDataApi {
    async getCreativeDetailsWithRenders(
        creativeIdSpace: string,
        creativeId: string,
        variantId?: string,
        continuationToken?: string
    ): Promise<CreativeDetailsWithRenders> {
        const rendersForCreativeID = await getHumanEvaluationClient()
            .getRendersByCreativeId({
                creativeId,
                creativeIdSpace,
                variantId,
                continuationToken
            })
            .toPromise();

        return {
            creativeDetails: {
                sourceProgram: rendersForCreativeID.sourceProgram,
                sourceSystem: rendersForCreativeID.sourceSystem,
                creativeIDs: rendersForCreativeID.creativeIds.map(e => ({
                    idSpace: e.creativeIdSpace,
                    id: e.creativeId,
                    variantID: e.variantId
                }))
            },
            renders: rendersForCreativeID.renderSummaries.map(r => ({
                id: r.renderId,
                timestamp: new Date(r.startTimestamp),
                renderType: r.renderType,
                device: r.device ?? "(Default)",
                operatingSystem: r.operatingSystem,
                browser: r.browser,
                result: r.result,
                hasLandingPage: r.hasLandingPage,
                hasMalwareDetections: r.hasMalwareDetection,
                hasScreenCaptureFrames: r.hasScreenCaptureFrames ?? false,
                hasScreenCaptureVideo: r.hasScreenCaptureVideo ?? false,
                imagePreviewUrl: r.imagePreviewUrl
            })),
            continuationToken: rendersForCreativeID.continuationToken
        };
    }

    async getRenderDetails(eriskayId: string, renderId: string): Promise<RenderDetails> {
        const rendersForCreativeID = await getHumanEvaluationClient()
            .getRenderData({
                creativeId: eriskayId,
                creativeIdSpace: CreativeIDSpace.ERISKAY_ID,
                renderId
            })
            .toPromise();

        const screenshots = rendersForCreativeID.screenshots;
        return {
            creativeDetails: {
                sourceSystem: rendersForCreativeID.creativeDetails.sourceSystem,
                sourceProgram: rendersForCreativeID.creativeDetails.sourceProgram,
                creativeIDs: rendersForCreativeID.creativeDetails.creativeIds.map(e => ({
                    idSpace: e.creativeIdSpace,
                    id: e.creativeId,
                    variantID: e.variantId
                }))
            },
            renderAttributes: {
                id: renderId,
                startTime: new Date(rendersForCreativeID.renderDetails.startTime),
                completionTime: rendersForCreativeID.renderDetails.completionTime
                    ? new Date(rendersForCreativeID.renderDetails.completionTime)
                    : undefined,
                fidelity: rendersForCreativeID.renderDetails.fidelity,
                renderType: rendersForCreativeID.renderDetails.renderType,
                result: rendersForCreativeID.renderDetails.result,
                detectedLandingPageUrl: rendersForCreativeID.detectedLandingPageUrl
            },
            creativeConfiguration: {
                creativeType: rendersForCreativeID.renderDetails.creativeConfiguration.creativeType,
                markupUrl: rendersForCreativeID.renderDetails.creativeConfiguration.markupUrl,
                publisherPageUrl: rendersForCreativeID.renderDetails.creativeConfiguration.publisherPageUrl,
                protocol: rendersForCreativeID.renderDetails.creativeConfiguration.protocol,
                containerType: rendersForCreativeID.renderDetails.creativeConfiguration.containerType,
                viewportSize: rendersForCreativeID.renderDetails.creativeConfiguration.viewportSize
                    ? {
                          width: rendersForCreativeID.renderDetails.creativeConfiguration.viewportSize?.width,
                          height: rendersForCreativeID.renderDetails.creativeConfiguration.viewportSize?.height
                      }
                    : undefined,
                tcString: rendersForCreativeID.renderDetails.creativeConfiguration.tcString
            },
            targetingConfiguration: {
                geoLocation: {
                    countryCode: rendersForCreativeID.renderDetails.targetingConfiguration.geoLocation.countryCode,
                    zipCode: rendersForCreativeID.renderDetails.targetingConfiguration.geoLocation.zipCode
                },
                browser: rendersForCreativeID.renderDetails.targetingConfiguration.browser,
                operatingSystem: rendersForCreativeID.renderDetails.targetingConfiguration.operatingSystem,
                deviceLayout: rendersForCreativeID.renderDetails.targetingConfiguration.deviceLayout,
                deviceName: rendersForCreativeID.renderDetails.targetingConfiguration.deviceName,
                userAgent: rendersForCreativeID.renderDetails.targetingConfiguration.userAgent
            },
            clickConfiguration: rendersForCreativeID.renderDetails.clickConfiguration
                ? {
                      performClick: rendersForCreativeID.renderDetails.clickConfiguration.performClick ?? false,
                      clickDelay: rendersForCreativeID.renderDetails.clickConfiguration.clickDelay,
                      clickLocation: rendersForCreativeID.renderDetails.clickConfiguration.clickLocation
                          ? {
                                x: rendersForCreativeID.renderDetails.clickConfiguration.clickLocation.x,
                                y: rendersForCreativeID.renderDetails.clickConfiguration.clickLocation.y
                            }
                          : undefined
                  }
                : undefined,
            screenCaptureConfiguration: rendersForCreativeID.renderDetails.screenCaptureConfiguration
                ? {
                      recordScreenCapture:
                          rendersForCreativeID.renderDetails.screenCaptureConfiguration.enableScreenCapture ?? false
                  }
                : undefined,
            screenshots: {
                creativeScreenshots: screenshots?.filter(s => s.renderPartition === RenderPartition.CREATIVE),
                landingPageScreenshot: screenshots?.filter(s => s.renderPartition === RenderPartition.LANDING_PAGE)
            },
            screencastFrames: rendersForCreativeID.screencastFrames,
            screenCaptureVideos: rendersForCreativeID.screenCaptureVideos,
            consoleLogLinesUrl: rendersForCreativeID.consoleLogLinesLocation,
            networkTraceUrl: rendersForCreativeID.networkTraceLocation,
            annotations: this.convertRenderAnnotations(rendersForCreativeID.annotations),
            successfulRenderArtifacts: rendersForCreativeID.successfulRenderArtifacts,
            failedRenderArtifacts: rendersForCreativeID.failedRenderArtifacts
        };
    }

    private convertRenderAnnotations(annotations?: Annotations): RenderAnnotations | undefined {
        if (!annotations) {
            return;
        }

        return {
            malwareDetections: this.convertRenderMalwareDetections(annotations.malwareDetections)
        };
    }

    private convertRenderMalwareDetections(
        malwareDetections?: MalwareDetectionAnnotations
    ): MalwareDetectionAnnotation[] | undefined {
        return malwareDetections?.map(md => ({
            incidentID: md.incidentID,
            description: md.description,
            detectionSourceSystem: md.detectionSourceSystem,
            enforcementType: md.enforcementType
        }));
    }

    loadConsoleLogs(url: string): Promise<string> {
        return fetch(url).then(response => {
            if (response.status === 404) {
                return Promise.reject(
                    "Console Log Lines not found -- they may have expired, or the render type may not support them."
                );
            }

            if (!response.body) {
                return Promise.reject();
            }

            return response
                .blob()
                .then(r => r.arrayBuffer())
                .then(r => pako.inflate(r, {to: "string"}));
        });
    }

    loadHarContent(url: string): Promise<HarFileLog> {
        return fetch(url).then(response => {
            if (response.status === 404) {
                return Promise.reject("HAR File not found -- it may have expired.");
            }

            if (!response.body) {
                return Promise.reject("No HAR Content.");
            }

            return response
                .blob()
                .then(r => r.arrayBuffer())
                .then(r => pako.inflate(r, {to: "string"}))
                .then(r => JSON.parse(r) as HarFileLog);
        });
    }

    async loadScreencastFrameData(url: string): Promise<{
        frames: ScreencastFrameData[];
        metadata: ScreencastFrameMetadata[];
    }> {
        const response = await fetch(url);
        if (response.status === 404) {
            return Promise.reject("Screencast frames zip file not found -- it may have expired.");
        }

        if (!response.body) {
            return Promise.reject("Response did not contain screencast frames content");
        }

        const zipReader = new zip.ZipReader(response.body);
        const entries = await zipReader.getEntries();
        const screencastFramesData = await this.getFramesData(entries);
        const screencastFramesMetadata = await this.getMetadataForFrameData(entries);
        return Promise.resolve({frames: screencastFramesData, metadata: screencastFramesMetadata});
    }

    private getFramesData(entries: zip.Entry[]): Promise<ScreencastFrameData[]> {
        return Promise.all(
            entries
                .filter(entry => entry.filename.toLowerCase().endsWith(".png"))
                .map(async entry => {
                    if (entry.getData !== undefined) {
                        return {
                            url: URL.createObjectURL(await entry.getData(new zip.BlobWriter())),
                            filename: entry.filename
                        };
                    }
                    throw new Error("Invalid state");
                })
        );
    }

    private async getMetadataForFrameData(entries: zip.Entry[]): Promise<ScreencastFrameMetadata[]> {
        const metadataEntry = entries.find(entry => entry.filename === "metadata.json");
        if (metadataEntry === undefined) {
            throw new Error("Metadata file is missing from screencast frame zip");
        }
        if (metadataEntry.getData !== undefined) {
            const text = await (await metadataEntry.getData(new zip.BlobWriter())).text();
            return JSON.parse(text);
        }
        throw new Error("Invalid state");
    }

    async getSignatureCategories(): Promise<string[]> {
        const signatureCategories = await getHumanEvaluationClient().debugGetSignatureCategories({}).toPromise();

        return signatureCategories.signatureCategories?.map(it => it);
    }
}
