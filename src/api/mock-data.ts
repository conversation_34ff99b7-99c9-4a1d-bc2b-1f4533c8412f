import { 
    SignatureStatus, 
    SignatureDecision, 
    RequestedSignatureStatus, 
    SignatureListItem 
} from "./mock-data-types";
import { 
    DetectionDetails as Detection,
    DetectionType, 
    DetectionStatus, 
    MatchType,
    DetectionValidationType,
    DetectionValidationStatus
} from "./detection-types";

// Mock signature data
export const mockSignatures: SignatureListItem[] = [
    {
        signatureId: "sig-abc123456",
        signatureDecisionType: SignatureDecision.THREAT,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "alice.smith",
        auditor: "audit.team1",
        source: "AdRisk Web Threat Hunting",
        description:
            "Malicious domain detected with phishing content targeting financial institutions. Multiple instances observed across different campaigns.",
        creationTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        content: "malicious-phishing.com",
        lineage: "THREAT_HUNTING",
        threatClassification: "PHISHING_DOMAIN",
        requestedStatus: RequestedSignatureStatus.LIVE,
        confidenceLevel: 87.5,
        detectionCount: 2
    },
    {
        signatureId: "sig-def789012",
        signatureDecisionType: SignatureDecision.THREAT,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "bob.jones",
        auditor: "audit.team2",
        source: "Human",
        description:
            "Suspicious domain with malware distribution detected through manual analysis. Contains obfuscated JavaScript that downloads executable files.",
        creationTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        content: "malware-distribution.net",
        lineage: "HUMAN_INTELLIGENCE",
        threatClassification: "MALICIOUS_EXECUTABLE",
        requestedStatus: RequestedSignatureStatus.SHADOW,
        confidenceLevel: 92.3,
        detectionCount: 1
    },
    {
        signatureId: "sig-ghi345678",
        signatureDecisionType: SignatureDecision.SAFETY,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "charlie.brown",
        auditor: "audit.team3",
        source: "Confiant",
        description:
            "False positive domain verification requested by partner. Domain has been verified as legitimate business.",
        creationTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        content: "legitimate-business.org",
        lineage: "EXTERNAL_FEED",
        requestedStatus: RequestedSignatureStatus.LIVE,
        confidenceLevel: 95.8,
        detectionCount: 2
    },
    {
        signatureId: "sig-jkl901234",
        signatureDecisionType: SignatureDecision.THREAT,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "diana.wilson",
        auditor: "audit.team1",
        source: "GeoEdge",
        description:
            "Tech support scam domain identified through partner feed. Uses social engineering to trick users into calling fake support numbers.",
        creationTime: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        content: "tech-support-scam.com",
        lineage: "EXTERNAL_FEED",
        threatClassification: "TECH_SUPPORT_SCAM",
        requestedStatus: RequestedSignatureStatus.LIVE,
        confidenceLevel: 89.2,
        detectionCount: 1
    },
    {
        signatureId: "sig-mno567890",
        signatureDecisionType: SignatureDecision.THREAT,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "eve.davis",
        auditor: "audit.team2",
        source: "TMT",
        description:
            "Investment scam domain promoting fake cryptocurrency platform. High risk of financial fraud targeting vulnerable users.",
        creationTime: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
        content: "crypto-investment-scam.biz",
        lineage: "THREAT_HUNTING",
        threatClassification: "INVESTMENT_SCAM",
        requestedStatus: RequestedSignatureStatus.SHADOW,
        confidenceLevel: 94.7,
        detectionCount: 2
    },
    {
        signatureId: "sig-pqr123456",
        signatureDecisionType: SignatureDecision.THREAT,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "frank.miller",
        auditor: "audit.team3",
        source: "AdRisk Web Threat Hunting",
        description: "Phishing domain targeting banking credentials with fake login forms.",
        creationTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        content: "bank-secure-login.net",
        lineage: "THREAT_HUNTING",
        threatClassification: "PHISHING_DOMAIN",
        requestedStatus: RequestedSignatureStatus.LIVE,
        confidenceLevel: 96.2,
        detectionCount: 3
    },
    {
        signatureId: "sig-stu789012",
        signatureDecisionType: SignatureDecision.THREAT,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "grace.huang",
        auditor: "audit.team1",
        source: "Human",
        description: "Malvertising campaign redirecting to exploit kit landing pages.",
        creationTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        content: "ads-tracking-metrics.com",
        lineage: "HUMAN_INTELLIGENCE",
        threatClassification: "MALVERTISING",
        requestedStatus: RequestedSignatureStatus.SHADOW,
        confidenceLevel: 88.9,
        detectionCount: 4
    },
    {
        signatureId: "sig-vwx345678",
        signatureDecisionType: SignatureDecision.SAFETY,
        status: SignatureStatus.PENDING_MANUAL_REVIEW,
        creator: "henry.ford",
        auditor: "audit.team2",
        source: "Confiant",
        description: "Potential false positive for legitimate advertising network.",
        creationTime: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
        content: "ad-metrics-analytics.com",
        lineage: "EXTERNAL_FEED",
        requestedStatus: RequestedSignatureStatus.LIVE,
        confidenceLevel: 45.3,
        detectionCount: 2
    }
];

// Mock detection data for each signature
export const mockDetections: Record<string, Detection[]> = {
    "sig-abc123456": [
        {
            detectionId: "det-12345",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-67890",
            signatureId: "sig-abc123456",
            landingPage: "https://malicious-phishing.com/login",
            renderId: "render-12345",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.EXACT,
            metadata: JSON.stringify({
                browser: "Chrome",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "search-results.com/query?q=banking+login",
                cookies: "session=abc123; tracking=enabled"
            }),
            validationDetails: {
                confidence: "95.2",
                source: "automated_scan",
                verificationMethod: "visual_analysis",
                analysisTime: "1.2s"
            }
        },
        {
            detectionId: "det-23456",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-78901",
            signatureId: "sig-abc123456",
            landingPage: "https://malicious-phishing.com/account",
            renderId: "render-23456",
            status: DetectionStatus.PENDING_VERIFICATION,
            creationTime: new Date(Date.now() - 1.5 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 0.5 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.SIMILARITY,
            metadata: JSON.stringify({
                browser: "Firefox",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:96.0)",
                referrer: "email-link.com/click/id=12345",
                cookies: "session=def456; tracking=disabled"
            }),
            validationDetails: {
                confidence: "87.5",
                source: "automated_scan",
                verificationMethod: "domain_analysis",
                analysisTime: "0.8s"
            }
        }
    ],
    "sig-def789012": [
        {
            detectionId: "det-34567",
            detectionType: DetectionType.SIMULATED,
            eriskayId: "eri-89012",
            signatureId: "sig-def789012",
            landingPage: "https://malware-distribution.net/download",
            renderId: "render-34567",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.EXACT,
            metadata: JSON.stringify({
                browser: "Edge",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "download-software.com/free-tools",
                cookies: "session=ghi789; tracking=enabled; preferences=dark-mode"
            }),
            validationDetails: {
                confidence: "98.7",
                source: "manual_verification",
                verificationMethod: "code_analysis",
                analysisTime: "3.5s",
                malwareType: "trojan_downloader"
            }
        }
    ],
    "sig-ghi345678": [
        {
            detectionId: "det-45678",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-90123",
            signatureId: "sig-ghi345678",
            landingPage: "https://legitimate-business.org/products",
            renderId: "render-45678",
            status: DetectionStatus.FALSE_POSITIVE,
            creationTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 0.2 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.SIMILARITY,
            metadata: JSON.stringify({
                browser: "Safari",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15",
                referrer: "google.com/search?q=business+products",
                cookies: "session=jkl012; cart=empty; region=us-west"
            }),
            validationDetails: {
                confidence: "23.4",
                source: "manual_verification",
                verificationMethod: "business_verification",
                analysisTime: "12.5s",
                verificationNotes: "Confirmed legitimate business with valid registration"
            }
        },
        {
            detectionId: "det-56789",
            detectionType: DetectionType.SIMULATED,
            eriskayId: "eri-01234",
            signatureId: "sig-ghi345678",
            landingPage: "https://legitimate-business.org/contact",
            renderId: "render-56789",
            status: DetectionStatus.FALSE_POSITIVE,
            creationTime: new Date(Date.now() - 0.5 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 0.1 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.SIMILARITY,
            metadata: JSON.stringify({
                browser: "Chrome",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "directory-listing.com/businesses",
                cookies: "session=mno345; preferences=high-contrast"
            }),
            validationDetails: {
                confidence: "18.9",
                source: "automated_scan",
                verificationMethod: "content_analysis",
                analysisTime: "0.7s"
            }
        }
    ],
    "sig-jkl901234": [
        {
            detectionId: "det-67890",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-12345",
            signatureId: "sig-jkl901234",
            landingPage: "https://tech-support-scam.com/alert",
            renderId: "render-67890",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.EXACT,
            metadata: JSON.stringify({
                browser: "Chrome",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "search-results.com/query?q=computer+help",
                popupCount: "3",
                audioPlayed: "true"
            }),
            validationDetails: {
                confidence: "91.3",
                source: "automated_scan",
                verificationMethod: "behavioral_analysis",
                analysisTime: "2.3s",
                scamType: "tech_support"
            }
        }
    ],
    "sig-mno567890": [
        {
            detectionId: "det-78901",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-23456",
            signatureId: "sig-mno567890",
            landingPage: "https://crypto-investment-scam.biz/invest",
            renderId: "render-78901",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.EXACT,
            metadata: JSON.stringify({
                browser: "Firefox",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:96.0)",
                referrer: "investment-news.com/crypto-boom",
                formFields: "name,email,phone,investment_amount"
            }),
            validationDetails: {
                confidence: "96.8",
                source: "manual_verification",
                verificationMethod: "financial_scam_analysis",
                analysisTime: "5.2s",
                scamType: "investment_fraud"
            }
        },
        {
            detectionId: "det-89012",
            detectionType: DetectionType.SIMULATED,
            eriskayId: "eri-34567",
            signatureId: "sig-mno567890",
            landingPage: "https://crypto-investment-scam.biz/signup",
            renderId: "render-89012",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 4.5 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 3.5 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.SIMILARITY,
            metadata: JSON.stringify({
                browser: "Edge",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "social-media.com/ad/crypto-returns",
                formFields: "email,investment_amount,payment_method"
            }),
            validationDetails: {
                confidence: "89.2",
                source: "automated_scan",
                verificationMethod: "pattern_matching",
                analysisTime: "1.8s"
            }
        }
    ],
    "sig-pqr123456": [
        {
            detectionId: "det-90123",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-45678",
            signatureId: "sig-pqr123456",
            landingPage: "https://bank-secure-login.net/auth",
            renderId: "render-90123",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 0.8 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 0.3 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.EXACT,
            metadata: JSON.stringify({
                browser: "Chrome",
                ipAddress: "***********",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "email-link.com/secure-message",
                formFields: "username,password,account_number"
            }),
            validationDetails: {
                confidence: "97.5",
                source: "automated_scan",
                verificationMethod: "visual_comparison",
                analysisTime: "1.1s",
                targetedBank: "Multiple"
            }
        },
        {
            detectionId: "det-01234",
            detectionType: DetectionType.ONLINE,
            eriskayId: "eri-56789",
            signatureId: "sig-pqr123456",
            landingPage: "https://bank-secure-login.net/verify",
            renderId: "render-01234",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 0.6 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 0.2 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.EXACT,
            metadata: JSON.stringify({
                browser: "Safari",
                ipAddress: "***********0",
                userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
                referrer: "sms-link.com/alert",
                formFields: "username,password,security_questions"
            }),
            validationDetails: {
                confidence: "98.1",
                source: "manual_verification",
                verificationMethod: "domain_analysis",
                analysisTime: "2.5s",
                targetedBank: "National Bank"
            }
        },
        {
            detectionId: "det-12345a",
            detectionType: DetectionType.SIMULATED,
            eriskayId: "eri-67890a",
            signatureId: "sig-pqr123456",
            landingPage: "https://bank-secure-login.net/confirm",
            renderId: "render-12345a",
            status: DetectionStatus.UNSAFE,
            creationTime: new Date(Date.now() - 0.4 * 24 * 60 * 60 * 1000).toISOString(),
            lastUpdatedTime: new Date(Date.now() - 0.1 * 24 * 60 * 60 * 1000).toISOString(),
            matchType: MatchType.SIMILARITY,
            metadata: JSON.stringify({
                browser: "Edge",
                ipAddress: "***********1",
                userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                referrer: "search-results.com/query?q=bank+login",
                formFields: "username,password,token"
            }),
            validationDetails: {
                confidence: "94.3",
                source: "automated_scan",
                verificationMethod: "content_analysis",
                analysisTime: "0.9s"
            }
        }
    ]
};

// Add detection counts to signatures based on mock detections
for (const signatureId in mockDetections) {
    const signature = mockSignatures.find(sig => sig.signatureId === signatureId);
    if (signature) {
        signature.detectionCount = mockDetections[signatureId].length;
    }
}