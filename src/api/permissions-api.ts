import {getHumanEvaluationClient} from "./index";

export class PermissionsApi {
    private userPermissions: string[] | undefined = undefined;

    /**
     * Fetches all permissions for this user. The return value of this call is cached, and so subsequent calls will just
     * return the cached value.
     */
    async getUserPermissions(): Promise<string[]> {
        if (this.userPermissions) {
            return this.userPermissions;
        }
        const newPermissions = await getHumanEvaluationClient()
            .getPermissions({})
            .toPromise()
            .then(p => p.permissions.map(p => p.permissionID));

        this.userPermissions = newPermissions;

        return newPermissions;
    }
}
