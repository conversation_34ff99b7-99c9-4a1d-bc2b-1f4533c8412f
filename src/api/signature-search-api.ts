import {ValidationStatus} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {mockSignatures} from "./mock-data";
import {
    SignatureStatus,
    SignatureDecision,
    RequestedSignatureStatus,
    SignatureListItem as SignatureListItemType
} from "./mock-data-types";

export {
    SignatureStatus,
    SignatureDecision,
    RequestedSignatureStatus
};

export type SignatureListItem = SignatureListItemType;

export interface SearchSignaturesRequest {
    status: SignatureStatus;
    maxResults?: number;
    nextToken?: string;
}

export interface SearchSignaturesResponse {
    items: SignatureListItem[];
    nextToken?: string;
    totalCount: number;
}

export {ValidationStatus as Status};

export class SignatureSearchApi {
    private generateMockSignatures(count: number, status: SignatureStatus): SignatureListItem[] {
        const mockSignatures: SignatureListItem[] = [];
        const domains = [
            "malicious-site.com",
            "phishing-domain.net",
            "suspicious-ads.org",
            "threat-actor.io",
            "scam-website.biz"
        ];
        const sources = ["AdRisk Web Threat Hunting", "Human", "Confiant", "GeoEdge", "TMT"];
        const creators = ["alice.smith", "bob.jones", "charlie.brown", "diana.wilson", "eve.davis"];
        const auditors = ["audit.team1", "audit.team2", "audit.team3"];

        for (let i = 0; i < count; i++) {
            const domain = domains[i % domains.length];
            mockSignatures.push({
                signatureId: `sig-${Math.random().toString(36).substr(2, 9)}`,
                signatureDecisionType: Math.random() > 0.5 ? SignatureDecision.THREAT : SignatureDecision.SAFETY,
                status,
                creator: creators[i % creators.length],
                auditor: auditors[i % auditors.length],
                source: sources[i % sources.length],
                description: `Signature for ${domain} - detected malicious activity`,
                creationTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
                content: domain,
                lineage: "THREAT_HUNTING",
                threatClassification: "MALICIOUS_DOMAIN",
                requestedStatus: Math.random() > 0.5 ? RequestedSignatureStatus.LIVE : RequestedSignatureStatus.SHADOW,
                confidenceLevel: parseFloat((Math.random() * 100).toFixed(2)),
                detectionCount: Math.floor(Math.random() * 5) + 1
            });
        }

        return mockSignatures.sort((a, b) => new Date(b.creationTime).getTime() - new Date(a.creationTime).getTime());
    }

    async searchSignatures(request: SearchSignaturesRequest): Promise<SearchSignaturesResponse> {
        // Mock delay to simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));

        const maxResults = request.maxResults || 50;
        
        // Use predefined mock data if available, otherwise generate random data
        const data = mockSignatures.length > 0 ? mockSignatures : this.generateMockSignatures(50, request.status);
        
        // Filter by status if needed
        const filteredData = data.filter(sig => sig.status === request.status);

        const startIndex = request.nextToken ? parseInt(request.nextToken) : 0;
        const endIndex = Math.min(startIndex + maxResults, filteredData.length);
        const items = filteredData.slice(startIndex, endIndex);

        const nextToken = endIndex < filteredData.length ? endIndex.toString() : undefined;

        return {
            items,
            nextToken,
            totalCount: filteredData.length
        };
    }
}