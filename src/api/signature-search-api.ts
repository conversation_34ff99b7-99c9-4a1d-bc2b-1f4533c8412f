import {getHumanEvaluationClient} from "./index";

export enum SignatureStatus {
    SHADOW = "shadow",
    LIVE = "live",
    DELETED = "deleted",
    PROPOSED = "proposed",
    PENDING_AUTO_VERIFICATION = "pending_auto_verification",
    REJECTED = "rejected",
    PENDING_MANUAL_REVIEW = "pending_manual_review",
    LIVE_REFERRAL = "live_referral"
}

export enum SignatureDecisionType {
    THREAT = "threat",
    SAFETY = "safety"
}

export interface SignatureListItem {
    readonly signatureId: string;
    readonly signatureDecisionType: SignatureDecisionType;
    readonly status: SignatureStatus;
    readonly creator: string;
    readonly auditor: string;
    readonly source: string;
    readonly description: string;
    readonly creationTime: string;
    readonly content: string;
    readonly lineage?: string;
    readonly threatClassification?: string;
}

export interface SearchSignaturesRequest {
    readonly status: SignatureStatus;
    readonly maxResults?: number;
    readonly nextToken?: string;
}

export interface SearchSignaturesResponse {
    readonly items: SignatureListItem[];
    readonly nextToken?: string;
    readonly totalCount: number;
}

interface BackendSignatureListItem {
    readonly signatureId: string;
    readonly signatureDecisionType: string;
    readonly status: string;
    readonly creator: string;
    readonly auditor: string;
    readonly source: string;
    readonly description: string;
    readonly creationTime: string;
    readonly content: string;
    readonly lineage?: string;
    readonly threatClassification?: string;
}

interface BackendSearchSignaturesResponse {
    readonly items: BackendSignatureListItem[];
    readonly nextToken?: string;
    readonly totalCount: number;
}

export class SignatureSearchApi {
    async searchSignatures(request: SearchSignaturesRequest): Promise<SearchSignaturesResponse> {
        console.log("SignatureSearchApi.searchSignatures called with:", request);
        
        try {
            const client = getHumanEvaluationClient();
            console.log("Got client:", client);

            // Build query parameters
            const queryParams: Record<string, string> = {
                status: request.status
            };
            
            if (request.maxResults) {
                queryParams.maxResults = request.maxResults.toString();
            }
            if (request.nextToken) {
                queryParams.nextToken = request.nextToken;
            }

            console.log("Calling searchSignatures with params:", queryParams);

            const response = await client.searchSignatures(queryParams).toPromise();
            console.log("Backend response:", response);

            const backendResponse = response as BackendSearchSignaturesResponse;

            return {
                items: backendResponse.items.map((item: BackendSignatureListItem) => ({
                    signatureId: item.signatureId,
                    signatureDecisionType: item.signatureDecisionType as SignatureDecisionType,
                    status: item.status as SignatureStatus,
                    creator: item.creator,
                    auditor: item.auditor,
                    source: item.source,
                    description: item.description,
                    creationTime: item.creationTime,
                    content: item.content,
                    lineage: item.lineage,
                    threatClassification: item.threatClassification
                })),
                nextToken: backendResponse.nextToken,
                totalCount: backendResponse.totalCount
            };
        } catch (error) {
            console.error("Error searching signatures:", error);
            throw error;
        }
    }

    async searchReferredSignatures(): Promise<SearchSignaturesResponse> {
        return this.searchSignatures({ status: SignatureStatus.LIVE_REFERRAL });
    }
}
