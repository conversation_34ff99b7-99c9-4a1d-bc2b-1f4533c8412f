// Detection types based on the Smithy model

export enum DetectionType {
    ONLINE = "ONLINE",
    SIMULATED = "SIMULATED"
}

export enum DetectionStatus {
    PENDING_VERIFICATION = "PENDING_VERIFICATION",
    UNSAFE = "UNSAFE",
    FALSE_POSITIVE = "FALSE_POSITIVE",
    SHADOW = "SHADOW"
}

export enum MatchType {
    EXACT = "EXACT",
    SIMILARITY = "SIMILARITY"
}

export enum DetectionValidationType {
    ALLOWLIST = "ALLOWLIST",
    PROFOUND = "PROFOUND",
    MANUAL = "MANUAL"
}

export enum DetectionValidationStatus {
    PENDING_VALIDATION = "PENDING_VALIDATION",
    VALIDATED_DETECTION = "VALIDATED_DETECTION",
    FALSE_DETECTION = "FALSE_DETECTION",
    FAILED_VALIDATION = "FAILED_VALIDATION",
    SKIPPED = "SKIPPED"
}

export enum DetectionValidationReason {
    NO_MALICIOUS_SIGNAL = "NO_MALICIOUS_SIGNAL",
    VERIFIED_MALICIOUS_SIGNAL = "VERIFIED_MALICIOUS_SIGNAL",
    NONE = "NONE",
    DOMAIN_DOESNT_EXIST = "DOMAIN_DOESNT_EXIST",
    CANNOT_VERIFY = "CANNOT_VERIFY",
    UNREGISTERED_DOMAIN = "UNREGISTERED_DOMAIN"
}

export interface DetectionValidationDetails {
    status: DetectionValidationStatus;
    reason?: DetectionValidationReason;
}

// For backward compatibility with existing code
export interface DetectionDetails {
    detectionId: string;
    detectionType: DetectionType;
    eriskayId?: string;
    signatureId: string;
    landingPage?: string;
    renderId?: string;
    status: DetectionStatus;
    creationTime: string;
    lastUpdatedTime?: string;
    matchType?: MatchType;
    validationDetails?: any; // Using any to avoid type errors with existing code
    metadata?: string;
}

// Sample SIMULATED detection based on the schema
export const sampleSimulatedDetection: DetectionDetails = {
    detectionId: "det-simulated123",
    detectionType: DetectionType.SIMULATED,
    eriskayId: "eri-12345",
    signatureId: "sig-abc123456",
    landingPage: "https://test-domain.com/landing",
    renderId: "render-67890",
    status: DetectionStatus.PENDING_VERIFICATION,
    creationTime: new Date().toISOString(),
    lastUpdatedTime: new Date().toISOString(),
    matchType: MatchType.SIMILARITY,
    validationDetails: {
        [DetectionValidationType.ALLOWLIST]: {
            status: DetectionValidationStatus.PENDING_VALIDATION
        },
        [DetectionValidationType.PROFOUND]: {
            status: DetectionValidationStatus.SKIPPED
        },
        [DetectionValidationType.MANUAL]: {
            status: DetectionValidationStatus.PENDING_VALIDATION
        }
    },
    metadata: JSON.stringify({
        browser: "Chrome",
        ipAddress: "***********",
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        testCase: "simulated_phishing_test"
    })
};