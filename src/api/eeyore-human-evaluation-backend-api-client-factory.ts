import {
    EeyoreHumanEvaluationBackendApi,
    eeyoreHumanEvaluationBackendApiHttpBindingInterceptor
} from "@amzn/coral_com-amazon-eeyore-humanevaluation-model";
import {MidwayIdentityCredentialProviderCognito} from "@amzn/midway-identity-credential-provider";
import {
    CoralHttpConfig,
    InterceptingHandler,
    Aws4HmacSha256Interceptor,
    CoralInterceptor,
    CoralRequest,
    CoralResponse,
    CoralHandler,
    CoralFetchHandler
} from "@amzn/ihm-js-coral-client";

import {Observable} from "rxjs";

export class EeyoreHumanEvaluationBackendApiClientFactory {
    public static client(clientConfig: CoralHttpConfig): EeyoreHumanEvaluationBackendApi {
        const credentials = MidwayIdentityCredentialProviderCognito.newProvider(clientConfig);

        if (!credentials) {
            throw new Error("No credentials available.");
        }

        if (clientConfig.httpSigning?.scheme !== "aws4-hmac-sha256") {
            throw new Error(`Invalid scheme [${clientConfig.httpSigning}]`);
        }

        const aws4HmacSha256Interceptor = new Aws4HmacSha256Interceptor(async () => {
            // credentials could expire
            if (credentials.needsRefresh()) {
                await credentials.getPromise();
            }
            return {
                accessKeyId: credentials.accessKeyId,
                secretAccessKey: credentials.secretAccessKey,
                sessionToken: credentials.sessionToken
            };
        });

        const interceptors: CoralInterceptor[] = [
            eeyoreHumanEvaluationBackendApiHttpBindingInterceptor,
            new AuxiliaryRequestInterceptor(),
            aws4HmacSha256Interceptor
        ];

        const client = new EeyoreHumanEvaluationBackendApi(
            clientConfig,
            new InterceptingHandler(new CoralFetchHandler(), interceptors)
        );

        return client;
    }
}

/**
 * Massages the outgoing requests to make CORS and GET requests happy
 */
class AuxiliaryRequestInterceptor extends CoralInterceptor {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    intercept(request: CoralRequest, next: CoralHandler): Observable<CoralResponse<any>> {
        return next.handle(
            this.excludeContentEncodingHeader(
                this.addOmitCredentials(this.replaceNullOrEmptyFieldsOfBodyWithUndefined(request))
            )
        );
    }

    /**
     * A fix to avoid generating body for GET requests.
     * Ref 1: This is added here: https://code.amazon.com/packages/IhmJSCoralClient/blobs/3bdd1a1f5735f4c3c14350cd8273cf5fda6841ac/--/src/interceptors/coral-client-http-binding-interceptor.ts#L56
     * Ref 2: It is used here: https://code.amazon.com/packages/IhmJSCoralClient/blobs/95d6123041b0a66d6f1a940058849ca1adc25000/--/src/interceptors/aws4-hmac-sha256-interceptor.ts#L78
     */
    private replaceNullOrEmptyFieldsOfBodyWithUndefined(request: CoralRequest): CoralRequest {
        return request.clone({
            body: cleanNullOrEmptyToUndefined(request.body)
        });
    }

    /**
     * FIX: To avoid CORS access control errors when credential mode is `include`
     * In CoralRequest class, `credentials` are by default set to `included` if it is either undefined | null | empty string
     * https://code.amazon.com/packages/IhmJSCoralClient/blobs/2166c3ea992ea581bea925c309907f20b2cb7863/--/src/client/coral-request.ts#L59
     * Hence to avoid switching to `include` automatically, setting it to `omit`
     */
    private addOmitCredentials(request: CoralRequest): CoralRequest {
        return request.clone({
            credentials: "omit"
        });
    }

    /**
     * FIX: Request header field content-encoding is not allowed by Access-Control-Allow-Headers in preflight response
     */
    private excludeContentEncodingHeader(request: CoralRequest): CoralRequest {
        const headersToExclude = ["Content-Encoding"];
        return request.clone({
            headers: request.headers.filter(header => !headersToExclude.includes(header[0]))
        });
    }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function cleanNullToUndefined(obj: any): any {
    if (obj === null) {
        return undefined;
    }
    if (typeof obj !== "object") {
        return obj;
    }
    if (obj instanceof Array) {
        return obj.map(cleanNullToUndefined);
    }

    return Object.keys(obj).reduce(
        (result, key) => ({
            ...result,
            [key]: cleanNullToUndefined(obj[key])
        }),
        {}
    );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function cleanNullOrEmptyToUndefined(obj: any): any {
    if (obj === null || obj === "") {
        return undefined;
    }
    if (typeof obj !== "object") {
        return obj;
    }
    if (obj instanceof Array) {
        if (isStringArray(obj)) {
            return obj.map(cleanNullToUndefined); // keep empty strings if it's a string array
        } else {
            return obj.map(cleanNullOrEmptyToUndefined);
        }
    }

    return Object.keys(obj).reduce(
        (result, key) => ({
            ...result,
            [key]: cleanNullOrEmptyToUndefined(obj[key])
        }),
        {}
    );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const isStringArray = (test: any[]): boolean => {
    return Array.isArray(test) && test.every(value => typeof value === "string");
};
