<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">
    <documentation target="ScatterPlotListRequests">
        List all stored scatter plot requests
    </documentation>

    <operation name="ScatterPlotListRequests">
        <input target="ScatterPlotListRequestsRequest"/>
        <output target="ScatterPlotListRequestsResponse"/>
        <error target="InternalException"/>
    </operation>

    <http target="ScatterPlotListRequests">
        <verb value="GET"/>
        <uri value="scatterplot_list_requests"/>
    </http>

    <structure name="ScatterPlotListRequestsRequest"/>

    <structure name="ScatterPlotListRequestsResponse">
        <member name="requests" target="ScatterPlotRequestedPlotList"/>
    </structure>

    <required target="ScatterPlotListRequestsResponse$requests"/>

    <list name="ScatterPlotRequestedPlotList">
        <member target="ScatterPlotRequestedPlot"/>
    </list>

    <structure name="ScatterPlotRequestedPlot">
        <member name="outputLocation" target="stringValue"/>
        <member name="requestStartTimestamp" target="stringValue"/>
        <member name="user" target="stringValue"/>
        <member name="tag" target="stringValue"/>
    </structure>

    <required target="ScatterPlotRequestedPlot$outputLocation"/>
    <required target="ScatterPlotRequestedPlot$requestStartTimestamp"/>
    <required target="ScatterPlotRequestedPlot$user"/>
    <required target="ScatterPlotRequestedPlot$tag"/>

</definition>
