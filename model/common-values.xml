<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <!-- Primitive types used by other structures. -->

    <integer name="intValue"/>

    <long name="longValue"/>

    <string name="stringValue"/>

    <boolean name="booleanValue"/>

    <float name="floatValue"/>

    <double name="doubleValue"/>

    <!-- Used to represent a timestamp in ISO 8601 format -->
    <string name="timestampValue"/>

    <!-- Used to represent a relative number of milliseconds -->
    <long name="relativeMilliseconds"/>

    <list name="doubleList">
        <member target="doubleValue"/>
    </list>

    <list name="stringList">
        <member target="stringValue"/>
    </list>

    <list name="stringListList">
        <member target="stringList"/>
    </list>

    <string name="CreativeIDSpace" />
    <enum target="CreativeIDSpace">
        <!-- System Identifiers -->
        <enumValue value="eriskayCreativeIDSpace" name="ERISKAY_ID" />

        <!-- Client System Identifiers -->
        <enumValue value="eriskayRodeoCfidSpace" name="RODEO_CFID" />
        <enumValue value="eriskayAxiomIDSpace" name="AXIOM_ID" />
        <enumValue value="eriskayAaxCridSpace" name="AAX_CRID" />
        <enumValue value="eriskayCanaryIDSpace" name="CANARY_ID" />
        <enumValue value="eriskayIntegIDSpace" name="INTEG_ID" />
        <enumValue value="eriskayLoadTestIDSpace" name="LOADTEST_ID" />
    </enum>
    <enumjava target="CreativeIDSpace" >
        <class value="com.amazon.eeyore.humanevaluation.model.CreativeIDSpace" />
    </enumjava>
</definition>
