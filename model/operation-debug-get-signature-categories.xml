<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="DebugGetSignatureCategories">
        List all the available signature categories, separated by commas
    </documentation>

    <operation name="DebugGetSignatureCategories">
        <input target="DebugGetSignatureCategoriesRequest"/>
        <output target="DebugGetSignatureCategoriesResponse"/>
        <error target="InternalException"/>
    </operation>

    <http target="DebugGetSignatureCategories">
        <verb value="GET"/>
        <uri value="debug_getsignaturecategories"/>
    </http>

    <structure name="DebugGetSignatureCategoriesRequest">
    </structure>

    <structure name="DebugGetSignatureCategoriesResponse">
        <member name="signatureCategories" target="stringList"/>
    </structure>

    <required target="DebugGetSignatureCategoriesResponse$signatureCategories" />
</definition>
