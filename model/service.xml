<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="EeyoreHumanEvaluationBackendApi">
        Backend Api used by HumanEvaluation UI
    </documentation>

    <service name="EeyoreHumanEvaluationBackendApi">
        <operation target="GetIdentity"/>
        <operation target="GetPermissions"/>

        <!-- Creative Browser -->
        <operation target="GetRendersByCreativeId"/>
        <operation target="GetRenderData"/>

        <!-- Templated Data Loch Queries -->
        <operation target="DebugTemplatedQueryStart"/>
        <operation target="DebugTemplatedQueryResult"/>
        <operation target="DebugGetSignatureCategories"/>

        <!-- Scatter Plot Operations -->
        <operation target="ScatterPlotStartPlot"/>
        <operation target="ScatterPlotProgress"/>
        <operation target="ScatterPlotListRequests"/>
        
        <!-- Signature Operations -->
        <operation target="CreateSignatures"/>
        <operation target="SearchSignatures"/>
        <operation target="ChangeSignatureStatus"/>

        <!-- Detection Operations -->
        <operation target="SearchDetections"/>
    </service>

</definition>
