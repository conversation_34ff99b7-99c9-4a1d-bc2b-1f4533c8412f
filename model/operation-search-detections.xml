<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="SearchDetections">
        Search for detections based on signature ID and other criteria using Woozle ThreatStore API.
    </documentation>

    <operation name="SearchDetections">
        <input target="SearchDetectionsRequest"/>
        <output target="SearchDetectionsResponse"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
        <error target="InternalException"/>
        <error target="InvalidRequestParametersException"/>
    </operation>

    <http target="SearchDetections">
        <verb value="GET"/>
        <uri value="detections?signatureId={signatureId}&amp;detectionType={detectionType}&amp;maxResults={maxResults}&amp;nextToken={nextToken}&amp;createdAfter={createdAfter}&amp;createdBefore={createdBefore}"/>
    </http>

    <structure name="SearchDetectionsRequest">
        <member name="signatureId" target="stringValue"/>
        <member name="detectionType" target="DetectionType"/>
        <member name="maxResults" target="intValue"/>
        <member name="nextToken" target="stringValue"/>
        <member name="createdAfter" target="timestampValue"/>
        <member name="createdBefore" target="timestampValue"/>
    </structure>

    <required target="SearchDetectionsRequest$signatureId" />
    <required target="SearchDetectionsRequest$detectionType" />

    <httplabel target="SearchDetectionsRequest$signatureId">
        <label value="signatureId"/>
    </httplabel>
    <httplabel target="SearchDetectionsRequest$detectionType">
        <label value="detectionType"/>
    </httplabel>
    <httplabel target="SearchDetectionsRequest$maxResults">
        <label value="maxResults"/>
    </httplabel>
    <httplabel target="SearchDetectionsRequest$nextToken">
        <label value="nextToken"/>
    </httplabel>
    <httplabel target="SearchDetectionsRequest$createdAfter">
        <label value="createdAfter"/>
    </httplabel>
    <httplabel target="SearchDetectionsRequest$createdBefore">
        <label value="createdBefore"/>
    </httplabel>

    <!-- Detection Type Enum -->
    <string name="DetectionType" />
    <enum target="DetectionType">
        <enumValue value="Simulated" name="SIMULATED" />
        <enumValue value="Online" name="ONLINE" />
    </enum>
    <enumjava target="DetectionType">
        <class value="com.amazon.eeyore.humanevaluation.model.DetectionType" />
    </enumjava>

    <!-- Detection Status Enum -->
    <string name="DetectionStatus" />
    <enum target="DetectionStatus">
        <enumValue value="PENDING_VERIFICATION" name="PENDING_VERIFICATION" />
        <enumValue value="UNSAFE" name="UNSAFE" />
        <enumValue value="FALSE_POSITIVE" name="FALSE_POSITIVE" />
        <enumValue value="SHADOW" name="SHADOW" />
    </enum>
    <enumjava target="DetectionStatus">
        <class value="com.amazon.eeyore.humanevaluation.model.DetectionStatus" />
    </enumjava>

    <!-- Match Type Enum -->
    <string name="MatchType" />
    <enum target="MatchType">
        <enumValue value="EXACT" name="EXACT" />
        <enumValue value="SIMILARITY" name="SIMILARITY" />
    </enum>
    <enumjava target="MatchType">
        <class value="com.amazon.eeyore.humanevaluation.model.MatchType" />
    </enumjava>

    <!-- Validation Details Map -->
    <map name="ValidationDetailsMap">
        <key target="stringValue"/>
        <value target="stringValue"/>
    </map>

    <!-- Detection Structure -->
    <structure name="Detection">
        <member name="detectionId" target="stringValue"/>
        <member name="detectionType" target="DetectionType"/>
        <member name="eriskayId" target="stringValue"/>
        <member name="signatureId" target="stringValue"/>
        <member name="landingPage" target="stringValue"/>
        <member name="renderId" target="stringValue"/>
        <member name="status" target="DetectionStatus"/>
        <member name="creationTime" target="timestampValue"/>
        <member name="lastUpdatedTime" target="timestampValue"/>
        <member name="matchType" target="MatchType"/>
        <member name="metadata" target="stringValue"/>
        <member name="validationDetails" target="ValidationDetailsMap"/>
    </structure>

    <required target="Detection$detectionId" />
    <required target="Detection$detectionType" />
    <required target="Detection$signatureId" />
    <required target="Detection$status" />
    <required target="Detection$creationTime" />

    <!-- Detection List -->
    <list name="DetectionList">
        <member target="Detection"/>
    </list>

    <!-- Search Detections Response -->
    <structure name="SearchDetectionsResponse">
        <member name="items" target="DetectionList"/>
        <member name="nextToken" target="stringValue"/>
    </structure>

    <required target="SearchDetectionsResponse$items" />

</definition>
