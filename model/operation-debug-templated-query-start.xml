<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="DebugTemplatedQueryStart">
        Start a templated query
    </documentation>

    <operation name="DebugTemplatedQueryStart">
        <input target="DebugTemplatedQueryStartRequest"/>
        <output target="DebugTemplatedQueryStartResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
        <error target="NotFoundException"/>
    </operation>

    <http target="DebugTemplatedQueryStart">
        <verb value="POST"/>
        <uri value="debug_templatedquerystart"/>
    </http>

    <structure name="DebugTemplatedQueryStartRequest">
        <member name="templateId" target="stringValue"/>
        <member name="params" target="stringValue"/>
    </structure>

    <structure name="DebugTemplatedQueryStartResponse">
        <member name="queryExecutionId" target="stringValue"/>
    </structure>

    <required target="DebugTemplatedQueryStartResponse$queryExecutionId"/>
</definition>
