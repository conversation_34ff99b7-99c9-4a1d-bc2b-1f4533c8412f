<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <string name="errorMessage"/>

    <!-- ==================================== -->

    <documentation target="InternalException">
        This exception is thrown in case of an internal error.
    </documentation>

    <structure name="InternalException">
        <member name="message" target="errorMessage"/>
    </structure>

    <exception target="InternalException"/>

    <httperror target="InternalException">
        <httpresponsecode value="500"/>
    </httperror>

    <!-- ==================================== -->

    <structure name="ClientFaultException" abstract="true">
        <member name="message" target="errorMessage"/>
    </structure>

    <exception target="ClientFaultException"/>

    <httperror target="ClientFaultException">
        <httpresponsecode value="400"/>
    </httperror>

    <!-- ==================================== -->

    <structure name="MissingParametersException" isa="ClientFaultException"/>

    <exception target="MissingParametersException"/>

    <httperror target="MissingParametersException">
        <httpresponsecode value="400"/>
    </httperror>

    <!-- ==================================== -->

    <structure name="InvalidRequestParametersException" isa="ClientFaultException"/>

    <exception target="InvalidRequestParametersException"/>

    <httperror target="InvalidRequestParametersException">
        <httpresponsecode value="400"/>
    </httperror>

    <!-- ==================================== -->

    <structure name="NotFoundException" isa="ClientFaultException"/>

    <exception target="NotFoundException"/>

    <httperror target="NotFoundException">
        <httpresponsecode value="404"/>
    </httperror>

    <!-- ==================================== -->

    <structure name="ForbiddenException" isa="ClientFaultException"/>

    <exception target="ForbiddenException"/>

    <httperror target="ForbiddenException">
        <httpresponsecode value="403"/>
    </httperror>

    <!-- ==================================== -->

    <structure name="UnauthorizedException" isa="ClientFaultException"/>

    <exception target="UnauthorizedException"/>

    <httperror target="UnauthorizedException">
        <httpresponsecode value="401"/>
    </httperror>

</definition>
