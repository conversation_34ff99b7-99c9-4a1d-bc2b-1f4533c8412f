<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="GetIdentity">
        Get details of who you are
    </documentation>

    <operation name="GetIdentity">
        <input target="GetIdentityRequest"/>
        <output target="GetIdentityResponse"/>
    </operation>

    <http target="GetIdentity">
        <verb value="GET"/>
        <uri value="identity"/>
    </http>

    <structure name="GetIdentityRequest"/>

    <structure name="GetIdentityResponse">
        <member name="username" target="stringValue"/>
        <member name="icon" target="stringValue"/>
    </structure>

    <required target="GetIdentityResponse$username" />
    <required target="GetIdentityResponse$icon" />
</definition>
