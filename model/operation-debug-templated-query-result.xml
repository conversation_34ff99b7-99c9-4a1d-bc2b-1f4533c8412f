<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="DebugTemplatedQueryResult">
        Fetch results of a templated query
    </documentation>

    <operation name="DebugTemplatedQueryResult">
        <input target="DebugTemplatedQueryResultRequest"/>
        <output target="DebugTemplatedQueryResultResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
        <error target="NotFoundException"/>
    </operation>

    <http target="DebugTemplatedQueryResult">
        <verb value="POST"/>
        <uri value="debug_templatedqueryresult"/>
    </http>

    <structure name="DebugTemplatedQueryResultRequest">
        <member name="queryExecutionId" target="stringValue"/>
        <member name="continuationToken" target="stringValue"/>
    </structure>

    <required target="DebugTemplatedQueryResultRequest$queryExecutionId"/>

    <structure name="DebugTemplatedQueryResultResponse">
        <member name="result" target="stringValue"/>
        <member name="continuationToken" target="stringValue"/>
        <member name="completionDateTime" target="stringValue"/>
    </structure>

    <required target="DebugTemplatedQueryResultResponse$result"/>
    <required target="DebugTemplatedQueryResultResponse$completionDateTime"/>

</definition>
