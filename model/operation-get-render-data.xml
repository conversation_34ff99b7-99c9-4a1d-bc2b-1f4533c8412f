<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="GetRenderData">
        Get full data for a single render
    </documentation>

    <operation name="GetRenderData">
        <input target="GetRenderDataRequest"/>
        <output target="GetRenderDataResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
        <error target="NotFoundException"/>
    </operation>

    <http target="GetRenderData">
        <verb value="GET"/>
        <uri value="creative/{creativeIdSpace}/{creativeId}/render/{renderId}?creativeVariantId={creativeVariantId}"/>
    </http>

    <structure name="GetRenderDataRequest">
        <member name="creativeIdSpace" target="CreativeIDSpace"/>
        <member name="creativeId" target="stringValue"/>
        <member name="creativeVariantId" target="stringValue"/>
        <member name="renderId" target="stringValue"/>
    </structure>

    <required target="GetRenderDataRequest$creativeIdSpace"/>
    <required target="GetRenderDataRequest$creativeId"/>
    <required target="GetRenderDataRequest$renderId"/>

    <httplabel target="GetRenderDataRequest$creativeIdSpace">
        <label value="creativeIdSpace"/>
    </httplabel>
    <httplabel target="GetRenderDataRequest$creativeId">
        <label value="creativeId"/>
    </httplabel>
    <httplabel target="GetRenderDataRequest$creativeVariantId">
        <label value="creativeVariantId"/>
    </httplabel>
    <httplabel target="GetRenderDataRequest$renderId">
        <label value="renderId"/>
    </httplabel>

    <structure name="GetRenderDataResponse">
        <member name="creativeDetails" target="CreativeDetails"/>
        <member name="renderDetails" target="RenderDetails"/>
        <member name="screenshots" target="ScreenshotList"/>
        <member name="screencastFrames" target="ScreencastFrameList"/>
        <member name="screenCaptureVideos" target="ScreenCaptureVideoList"/>
        <member name="consoleLogLinesLocation" target="stringValue"/>
        <member name="networkTraceLocation" target="stringValue"/>
        <member name="annotations" target="Annotations"/>
        <member name="detectedLandingPageUrl" target="stringValue"/>
        <member name="successfulRenderArtifacts" target="RenderArtifacts" />
        <member name="failedRenderArtifacts" target="FailedRenderArtifactsList" />
    </structure>
    <required target="GetRenderDataResponse$creativeDetails"/>
    <required target="GetRenderDataResponse$renderDetails"/>

    <list name="FailedRenderArtifactsList">
        <member target="FailedRenderArtifacts"/>
    </list>

    <structure name="RenderArtifacts">
        <member name="displayRenderArtifacts" target="DisplayRenderArtifacts"/>
        <member name="pageRenderArtifacts" target="PageRenderArtifacts"/>
        <member name="vastRenderedRenderArtifacts" target="VastRenderedRenderArtifacts"/>
        <member name="vastParsedRenderArtifacts" target="VastParsedRenderArtifacts"/>
    </structure>

    <structure name="StandardRenderArtifacts" abstract="true">
        <member name="screenshots" target="ScreenshotList" />
        <member name="screencastFrames" target="ScreencastFrameList"/>
        <member name="screenCaptureVideos" target="ScreenCaptureVideoList"/>
        <member name="consoleLogLinesLocation" target="stringValue"/>
        <member name="networkTraceLocation" target="stringValue"/>
    </structure>

    <structure name="DisplayRenderArtifacts" isa="StandardRenderArtifacts"  abstract="false"/>
    <structure name="PageRenderArtifacts" isa="StandardRenderArtifacts" abstract="false"/>
    <structure name="VastRenderedRenderArtifacts" isa="StandardRenderArtifacts" abstract="false">
        <member name="mediaFiles" target="MediaFileList"/>
    </structure>
    <structure name="VastParsedRenderArtifacts">
        <member name="pageRender" target="PageRenderArtifacts" />
        <member name="networkTraceLocation" target="stringValue"/>
        <member name="mediaFiles" target="MediaFileList"/>
    </structure>

    <structure name="MediaFile">
        <member name="url" target="stringValue"/>
    </structure>

    <list name="MediaFileList">
        <member target="MediaFile"/>
    </list>

    <structure name="FailedRenderArtifacts">
        <member name="renderArtifacts" target="RenderArtifacts"/>
        <member name="failureDetails" target="FailureDetails"/>
    </structure>

    <structure name="FailureDetails">
        <member name="failureMessage" target="stringValue"/>
        <member name="failureType" target="FailureType"/>
    </structure>
    <required target="FailureDetails$failureMessage"/>
    <required target="FailureDetails$failureType"/>

    <string name="FailureType"/>
    <enumjava target="FailureType" >
        <class value="com.amazon.eeyore.humanevaluation.model.FailureType" />
    </enumjava>
    <enum target="FailureType">
        <enumValue name="BLANK" value="BLANK"/>
        <enumValue name="AREA_TOO_SMALL" value="AREA_TOO_SMALL"/>
        <enumValue name="MISSING_SCREENSHOTS" value="MISSING_SCREENSHOTS"/>
        <enumValue name="TOO_FEW_REQUESTS" value="TOO_FEW_REQUESTS"/>
        <enumValue name="CLOUDFLARE_BLOCKED" value="CLOUDFLARE_BLOCKED"/>
        <enumValue name="RENDER_DATA_LIMIT_BREACHED" value="RENDER_DATA_LIMIT_BREACHED"/>
        <enumValue name="UNAVAILABLE_PROXY" value="UNAVAILABLE_PROXY"/>
        <enumValue name="TRUNCATED_RENDER" value="TRUNCATED_RENDER"/>
        <enumValue name="INTERNAL_SERVICE_ERROR" value="INTERNAL_SERVICE_ERROR"/>
        <enumValue name="UNKNOWN_LANDING_PAGE_PROTOCOL" value="UNKNOWN_LANDING_PAGE_PROTOCOL"/>
        <enumValue name="UNKNOWN_EXCEPTION" value="UNKNOWN_EXCEPTION"/>
        <enumValue name="MISSING_SCREENCAST_FRAMES" value="MISSING_SCREENCAST_FRAMES"/>
    </enum>

    <structure name="CreativeDetails">
        <member name="sourceSystem" target="stringValue"/>
        <member name="sourceProgram" target="stringValue"/>
        <member name="creativeIds" target="creativeIdList"/>
    </structure>
    <required target="CreativeDetails$sourceSystem"/>
    <required target="CreativeDetails$sourceProgram"/>
    <required target="CreativeDetails$creativeIds"/>

    <structure name="RenderDetails">
        <member name="renderID" target="stringValue"/>
        <member name="startTime" target="stringValue"/>
        <member name="completionTime" target="stringValue"/>
        <member name="renderType" target="stringValue"/>
        <member name="fidelity" target="stringValue"/>
        <member name="result" target="stringValue"/>
        <member name="creativeConfiguration" target="RenderCreativeConfiguration"/>
        <member name="targetingConfiguration" target="RenderTargetingConfiguration"/>
        <member name="clickConfiguration" target="RenderClickConfiguration"/>
        <member name="screenCaptureConfiguration" target="RenderScreenCaptureConfiguration"/>
    </structure>
    <required target="RenderDetails$renderID"/>
    <required target="RenderDetails$startTime"/>
    <required target="RenderDetails$completionTime"/>
    <required target="RenderDetails$renderType"/>
    <required target="RenderDetails$fidelity"/>
    <required target="RenderDetails$result"/>
    <required target="RenderDetails$creativeConfiguration"/>
    <required target="RenderDetails$targetingConfiguration"/>

    <structure name="RenderCreativeConfiguration">
        <member name="creativeType" target="stringValue"/>
        <member name="markupUrl" target="stringValue"/>
        <member name="protocol" target="stringValue"/>
        <member name="publisherPageUrl" target="stringValue"/>
        <member name="containerType" target="stringValue"/>
        <member name="viewportSize" target="ViewportSize"/>
        <member name="tcString" target="stringValue"/>
    </structure>
    <required target="RenderCreativeConfiguration$creativeType"/>
    <required target="RenderCreativeConfiguration$markupUrl"/>
    <required target="RenderCreativeConfiguration$protocol"/>

    <structure name="ViewportSize">
        <member name="width" target="intValue"/>
        <member name="height" target="intValue"/>
    </structure>
    <required target="ViewportSize$width"/>
    <required target="ViewportSize$height"/>

    <structure name="RenderTargetingConfiguration">
        <member name="geoLocation" target="RenderGeoTargeting" />
        <member name="browser" target="stringValue" />
        <member name="operatingSystem" target="stringValue" />
        <member name="deviceLayout" target="stringValue" />
        <member name="deviceName" target="stringValue" />
        <member name="userAgent" target="stringValue" />
    </structure>
    <required target="RenderTargetingConfiguration$geoLocation" />
    <required target="RenderTargetingConfiguration$browser" />
    <required target="RenderTargetingConfiguration$operatingSystem" />

    <structure name="RenderGeoTargeting">
        <member name="countryCode" target="stringValue" />
        <member name="zipCode" target="stringValue" />
    </structure>
    <required target="RenderGeoTargeting$countryCode" />

    <structure name="RenderClickConfiguration">
        <member name="performClick" target="booleanValue"/>
        <member name="clickDelay" target="intValue"/>
        <member name="clickLocation" target="RenderClickLocation"/>
    </structure>
    <structure name="RenderClickLocation">
        <member name="x" target="intValue"/>
        <member name="y" target="intValue"/>
    </structure>
    <required target="RenderClickLocation$x"/>
    <required target="RenderClickLocation$y"/>

    <structure name="RenderScreenCaptureConfiguration">
        <member name="enableScreenCapture" target="booleanValue"/>
    </structure>

    <list name="ScreenshotList">
        <member target="Screenshot"/>
    </list>
    <structure name="Screenshot">
        <member name="id" target="stringValue"/>
        <member name="url" target="stringValue"/>
        <member name="timestamp" target="relativeMilliseconds"/>
        <member name="renderPartition" target="RenderPartition"/>
    </structure>
    <required target="Screenshot$id"/>
    <required target="Screenshot$url"/>
    <required target="Screenshot$timestamp"/>
    <required target="Screenshot$renderPartition"/>

    <list name="ScreencastFrameList">
        <member target="ScreencastFrame"/>
    </list>
    <structure name="ScreencastFrame">
        <member name="id" target="stringValue"/>
        <member name="url" target="stringValue"/>
        <member name="renderPartition" target="RenderPartition"/>
    </structure>
    <required target="ScreencastFrame$id"/>
    <required target="ScreencastFrame$url"/>
    <required target="ScreencastFrame$renderPartition"/>

    <list name="ScreenCaptureVideoList">
        <member target="ScreenCaptureVideo"/>
    </list>
    <structure name="ScreenCaptureVideo">
        <member name="id" target="stringValue"/>
        <member name="url" target="stringValue"/>
        <member name="renderPartition" target="RenderPartition"/>
    </structure>
    <required target="ScreenCaptureVideo$id"/>
    <required target="ScreenCaptureVideo$url"/>
    <required target="ScreenCaptureVideo$renderPartition"/>

    <string name="RenderPartition"/>
    <enum target="RenderPartition">
        <enumValue value="creative" name="CREATIVE" />
        <enumValue value="landingPage" name="LANDING_PAGE" />
    </enum>
    <enumjava target="RenderPartition">
        <class value="com.amazon.eeyore.humanevaluation.model.RenderPartition" />
    </enumjava>

    <structure name="Annotations">
        <member name="malwareDetections" target="MalwareDetectionAnnotations"/>
    </structure>
    <list name="MalwareDetectionAnnotations">
        <member target="MalwareDetectionAnnotation" />
    </list>
    <structure name="MalwareDetectionAnnotation">
        <member name="incidentID" target="stringValue" />
        <member name="description" target="stringValue" />
        <member name="detectionSourceSystem" target="stringValue" />
        <member name="enforcementType" target="stringValue" />
    </structure>
    <required target="MalwareDetectionAnnotation$incidentID"/>
    <required target="MalwareDetectionAnnotation$description"/>
    <required target="MalwareDetectionAnnotation$detectionSourceSystem"/>
    <required target="MalwareDetectionAnnotation$enforcementType"/>
</definition>
