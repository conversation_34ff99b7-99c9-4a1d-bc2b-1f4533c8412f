<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="GetRenderData">
        Get full data for a single render
    </documentation>

    <operation>
        <input target="GetRenderDataRequest"/>
        <output target="GetRenderDataResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
        <error target="NotFoundException"/>
    </operation>

    <http target="GetRenderData">
        <verb value="GET"/>
        <uri value="creative/{creativeIdSpace}/{creativeId}/render/{renderId}?creativeVariantId={creativeVariantId}"/>
    </http>

    <structure>
        <member target="CreativeIDSpace"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
    </structure>

    <required target="GetRenderDataRequest$creativeIdSpace"/>
    <required target="GetRenderDataRequest$creativeId"/>
    <required target="GetRenderDataRequest$renderId"/>

    <httplabel target="GetRenderDataRequest$creativeIdSpace">
        <label value="creativeIdSpace"/>
    </httplabel>
    <httplabel target="GetRenderDataRequest$creativeId">
        <label value="creativeId"/>
    </httplabel>
    <httplabel target="GetRenderDataRequest$creativeVariantId">
        <label value="creativeVariantId"/>
    </httplabel>
    <httplabel target="GetRenderDataRequest$renderId">
        <label value="renderId"/>
    </httplabel>

    <structure>
        <member target="CreativeDetails"/>
        <member target="RenderDetails"/>
        <member target="ScreenshotList"/>
        <member target="ScreencastFrameList"/>
        <member target="ScreenCaptureVideoList"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="Annotations"/>
        <member target="stringValue"/>
        <member target="RenderArtifacts" />
        <member target="FailedRenderArtifactsList" />
    </structure>
    <required target="GetRenderDataResponse$creativeDetails"/>
    <required target="GetRenderDataResponse$renderDetails"/>

    <list>
        <member target="FailedRenderArtifacts"/>
    </list>

    <structure>
        <member target="DisplayRenderArtifacts"/>
        <member target="PageRenderArtifacts"/>
        <member target="VastRenderedRenderArtifacts"/>
        <member target="VastParsedRenderArtifacts"/>
    </structure>

    <structure abstract="true">
        <member target="ScreenshotList" />
        <member target="ScreencastFrameList"/>
        <member target="ScreenCaptureVideoList"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
    </structure>

    <structure isa="StandardRenderArtifacts"  abstract="false"/>
    <structure isa="StandardRenderArtifacts" abstract="false"/>
    <structure isa="StandardRenderArtifacts" abstract="false">
        <member target="MediaFileList"/>
    </structure>
    <structure>
        <member target="PageRenderArtifacts" />
        <member target="stringValue"/>
        <member target="MediaFileList"/>
    </structure>

    <structure>
        <member target="stringValue"/>
    </structure>

    <list>
        <member target="MediaFile"/>
    </list>

    <structure>
        <member target="RenderArtifacts"/>
        <member target="FailureDetails"/>
    </structure>

    <structure>
        <member target="stringValue"/>
        <member target="FailureType"/>
    </structure>
    <required target="FailureDetails$failureMessage"/>
    <required target="FailureDetails$failureType"/>

    <string/>
    <enumjava target="FailureType" >
        <class value="com.amazon.eeyore.humanevaluation.model.FailureType" />
    </enumjava>
    <enum target="FailureType">
        <enumValue value="BLANK"/>
        <enumValue value="AREA_TOO_SMALL"/>
        <enumValue value="MISSING_SCREENSHOTS"/>
        <enumValue value="TOO_FEW_REQUESTS"/>
        <enumValue value="CLOUDFLARE_BLOCKED"/>
        <enumValue value="RENDER_DATA_LIMIT_BREACHED"/>
        <enumValue value="UNAVAILABLE_PROXY"/>
        <enumValue value="TRUNCATED_RENDER"/>
        <enumValue value="INTERNAL_SERVICE_ERROR"/>
        <enumValue value="UNKNOWN_LANDING_PAGE_PROTOCOL"/>
        <enumValue value="UNKNOWN_EXCEPTION"/>
        <enumValue value="MISSING_SCREENCAST_FRAMES"/>
    </enum>

    <structure>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="creativeIdList"/>
    </structure>
    <required target="CreativeDetails$sourceSystem"/>
    <required target="CreativeDetails$sourceProgram"/>
    <required target="CreativeDetails$creativeIds"/>

    <structure>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="RenderCreativeConfiguration"/>
        <member target="RenderTargetingConfiguration"/>
        <member target="RenderClickConfiguration"/>
        <member target="RenderScreenCaptureConfiguration"/>
    </structure>
    <required target="RenderDetails$renderID"/>
    <required target="RenderDetails$startTime"/>
    <required target="RenderDetails$completionTime"/>
    <required target="RenderDetails$renderType"/>
    <required target="RenderDetails$fidelity"/>
    <required target="RenderDetails$result"/>
    <required target="RenderDetails$creativeConfiguration"/>
    <required target="RenderDetails$targetingConfiguration"/>

    <structure>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="ViewportSize"/>
        <member target="stringValue"/>
    </structure>
    <required target="RenderCreativeConfiguration$creativeType"/>
    <required target="RenderCreativeConfiguration$markupUrl"/>
    <required target="RenderCreativeConfiguration$protocol"/>

    <structure>
        <member target="intValue"/>
        <member target="intValue"/>
    </structure>
    <required target="ViewportSize$width"/>
    <required target="ViewportSize$height"/>

    <structure>
        <member target="RenderGeoTargeting" />
        <member target="stringValue" />
        <member target="stringValue" />
        <member target="stringValue" />
        <member target="stringValue" />
        <member target="stringValue" />
    </structure>
    <required target="RenderTargetingConfiguration$geoLocation" />
    <required target="RenderTargetingConfiguration$browser" />
    <required target="RenderTargetingConfiguration$operatingSystem" />

    <structure>
        <member target="stringValue" />
        <member target="stringValue" />
    </structure>
    <required target="RenderGeoTargeting$countryCode" />

    <structure>
        <member target="booleanValue"/>
        <member target="intValue"/>
        <member target="RenderClickLocation"/>
    </structure>
    <structure>
        <member target="intValue"/>
        <member target="intValue"/>
    </structure>
    <required target="RenderClickLocation$x"/>
    <required target="RenderClickLocation$y"/>

    <structure>
        <member target="booleanValue"/>
    </structure>

    <list>
        <member target="Screenshot"/>
    </list>
    <structure>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="relativeMilliseconds"/>
        <member target="RenderPartition"/>
    </structure>
    <required target="Screenshot$id"/>
    <required target="Screenshot$url"/>
    <required target="Screenshot$timestamp"/>
    <required target="Screenshot$renderPartition"/>

    <list>
        <member target="ScreencastFrame"/>
    </list>
    <structure>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="RenderPartition"/>
    </structure>
    <required target="ScreencastFrame$id"/>
    <required target="ScreencastFrame$url"/>
    <required target="ScreencastFrame$renderPartition"/>

    <list>
        <member target="ScreenCaptureVideo"/>
    </list>
    <structure>
        <member target="stringValue"/>
        <member target="stringValue"/>
        <member target="RenderPartition"/>
    </structure>
    <required target="ScreenCaptureVideo$id"/>
    <required target="ScreenCaptureVideo$url"/>
    <required target="ScreenCaptureVideo$renderPartition"/>

    <string/>
    <enum target="RenderPartition">
        <enumValue value="creative" />
        <enumValue value="landingPage" />
    </enum>
    <enumjava target="RenderPartition">
        <class value="com.amazon.eeyore.humanevaluation.model.RenderPartition" />
    </enumjava>

    <structure>
        <member target="MalwareDetectionAnnotations"/>
    </structure>
    <list>
        <member target="MalwareDetectionAnnotation" />
    </list>
    <structure>
        <member target="stringValue" />
        <member target="stringValue" />
        <member target="stringValue" />
        <member target="stringValue" />
    </structure>
    <required target="MalwareDetectionAnnotation$incidentID"/>
    <required target="MalwareDetectionAnnotation$description"/>
    <required target="MalwareDetectionAnnotation$detectionSourceSystem"/>
    <required target="MalwareDetectionAnnotation$enforcementType"/>
</definition>
