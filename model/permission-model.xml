<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <!-- Permission ID Enum -->
    <string name="PermissionID" />
    <enum target="PermissionID">
        <enumValue value="SIGNATURE_READ" name="SIGNATURE_READ" />
        <enumValue value="SIGNATURE_UPDATE" name="SIGNATURE_UPDATE" />
        <enumValue value="SIGNATURE_OVERRIDE" name="SIGNATURE_OVERRIDE" />
        <enumValue value="DETECTION_READ" name="DETECTION_READ" />
    </enum>
    <enumjava target="PermissionID">
        <class value="com.amazon.eeyore.humanevaluation.model.PermissionID" />
    </enumjava>

    <!-- Permission Object Structure -->
    <structure name="PermissionObject">
        <member name="permissionID" target="PermissionID"/>
    </structure>
    <required target="PermissionObject$permissionID" />

    <!-- Permissions List -->
    <list name="PermissionsList">
        <member target="PermissionObject"/>
    </list>

</definition>