<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <!-- Permission ID Enum -->
    <string />
    <enum target="PermissionID">
        <enumValue value="SIGNATURE_READ" />
        <enumValue value="SIGNATURE_UPDATE" />
        <enumValue value="SIGNATURE_OVERRIDE" />
        <enumValue value="DETECTION_READ" />
    </enum>
    <enumjava target="PermissionID">
        <class value="com.amazon.eeyore.humanevaluation.model.PermissionID" />
    </enumjava>

    <!-- Permission Object Structure -->
    <structure>
        <member target="PermissionID"/>
    </structure>
    <required target="PermissionObject$permissionID" />

    <!-- Permissions List -->
    <list>
        <member target="PermissionObject"/>
    </list>

</definition>