<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="CreateSignatures">
        Create signatures in the system.
    </documentation>

    <operation name="CreateSignatures">
        <input target="CreateSignaturesRequest"/>
        <output target="CreateSignaturesResponse"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
    </operation>

    <http target="CreateSignatures">
        <verb value="POST"/>
        <uri value="signatures"/>
    </http>

    <structure name="SignatureEntry">
        <member name="domain" target="stringValue"/>
        <member name="source" target="IntelligenceSource"/>
        <member name="description" target="stringValue"/>
        <member name="status" target="SignatureStatus"/>
        <member name="decision" target="SignatureDecision"/>
        <member name="lineage" target="IntelligenceLineage"/>
        <member name="threatClassification" target="ThreatClassification"/>
    </structure>

    <required target="SignatureEntry$domain" />
    <required target="SignatureEntry$source" />
    <required target="SignatureEntry$status" />
    <required target="SignatureEntry$decision" />
    <required target="SignatureEntry$description" />

    <string name="SignatureStatus" />
    <enum target="SignatureStatus">
        <enumValue value="SHADOW" name="SHADOW" />
        <enumValue value="LIVE" name="LIVE" />
        <enumValue value="DELETED" name="DELETED" />
        <enumValue value="PROPOSED" name="PROPOSED" />
        <enumValue value="PENDING_AUTO_VERIFICATION" name="PENDING_AUTO_VERIFICATION" />
        <enumValue value="REJECTED" name="REJECTED" />
        <enumValue value="PENDING_MANUAL_REVIEW" name="PENDING_MANUAL_REVIEW" />
        <enumValue value="LIVE_REFERRAL" name="LIVE_REFERRAL" />
    </enum>
    <enumjava target="SignatureStatus" >
        <class value="com.amazon.eeyore.humanevaluation.model.SignatureStatus" />
    </enumjava>

    <string name="SignatureDecision" />
    <enum target="SignatureDecision">
        <enumValue value="THREAT" name="THREAT" />
        <enumValue value="SAFETY" name="SAFETY" />
    </enum>
    <enumjava target="SignatureDecision" >
        <class value="com.amazon.eeyore.humanevaluation.model.SignatureDecision" />
    </enumjava>

    <string name="IntelligenceSource" />
    <enum target="IntelligenceSource">
        <enumValue value="SYSTEM_TESTING" name="SYSTEM_TESTING" />
        <enumValue value="ADRISK_REVERSE_IP" name="ADRISK_REVERSE_IP" />
        <enumValue value="ADRISK_WEB_THREAT_HUNTING" name="ADRISK_WEB_THREAT_HUNTING" />
        <enumValue value="ADRISK_SLICENDICE" name="ADRISK_SLICENDICE" />
        <enumValue value="ADRISK_ANOMSIM" name="ADRISK_ANOMSIM" />
        <enumValue value="ADRISK_GALLERY_HUMAN_REVIEW" name="ADRISK_GALLERY_HUMAN_REVIEW" />
        <enumValue value="ADRISK_TDI" name="ADRISK_TDI" />
        <enumValue value="ADRISK_MINHASH" name="ADRISK_MINHASH" />
        <enumValue value="ADRISK_DOM" name="ADRISK_DOM" />
        <enumValue value="ASA" name="ASA" />
        <enumValue value="TAG" name="TAG" />
        <enumValue value="GOOGLE" name="GOOGLE" />
        <enumValue value="CONFIANT" name="CONFIANT" />
        <enumValue value="GEOEDGE" name="GEOEDGE" />
        <enumValue value="TMT" name="TMT" />
        <enumValue value="BOLTIVE" name="BOLTIVE" />
        <enumValue value="HUMAN" name="HUMAN" />
    </enum>
    <enumjava target="IntelligenceSource" >
        <class value="com.amazon.eeyore.humanevaluation.model.IntelligenceSource" />
    </enumjava>

    <string name="IntelligenceLineage" />
    <enum target="IntelligenceLineage">
        <enumValue value="FIRST_PARTY" name="FIRST_PARTY" />
        <enumValue value="THIRD_PARTY" name="THIRD_PARTY" />
        <enumValue value="HUMAN_INTELLIGENCE" name="HUMAN_INTELLIGENCE" />
        <enumValue value="AUTOMATED_DETECTION" name="AUTOMATED_DETECTION" />
        <enumValue value="THREAT_HUNTING" name="THREAT_HUNTING" />
        <enumValue value="EXTERNAL_FEED" name="EXTERNAL_FEED" />
    </enum>
    <enumjava target="IntelligenceLineage" >
        <class value="com.amazon.eeyore.humanevaluation.model.IntelligenceLineage" />
    </enumjava>

    <string name="ThreatClassification" />
    <enum target="ThreatClassification">
        <enumValue value="MALICIOUS_EXECUTABLE" name="MALICIOUS_EXECUTABLE" />
        <enumValue value="FAKE_SECURITY_SOFTWARE" name="FAKE_SECURITY_SOFTWARE" />
        <enumValue value="PUP" name="PUP" />
        <enumValue value="AUTO_DOWNLOAD" name="AUTO_DOWNLOAD" />
        <enumValue value="PHISHING_DOMAIN" name="PHISHING_DOMAIN" />
        <enumValue value="PHISHING_DELIVERY" name="PHISHING_DELIVERY" />
        <enumValue value="FIZZCORE" name="FIZZCORE" />
        <enumValue value="SPOOFING" name="SPOOFING" />
        <enumValue value="HEALTHCARE_SCAM" name="HEALTHCARE_SCAM" />
        <enumValue value="INVESTMENT_SCAM" name="INVESTMENT_SCAM" />
        <enumValue value="E_COMMERCE_SCAM" name="E_COMMERCE_SCAM" />
        <enumValue value="TECH_GADGET_SCAM" name="TECH_GADGET_SCAM" />
        <enumValue value="TECH_SUPPORT_SCAM" name="TECH_SUPPORT_SCAM" />
        <enumValue value="AD_FRAUD" name="AD_FRAUD" />
        <enumValue value="COMPROMISED_DOMAIN" name="COMPROMISED_DOMAIN" />
        <enumValue value="SOCGHOLISH" name="SOCGHOLISH" />
        <enumValue value="GHOSTCAT" name="GHOSTCAT" />
        <enumValue value="SUSPICIOUS_DOMAIN" name="SUSPICIOUS_DOMAIN" />
        <enumValue value="HEURISTIC_MATCH" name="HEURISTIC_MATCH" />
        <enumValue value="THREAT_ACTOR_INFRA" name="THREAT_ACTOR_INFRA" />
        <enumValue value="COMMAND_BAIT" name="COMMAND_BAIT" />
        <enumValue value="MALICIOUS_REDIRECT" name="MALICIOUS_REDIRECT" />
        <enumValue value="APP_STORE_REDIRECT" name="APP_STORE_REDIRECT" />
        <enumValue value="APP_STORE_POPUP" name="APP_STORE_POPUP" />
    </enum>
    <enumjava target="ThreatClassification" >
        <class value="com.amazon.eeyore.humanevaluation.model.ThreatClassification" />
    </enumjava>

    <list name="SignatureEntryList">
        <member target="SignatureEntry"/>
    </list>

    <structure name="CreateSignaturesRequest">
        <member name="signatures" target="SignatureEntryList"/>
    </structure>

    <required target="CreateSignaturesRequest$signatures" />

    <string name="ValidationStatus" />
    <enum target="ValidationStatus">
        <enumValue value="APPROVED" name="APPROVED" />
        <enumValue value="REJECTED" name="REJECTED" />
        <enumValue value="N/A" name="N_A" />
    </enum>
    <enumjava target="ValidationStatus" >
        <class value="com.amazon.eeyore.humanevaluation.model.ValidationStatus" />
    </enumjava>

    <structure name="ValidationResult">
        <member name="domain" target="stringValue"/>
        <member name="status" target="ValidationStatus"/>
        <member name="reason" target="stringValue"/>
        <member name="impactAssessment" target="ImpactAssessment"/>
    </structure>

    <required target="ValidationResult$domain" />
    <required target="ValidationResult$status" />

    <structure name="ImpactAssessment">
        <member name="timesSeen" target="intValue"/>
        <member name="firstPartyAdvertisersCount" target="intValue"/>
        <member name="impactedCreatives" target="ImpactedCreatives"/>
    </structure>

    <structure name="ImpactedCreatives">
        <member name="safeCreativesCount" target="intValue"/>
        <member name="unsafeCreativesCount" target="intValue"/>
        <member name="clicks" target="intValue"/>
        <member name="impressions" target="intValue"/>
    </structure>

    <list name="ValidationResultList">
        <member target="ValidationResult"/>
    </list>
    <structure name="CreatedSignatureEntry">
        <member name="domain" target="stringValue"/>
        <member name="signatureId" target="stringValue"/>
        <member name="createdAt" target="stringValue"/>
        <member name="success" target="booleanValue"/>
        <member name="errorMessage" target="stringValue"/>
        <member name="validationResult" target="ValidationResult"/>
    </structure>

    <required target="CreatedSignatureEntry$domain" />
    <required target="CreatedSignatureEntry$success" />

    <list name="CreatedSignatureEntryList">
        <member target="CreatedSignatureEntry"/>
    </list>

    <structure name="CreateSignaturesResponse">
        <member name="createdSignatures" target="CreatedSignatureEntryList"/>
    </structure>

    <required target="CreateSignaturesResponse$createdSignatures" />

</definition>