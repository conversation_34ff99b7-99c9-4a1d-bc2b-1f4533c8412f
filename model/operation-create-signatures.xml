<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="CreateSignatures">
        Create signatures in the system.
    </documentation>

    <operation name="CreateSignatures">
        <input target="CreateSignaturesRequest"/>
        <output target="CreateSignaturesResponse"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
    </operation>

    <http target="CreateSignatures">
        <verb value="POST"/>
        <uri value="signatures"/>
    </http>

    <structure name="SignatureEntry">
        <member name="domain" target="stringValue"/>
        <member name="source" target="IntelligenceSource"/>
        <member name="description" target="stringValue"/>
        <member name="status" target="SignatureStatus"/>
        <member name="decision" target="SignatureDecision"/>
        <member name="lineage" target="IntelligenceLineage"/>
        <member name="threatClassification" target="ThreatClassification"/>
    </structure>

    <required target="SignatureEntry$domain" />
    <required target="SignatureEntry$source" />
    <required target="SignatureEntry$status" />
    <required target="SignatureEntry$decision" />
    <required target="SignatureEntry$description" />

    <string name="SignatureStatus" />
    <enum target="SignatureStatus">
        <enumValue value="SHADOW" />
        <enumValue value="LIVE" />
        <enumValue value="DELETED" />
        <enumValue value="PROPOSED" />
        <enumValue value="PENDING_AUTO_VERIFICATION" />
        <enumValue value="REJECTED" />
        <enumValue value="PENDING_MANUAL_REVIEW" />
        <enumValue value="LIVE_REFERRAL" />
    </enum>
    <enumjava target="SignatureStatus" >
        <class value="com.amazon.eeyore.humanevaluation.model.SignatureStatus" />
    </enumjava>

    <string name="SignatureDecision" />
    <enum target="SignatureDecision">
        <enumValue value="THREAT" />
        <enumValue value="SAFETY" />
    </enum>
    <enumjava target="SignatureDecision" >
        <class value="com.amazon.eeyore.humanevaluation.model.SignatureDecision" />
    </enumjava>

    <string name="IntelligenceSource" />
    <enum target="IntelligenceSource">
        <enumValue value="SYSTEM_TESTING" />
        <enumValue value="ADRISK_REVERSE_IP" />
        <enumValue value="ADRISK_WEB_THREAT_HUNTING" />
        <enumValue value="ADRISK_SLICENDICE" />
        <enumValue value="ADRISK_ANOMSIM" />
        <enumValue value="ADRISK_GALLERY_HUMAN_REVIEW" />
        <enumValue value="ADRISK_TDI" />
        <enumValue value="ADRISK_MINHASH" />
        <enumValue value="ADRISK_DOM" />
        <enumValue value="ASA" />
        <enumValue value="TAG" />
        <enumValue value="GOOGLE" />
        <enumValue value="CONFIANT" />
        <enumValue value="GEOEDGE" />
        <enumValue value="TMT" />
        <enumValue value="BOLTIVE" />
        <enumValue value="HUMAN" />
    </enum>
    <enumjava target="IntelligenceSource" >
        <class value="com.amazon.eeyore.humanevaluation.model.IntelligenceSource" />
    </enumjava>

    <string name="IntelligenceLineage" />
    <enum target="IntelligenceLineage">
        <enumValue value="FIRST_PARTY" />
        <enumValue value="THIRD_PARTY" />
        <enumValue value="HUMAN_INTELLIGENCE" />
        <enumValue value="AUTOMATED_DETECTION" />
        <enumValue value="THREAT_HUNTING" />
        <enumValue value="EXTERNAL_FEED" />
    </enum>
    <enumjava target="IntelligenceLineage" >
        <class value="com.amazon.eeyore.humanevaluation.model.IntelligenceLineage" />
    </enumjava>

    <string name="ThreatClassification" />
    <enum target="ThreatClassification">
        <enumValue value="MALICIOUS_EXECUTABLE" />
        <enumValue value="FAKE_SECURITY_SOFTWARE" />
        <enumValue value="PUP" />
        <enumValue value="AUTO_DOWNLOAD" />
        <enumValue value="PHISHING_DOMAIN" />
        <enumValue value="PHISHING_DELIVERY" />
        <enumValue value="FIZZCORE" />
        <enumValue value="SPOOFING" />
        <enumValue value="HEALTHCARE_SCAM" />
        <enumValue value="INVESTMENT_SCAM" />
        <enumValue value="E_COMMERCE_SCAM" />
        <enumValue value="TECH_GADGET_SCAM" />
        <enumValue value="TECH_SUPPORT_SCAM" />
        <enumValue value="AD_FRAUD" />
        <enumValue value="COMPROMISED_DOMAIN" />
        <enumValue value="SOCGHOLISH" />
        <enumValue value="GHOSTCAT" />
        <enumValue value="SUSPICIOUS_DOMAIN" />
        <enumValue value="HEURISTIC_MATCH" />
        <enumValue value="THREAT_ACTOR_INFRA" />
        <enumValue value="COMMAND_BAIT" />
        <enumValue value="MALICIOUS_REDIRECT" />
        <enumValue value="APP_STORE_REDIRECT" />
        <enumValue value="APP_STORE_POPUP" />
    </enum>
    <enumjava target="ThreatClassification" >
        <class value="com.amazon.eeyore.humanevaluation.model.ThreatClassification" />
    </enumjava>

    <list name="SignatureEntryList">
        <member target="SignatureEntry"/>
    </list>

    <structure name="CreateSignaturesRequest">
        <member name="signatures" target="SignatureEntryList"/>
    </structure>

    <required target="CreateSignaturesRequest$signatures" />

    <string name="ValidationStatus" />
    <enum target="ValidationStatus">
        <enumValue value="APPROVED" />
        <enumValue value="REJECTED" />
        <enumValue value="N/A" />
    </enum>
    <enumjava target="ValidationStatus" >
        <class value="com.amazon.eeyore.humanevaluation.model.ValidationStatus" />
    </enumjava>

    <structure name="ValidationResult">
        <member name="domain" target="stringValue"/>
        <member name="status" target="ValidationStatus"/>
        <member name="reason" target="stringValue"/>
        <member name="impactAssessment" target="ImpactAssessment"/>
    </structure>

    <required target="ValidationResult$domain" />
    <required target="ValidationResult$status" />

    <structure name="ImpactAssessment">
        <member name="timesSeen" target="intValue"/>
        <member name="firstPartyAdvertisersCount" target="intValue"/>
        <member name="impactedCreatives" target="ImpactedCreatives"/>
    </structure>

    <structure name="ImpactedCreatives">
        <member name="safeCreativesCount" target="intValue"/>
        <member name="unsafeCreativesCount" target="intValue"/>
        <member name="clicks" target="intValue"/>
        <member name="impressions" target="intValue"/>
    </structure>

    <list name="ValidationResultList">
        <member target="ValidationResult"/>
    </list>
    <structure name="CreatedSignatureEntry">
        <member name="domain" target="stringValue"/>
        <member name="signatureId" target="stringValue"/>
        <member name="createdAt" target="stringValue"/>
        <member name="success" target="booleanValue"/>
        <member name="errorMessage" target="stringValue"/>
        <member name="validationResult" target="ValidationResult"/>
    </structure>

    <required target="CreatedSignatureEntry$domain" />
    <required target="CreatedSignatureEntry$success" />

    <list name="CreatedSignatureEntryList">
        <member target="CreatedSignatureEntry"/>
    </list>

    <structure name="CreateSignaturesResponse">
        <member name="createdSignatures" target="CreatedSignatureEntryList"/>
    </structure>

    <required target="CreateSignaturesResponse$createdSignatures" />

</definition>