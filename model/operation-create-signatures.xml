<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="CreateSignatures">
        Create signatures in the system.
    </documentation>

    <operation name="CreateSignatures">
        <input target="CreateSignaturesRequest"/>
        <output target="CreateSignaturesResponse"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
    </operation>

    <http target="CreateSignatures">
        <verb value="POST"/>
        <uri value="signatures"/>
    </http>

    <structure name="SignatureEntry">
        <member name="domain" target="stringValue"/>
        <member name="source" target="stringValue"/>
        <member name="description" target="stringValue"/>
        <member name="status" target="SignatureStatus"/>
        <member name="decision" target="SignatureDecision"/>
    </structure>

    <required target="SignatureEntry$domain" />
    <required target="SignatureEntry$source" />
    <required target="SignatureEntry$status" />
    <required target="SignatureEntry$decision" />
    <required target="SignatureEntry$description" />

    <string name="SignatureStatus" />
    <enum target="SignatureStatus">
        <enumValue value="SHADOW" name="SHADOW" />
        <enumValue value="LIVE" name="LIVE" />
        <enumValue value="DELETED" name="DELETED" />
        <enumValue value="PROPOSED" name="PROPOSED" />
        <enumValue value="PENDING_AUTO_VERIFICATION" name="PENDING_AUTO_VERIFICATION" />
        <enumValue value="REJECTED" name="REJECTED" />
        <enumValue value="PENDING_MANUAL_REVIEW" name="PENDING_MANUAL_REVIEW" />
        <enumValue value="LIVE_REFERRAL" name="LIVE_REFERRAL" />
    </enum>
    <enumjava target="SignatureStatus" >
        <class value="com.amazon.eeyore.humanevaluation.model.SignatureStatus" />
    </enumjava>

    <string name="SignatureDecision" />
    <enum target="SignatureDecision">
        <enumValue value="THREAT" name="THREAT" />
        <enumValue value="SAFETY" name="SAFETY" />
    </enum>
    <enumjava target="SignatureDecision" >
        <class value="com.amazon.eeyore.humanevaluation.model.SignatureDecision" />
    </enumjava>

    <list name="SignatureEntryList">
        <member target="SignatureEntry"/>
    </list>

    <structure name="CreateSignaturesRequest">
        <member name="signatures" target="SignatureEntryList"/>
    </structure>

    <required target="CreateSignaturesRequest$signatures" />

    <string name="ValidationStatus" />
    <enum target="ValidationStatus">
        <enumValue value="APPROVED" name="APPROVED" />
        <enumValue value="REJECTED" name="REJECTED" />
        <enumValue value="N/A" name="N_A" />
    </enum>
    <enumjava target="ValidationStatus" >
        <class value="com.amazon.eeyore.humanevaluation.model.ValidationStatus" />
    </enumjava>

    <structure name="ValidationResult">
        <member name="domain" target="stringValue"/>
        <member name="status" target="ValidationStatus"/>
        <member name="reason" target="stringValue"/>
        <member name="impactAssessment" target="ImpactAssessment"/>
    </structure>

    <required target="ValidationResult$domain" />
    <required target="ValidationResult$status" />

    <structure name="ImpactAssessment">
        <member name="timesSeen" target="intValue"/>
        <member name="firstPartyAdvertisersCount" target="intValue"/>
        <member name="impactedCreatives" target="ImpactedCreatives"/>
    </structure>

    <structure name="ImpactedCreatives">
        <member name="safeCreativesCount" target="intValue"/>
        <member name="unsafeCreativesCount" target="intValue"/>
        <member name="clicks" target="intValue"/>
        <member name="impressions" target="intValue"/>
    </structure>

    <list name="ValidationResultList">
        <member target="ValidationResult"/>
    </list>
    <structure name="CreatedSignatureEntry">
        <member name="domain" target="stringValue"/>
        <member name="signatureId" target="stringValue"/>
        <member name="createdAt" target="stringValue"/>
        <member name="success" target="booleanValue"/>
        <member name="errorMessage" target="stringValue"/>
        <member name="validationResult" target="ValidationResult"/>
    </structure>

    <required target="CreatedSignatureEntry$domain" />
    <required target="CreatedSignatureEntry$success" />

    <list name="CreatedSignatureEntryList">
        <member target="CreatedSignatureEntry"/>
    </list>

    <structure name="CreateSignaturesResponse">
        <member name="createdSignatures" target="CreatedSignatureEntryList"/>
    </structure>

    <required target="CreateSignaturesResponse$createdSignatures" />

</definition>