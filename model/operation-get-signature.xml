<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.woozle.threatstore.model" version="1.0">

    <documentation target="GetSignature">
        Retrieves a specific signature by its signature ID.
    </documentation>

    <operation name="GetSignature">
        <input target="GetSignatureInput"/>
        <output target="GetSignatureOutput"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
        <error target="NotFoundError"/>
        <error target="InternalException"/>
    </operation>

    <http target="GetSignature">
        <verb value="GET"/>
        <uri value="signature/{signatureId}"/>
    </http>

    <structure name="GetSignatureInput">
        <member name="signatureId" target="SignatureId"/>
    </structure>

    <required target="GetSignatureInput$signatureId"/>

    <httplabel target="GetSignatureInput$signatureId">
        <label value="signatureId"/>
    </httplabel>

    <structure name="GetSignatureOutput">
        <member name="signatureId" target="SignatureId"/>
        <member name="signatureDecisionType" target="SignatureDecisionType"/>
        <member name="status" target="SignatureStatus"/>
        <member name="creator" target="AuthenticatedEntity"/>
        <member name="auditor" target="AuthenticatedUser"/>
        <member name="entrySystem" target="EntrySystem"/>
        <member name="source" target="IntelligenceSource"/>
        <member name="description" target="stringValue"/>
        <member name="creationTime" target="Timestamp"/>
        <member name="content" target="SignatureContent"/>
        <member name="lineage" target="IntelligenceLineage"/>
        <member name="threatClassification" target="ThreatClassification"/>
        <member name="requestedStatus" target="RequestedSignatureStatus"/>
        <member name="confidenceLevel" target="doubleValue"/>
    </structure>

    <required target="GetSignatureOutput$signatureId"/>
    <required target="GetSignatureOutput$signatureDecisionType"/>
    <required target="GetSignatureOutput$status"/>
    <required target="GetSignatureOutput$creator"/>
    <required target="GetSignatureOutput$auditor"/>
    <required target="GetSignatureOutput$entrySystem"/>
    <required target="GetSignatureOutput$source"/>
    <required target="GetSignatureOutput$description"/>
    <required target="GetSignatureOutput$creationTime"/>
    <required target="GetSignatureOutput$content"/>

</definition>
