<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="GetRendersByCreativeId">
        Get renders for a particular Creative ID
    </documentation>

    <operation name="GetRendersByCreativeId">
        <input target="GetRendersByCreativeIdRequest"/>
        <output target="GetRendersByCreativeIdResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
        <error target="NotFoundException"/>
    </operation>

    <http target="GetRendersByCreativeId">
        <verb value="GET"/>
        <uri value="creative/{creativeIdSpace}/{creativeId}/renders?variantId={variantId}&amp;continuationToken={continuationToken}"/>
    </http>

    <structure name="GetRendersByCreativeIdRequest">
        <member name="creativeIdSpace" target="CreativeIDSpace"/>
        <member name="creativeId" target="stringValue"/>
        <member name="variantId" target="stringValue"/>
        <member name="continuationToken" target="stringValue"/>
    </structure>

    <required target="GetRendersByCreativeIdRequest$creativeIdSpace"/>
    <required target="GetRendersByCreativeIdRequest$creativeId"/>

    <httplabel target="GetRendersByCreativeIdRequest$creativeIdSpace">
        <label value="creativeIdSpace"/>
    </httplabel>
    <httplabel target="GetRendersByCreativeIdRequest$creativeId">
        <label value="creativeId"/>
    </httplabel>
    <httplabel target="GetRendersByCreativeIdRequest$variantId">
        <label value="variantId"/>
    </httplabel>
    <httplabel target="GetRendersByCreativeIdRequest$continuationToken">
        <label value="continuationToken"/>
    </httplabel>

    <structure name="GetRendersByCreativeIdResponse">
        <member name="sourceSystem" target="stringValue"/>
        <member name="sourceProgram" target="stringValue"/>
        <member name="creativeIds" target="creativeIdList"/>
        <member name="renderSummaries" target="renderSummaryList"/>
        <member name="continuationToken" target="stringValue"/>
    </structure>

    <required target="GetRendersByCreativeIdResponse$sourceSystem"/>
    <required target="GetRendersByCreativeIdResponse$sourceProgram"/>
    <required target="GetRendersByCreativeIdResponse$creativeIds"/>
    <required target="GetRendersByCreativeIdResponse$renderSummaries"/>

    <structure name="CreativeId">
        <member name="creativeIdSpace" target="stringValue"/>
        <member name="creativeId" target="stringValue"/>
        <member name="variantId" target="stringValue"/>
    </structure>
    <list name="creativeIdList">
        <member target="CreativeId"/>
    </list>

    <required target="CreativeId$creativeIdSpace"/>
    <required target="CreativeId$creativeId"/>

    <structure name="RenderSummary">
        <member name="renderId" target="stringValue"/>
        <member name="startTimestamp" target="stringValue"/>
        <member name="renderType" target="stringValue"/>
        <member name="hasLandingPage" target="booleanValue"/>
        <member name="device" target="stringValue"/>
        <member name="operatingSystem" target="stringValue"/>
        <member name="browser" target="stringValue"/>
        <member name="result" target="stringValue"/>
        <member name="hasMalwareDetection" target="booleanValue"/>
        <member name="imagePreviewUrl" target="stringValue"/>
        <member name="hasScreenCaptureFrames" target="booleanValue"/>
        <member name="hasScreenCaptureVideo" target="booleanValue"/>
    </structure>
    <list name="renderSummaryList">
        <member target="RenderSummary"/>
    </list>

    <required target="RenderSummary$renderId"/>
    <required target="RenderSummary$startTimestamp"/>
    <required target="RenderSummary$renderType"/>
    <required target="RenderSummary$hasLandingPage"/>
    <required target="RenderSummary$operatingSystem"/>
    <required target="RenderSummary$browser"/>
    <required target="RenderSummary$result"/>
    <required target="RenderSummary$hasMalwareDetection"/>
</definition>
