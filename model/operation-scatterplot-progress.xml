<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">
    <documentation target="ScatterPlotProgress">
        Retrieve the progress of a previous async request for a scatter plot
    </documentation>

    <operation name="ScatterPlotProgress">
        <input target="ScatterPlotProgressRequest"/>
        <output target="ScatterPlotProgressResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
    </operation>

    <http target="ScatterPlotProgress">
        <verb value="GET"/>
        <uri value="scatterplot_progress?outputLocation={outputLocation}"/>
    </http>

    <structure name="ScatterPlotProgressRequest">
        <member name="outputLocation" target="stringValue"/>
    </structure>

    <required target="ScatterPlotProgressRequest$outputLocation"/>

    <httplabel target="ScatterPlotProgressRequest$outputLocation">
        <label value="outputLocation"/>
    </httplabel>

    <structure name="ScatterPlotProgressResponse">
        <member name="finished" target="booleanValue"/>
        <member name="plot" target="ScatterPlotPlotItemList"/>
        <member name="landingPagesPending" target="booleanValue"/>
        <member name="plotLandingPages" target="ScatterPlotPlotItemList"/>
        <member name="inputDataFile" target="stringValue"/>
    </structure>

    <required target="ScatterPlotProgressResponse$finished"/>
    <required target="ScatterPlotProgressResponse$landingPagesPending"/>

    <list name="ScatterPlotPlotItemList">
        <member target="ScatterPlotPlotItem"/>
    </list>

    <structure name="ScatterPlotPlotItem">
        <member name="creativeID" target="stringValue"/>
        <member name="creativeIDSpace" target="stringValue"/>
        <member name="renderID" target="stringValue"/>
        <member name="x" target="floatValue"/>
        <member name="y" target="floatValue"/>
        <member name="smallImage" target="stringValue"/>
        <member name="largeImage" target="stringValue"/>
    </structure>

    <required target="ScatterPlotPlotItem$creativeID"/>
    <required target="ScatterPlotPlotItem$creativeIDSpace"/>
    <required target="ScatterPlotPlotItem$renderID"/>
    <required target="ScatterPlotPlotItem$x"/>
    <required target="ScatterPlotPlotItem$y"/>
    <required target="ScatterPlotPlotItem$smallImage"/>
    <required target="ScatterPlotPlotItem$largeImage"/>
</definition>
