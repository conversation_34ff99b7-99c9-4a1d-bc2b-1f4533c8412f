<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="GetPermissions">
        Returns all permissions for the current user
    </documentation>

    <operation name="GetPermissions">
        <input target="GetPermissionsRequest"/>
        <output target="GetPermissionsResponse"/>
        <error target="UnauthorizedException"/>
    </operation>

    <http target="GetPermissions">
        <verb value="GET"/>
        <uri value="permissions"/>
    </http>

    <structure name="GetPermissionsRequest"/>

    <structure name="GetPermissionsResponse">
        <member name="permissions" target="PermissionsList"/>
    </structure>

    <required target="GetPermissionsResponse$permissions" />

</definition>