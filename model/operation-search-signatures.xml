<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">

    <documentation target="SearchSignatures">
        Search signatures filtered by status and other criteria using Woozle ThreatStore API.
    </documentation>

    <operation name="SearchSignatures">
        <input target="SearchSignaturesRequest"/>
        <output target="SearchSignaturesResponse"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
        <error target="InternalException"/>
    </operation>

    <http target="SearchSignatures">
        <verb value="GET"/>
        <uri value="signatures?status={status}&amp;maxResults={maxResults}&amp;nextToken={nextToken}"/>
    </http>

    <structure name="SearchSignaturesRequest">
        <member name="status" target="SignatureStatus"/>
        <member name="maxResults" target="intValue"/>
        <member name="nextToken" target="stringValue"/>
    </structure>

    <required target="SearchSignaturesRequest$status" />

    <structure name="SignatureListItem">
        <member name="signatureId" target="stringValue"/>
        <member name="signatureDecisionType" target="SignatureDecision"/>
        <member name="status" target="SignatureStatus"/>
        <member name="creator" target="stringValue"/>
        <member name="auditor" target="stringValue"/>
        <member name="source" target="stringValue"/>
        <member name="description" target="stringValue"/>
        <member name="creationTime" target="stringValue"/>
        <member name="content" target="stringValue"/>
        <member name="lineage" target="stringValue"/>
        <member name="threatClassification" target="stringValue"/>
    </structure>

    <required target="SignatureListItem$signatureId" />
    <required target="SignatureListItem$signatureDecisionType" />
    <required target="SignatureListItem$status" />
    <required target="SignatureListItem$creator" />
    <required target="SignatureListItem$auditor" />
    <required target="SignatureListItem$source" />
    <required target="SignatureListItem$description" />
    <required target="SignatureListItem$creationTime" />
    <required target="SignatureListItem$content" />

    <list name="SignatureList">
        <member target="SignatureListItem"/>
    </list>

    <structure name="SearchSignaturesResponse">
        <member name="items" target="SignatureList"/>
        <member name="nextToken" target="stringValue"/>
        <member name="totalCount" target="intValue"/>
    </structure>

    <required target="SearchSignaturesResponse$items" />

</definition>
