<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.eeyore.humanevaluation.model" version="1.0">
    <documentation target="ScatterPlotStartPlot">
        Start a new async request for generating a scatter plot
    </documentation>

    <operation name="ScatterPlotStartPlot">
        <input target="ScatterPlotStartPlotRequest"/>
        <output target="ScatterPlotStartPlotResponse"/>
        <error target="InternalException"/>
        <error target="MissingParametersException"/>
    </operation>

    <http target="ScatterPlotStartPlot">
        <verb value="POST"/>
        <uri value="scatterplot_startplot"/>
    </http>

    <structure name="ScatterPlotStartPlotRequest">
        <member name="renders" target="ScatterPlotCreativeRenderList"/>
        <member name="tag" target="stringValue"/>
        <member name="inputDataFile" target="ScatterPlotInputDataFile"/>
    </structure>

    <required target="ScatterPlotStartPlotRequest$renders"/>

    <structure name="ScatterPlotStartPlotResponse">
        <member name="outputLocation" target="stringValue"/>
    </structure>

    <required target="ScatterPlotStartPlotResponse$outputLocation" />

    <list name="ScatterPlotCreativeRenderList">
        <member target="ScatterPlotCreativeRender"/>
    </list>

    <structure name="ScatterPlotCreativeRender">
        <member name="creativeID" target="stringValue"/>
        <member name="creativeIDSpace" target="CreativeIDSpace"/>
        <member name="renderID" target="stringValue"/>
        <member name="embeddings" target="doubleList"/>
    </structure>
    <required target="ScatterPlotCreativeRender$creativeID" />
    <required target="ScatterPlotCreativeRender$creativeIDSpace" />
    <required target="ScatterPlotCreativeRender$renderID" />

    <structure name="ScatterPlotInputDataFile">
        <member name="header" target="stringList"/>
        <member name="data" target="stringListList"/>
    </structure>
</definition>
