<?xml version="1.0" encoding="UTF-8"?>
<definition assembly="com.amazon.woozle.threatstore.model" version="1.0">

    <documentation target="ChangeSignatureStatus">
        Changes the status of an existing signature. Status can only be changed to SHADOW or LIVE. Operation is idempotent.
    </documentation>

    <operation name="ChangeSignatureStatus">
        <input target="ChangeSignatureStatusInput"/>
        <output target="ChangeSignatureStatusOutput"/>
        <error target="UnauthorizedException"/>
        <error target="ForbiddenException"/>
        <error target="NotFoundError"/>
        <error target="InvalidRequestParametersException"/>
        <error target="InternalException"/>
    </operation>

    <http target="ChangeSignatureStatus">
        <verb value="POST"/>
        <uri value="change-signature-status/{signatureId}"/>
    </http>

    <structure name="ChangeSignatureStatusInput">
        <member name="signatureId" target="SignatureId"/>
        <member name="newStatus" target="RequestedSignatureStatus"/>
        <member name="requester" target="AuthenticatedEntity"/>
        <member name="approver" target="AuthenticatedUser"/>
    </structure>

    

    <required target="ChangeSignatureStatusInput$signatureId"/>
    <required target="ChangeSignatureStatusInput$newStatus"/>
    <required target="ChangeSignatureStatusInput$requester"/>
    <required target="ChangeSignatureStatusInput$approver"/>

    <httplabel target="ChangeSignatureStatusInput$signatureId">
        <label value="signatureId"/>
    </httplabel>

    <structure name="ChangeSignatureStatusOutput"/>

</definition>
