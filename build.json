{"models": {"EeyoreHumanEvaluation": {"assembly": "com.amazon.eeyore.humanevaluation.model", "metadata": {"api_version": "2017-07-25", "endpoint_prefix": "EeyoreHumanEvaluationBackendApi", "json_version": 1.1, "service_abbreviation": "EeyoreHumanEvaluationBackendApi", "service_full_name": "Backend Api for EeyoreHumanEvaluation UI", "signature_version": "v4", "signing_name": "execute-api", "type": "rest-json"}, "service": "EeyoreHumanEvaluationBackendApi", "swagger": {"basePath": "/", "executionRoleReferenceGenerator": "com.amazonaws.coral2swagger.generator.references.SingletonReferenceGenerator", "lambdaReferenceGenerator": "com.amazonaws.coral2swagger.generator.references.SingletonReferenceGenerator", "enableCORS": true, "extraAllowedHeaders": "x-amz-target"}}}}