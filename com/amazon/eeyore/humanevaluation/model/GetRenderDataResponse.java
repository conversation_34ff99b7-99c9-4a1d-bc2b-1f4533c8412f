package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="GetRenderDataResponse")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class GetRenderDataResponse extends java.lang.Object  {

  /**
   * Statically creates a builder instance for GetRenderDataResponse.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of GetRenderDataResponse.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected CreativeDetails creativeDetails;
    /**
     * Sets the value of the field "creativeDetails" to be used for the constructed object.
     * @param creativeDetails
     *   The value of the "creativeDetails" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeDetails(CreativeDetails creativeDetails) {
      this.creativeDetails = creativeDetails;
      return this;
    }

    protected RenderDetails renderDetails;
    /**
     * Sets the value of the field "renderDetails" to be used for the constructed object.
     * @param renderDetails
     *   The value of the "renderDetails" field.
     * @return
     *   This builder.
     */
    public Builder withRenderDetails(RenderDetails renderDetails) {
      this.renderDetails = renderDetails;
      return this;
    }

    protected List<Screenshot> screenshots;
    /**
     * Sets the value of the field "screenshots" to be used for the constructed object.
     * @param screenshots
     *   The value of the "screenshots" field.
     * @return
     *   This builder.
     */
    public Builder withScreenshots(List<Screenshot> screenshots) {
      this.screenshots = screenshots;
      return this;
    }

    protected List<ScreencastFrame> screencastFrames;
    /**
     * Sets the value of the field "screencastFrames" to be used for the constructed object.
     * @param screencastFrames
     *   The value of the "screencastFrames" field.
     * @return
     *   This builder.
     */
    public Builder withScreencastFrames(List<ScreencastFrame> screencastFrames) {
      this.screencastFrames = screencastFrames;
      return this;
    }

    protected List<ScreenCaptureVideo> screenCaptureVideos;
    /**
     * Sets the value of the field "screenCaptureVideos" to be used for the constructed object.
     * @param screenCaptureVideos
     *   The value of the "screenCaptureVideos" field.
     * @return
     *   This builder.
     */
    public Builder withScreenCaptureVideos(List<ScreenCaptureVideo> screenCaptureVideos) {
      this.screenCaptureVideos = screenCaptureVideos;
      return this;
    }

    protected String consoleLogLinesLocation;
    /**
     * Sets the value of the field "consoleLogLinesLocation" to be used for the constructed object.
     * @param consoleLogLinesLocation
     *   The value of the "consoleLogLinesLocation" field.
     * @return
     *   This builder.
     */
    public Builder withConsoleLogLinesLocation(String consoleLogLinesLocation) {
      this.consoleLogLinesLocation = consoleLogLinesLocation;
      return this;
    }

    protected String networkTraceLocation;
    /**
     * Sets the value of the field "networkTraceLocation" to be used for the constructed object.
     * @param networkTraceLocation
     *   The value of the "networkTraceLocation" field.
     * @return
     *   This builder.
     */
    public Builder withNetworkTraceLocation(String networkTraceLocation) {
      this.networkTraceLocation = networkTraceLocation;
      return this;
    }

    protected Annotations annotations;
    /**
     * Sets the value of the field "annotations" to be used for the constructed object.
     * @param annotations
     *   The value of the "annotations" field.
     * @return
     *   This builder.
     */
    public Builder withAnnotations(Annotations annotations) {
      this.annotations = annotations;
      return this;
    }

    protected String detectedLandingPageUrl;
    /**
     * Sets the value of the field "detectedLandingPageUrl" to be used for the constructed object.
     * @param detectedLandingPageUrl
     *   The value of the "detectedLandingPageUrl" field.
     * @return
     *   This builder.
     */
    public Builder withDetectedLandingPageUrl(String detectedLandingPageUrl) {
      this.detectedLandingPageUrl = detectedLandingPageUrl;
      return this;
    }

    protected RenderArtifacts successfulRenderArtifacts;
    /**
     * Sets the value of the field "successfulRenderArtifacts" to be used for the constructed object.
     * @param successfulRenderArtifacts
     *   The value of the "successfulRenderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withSuccessfulRenderArtifacts(RenderArtifacts successfulRenderArtifacts) {
      this.successfulRenderArtifacts = successfulRenderArtifacts;
      return this;
    }

    protected List<FailedRenderArtifacts> failedRenderArtifacts;
    /**
     * Sets the value of the field "failedRenderArtifacts" to be used for the constructed object.
     * @param failedRenderArtifacts
     *   The value of the "failedRenderArtifacts" field.
     * @return
     *   This builder.
     */
    public Builder withFailedRenderArtifacts(List<FailedRenderArtifacts> failedRenderArtifacts) {
      this.failedRenderArtifacts = failedRenderArtifacts;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(GetRenderDataResponse instance) {
      instance.setCreativeDetails(this.creativeDetails);
      instance.setRenderDetails(this.renderDetails);
      instance.setScreenshots(this.screenshots);
      instance.setScreencastFrames(this.screencastFrames);
      instance.setScreenCaptureVideos(this.screenCaptureVideos);
      instance.setConsoleLogLinesLocation(this.consoleLogLinesLocation);
      instance.setNetworkTraceLocation(this.networkTraceLocation);
      instance.setAnnotations(this.annotations);
      instance.setDetectedLandingPageUrl(this.detectedLandingPageUrl);
      instance.setSuccessfulRenderArtifacts(this.successfulRenderArtifacts);
      instance.setFailedRenderArtifacts(this.failedRenderArtifacts);
    }

    /**
     * Builds an instance of GetRenderDataResponse.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public GetRenderDataResponse build() {
      GetRenderDataResponse instance = new GetRenderDataResponse();

      populate(instance);

      return instance;
    }
  };

  private CreativeDetails creativeDetails;
  private RenderDetails renderDetails;
  private List<Screenshot> screenshots;
  private List<ScreencastFrame> screencastFrames;
  private List<ScreenCaptureVideo> screenCaptureVideos;
  private String consoleLogLinesLocation;
  private String networkTraceLocation;
  private Annotations annotations;
  private String detectedLandingPageUrl;
  private RenderArtifacts successfulRenderArtifacts;
  private List<FailedRenderArtifacts> failedRenderArtifacts;

  @Required()
  public CreativeDetails getCreativeDetails() {
    return this.creativeDetails;
  }

  public void setCreativeDetails(CreativeDetails creativeDetails) {
    this.creativeDetails = creativeDetails;
  }

  @Required()
  public RenderDetails getRenderDetails() {
    return this.renderDetails;
  }

  public void setRenderDetails(RenderDetails renderDetails) {
    this.renderDetails = renderDetails;
  }

  public List<Screenshot> getScreenshots() {
    return this.screenshots;
  }

  public void setScreenshots(List<Screenshot> screenshots) {
    this.screenshots = screenshots;
  }

  public List<ScreencastFrame> getScreencastFrames() {
    return this.screencastFrames;
  }

  public void setScreencastFrames(List<ScreencastFrame> screencastFrames) {
    this.screencastFrames = screencastFrames;
  }

  public List<ScreenCaptureVideo> getScreenCaptureVideos() {
    return this.screenCaptureVideos;
  }

  public void setScreenCaptureVideos(List<ScreenCaptureVideo> screenCaptureVideos) {
    this.screenCaptureVideos = screenCaptureVideos;
  }

  public String getConsoleLogLinesLocation() {
    return this.consoleLogLinesLocation;
  }

  public void setConsoleLogLinesLocation(String consoleLogLinesLocation) {
    this.consoleLogLinesLocation = consoleLogLinesLocation;
  }

  public String getNetworkTraceLocation() {
    return this.networkTraceLocation;
  }

  public void setNetworkTraceLocation(String networkTraceLocation) {
    this.networkTraceLocation = networkTraceLocation;
  }

  public Annotations getAnnotations() {
    return this.annotations;
  }

  public void setAnnotations(Annotations annotations) {
    this.annotations = annotations;
  }

  public String getDetectedLandingPageUrl() {
    return this.detectedLandingPageUrl;
  }

  public void setDetectedLandingPageUrl(String detectedLandingPageUrl) {
    this.detectedLandingPageUrl = detectedLandingPageUrl;
  }

  public RenderArtifacts getSuccessfulRenderArtifacts() {
    return this.successfulRenderArtifacts;
  }

  public void setSuccessfulRenderArtifacts(RenderArtifacts successfulRenderArtifacts) {
    this.successfulRenderArtifacts = successfulRenderArtifacts;
  }

  public List<FailedRenderArtifacts> getFailedRenderArtifacts() {
    return this.failedRenderArtifacts;
  }

  public void setFailedRenderArtifacts(List<FailedRenderArtifacts> failedRenderArtifacts) {
    this.failedRenderArtifacts = failedRenderArtifacts;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.GetRenderDataResponse");

  /**
   * HashCode implementation for GetRenderDataResponse
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreativeDetails(),
        getRenderDetails(),
        getScreenshots(),
        getScreencastFrames(),
        getScreenCaptureVideos(),
        getConsoleLogLinesLocation(),
        getNetworkTraceLocation(),
        getAnnotations(),
        getDetectedLandingPageUrl(),
        getSuccessfulRenderArtifacts(),
        getFailedRenderArtifacts());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for GetRenderDataResponse
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof GetRenderDataResponse)) {
      return false;
    }

    GetRenderDataResponse that = (GetRenderDataResponse) other;

    return
        Objects.equals(getCreativeDetails(), that.getCreativeDetails())
        && Objects.equals(getRenderDetails(), that.getRenderDetails())
        && Objects.equals(getScreenshots(), that.getScreenshots())
        && Objects.equals(getScreencastFrames(), that.getScreencastFrames())
        && Objects.equals(getScreenCaptureVideos(), that.getScreenCaptureVideos())
        && Objects.equals(getConsoleLogLinesLocation(), that.getConsoleLogLinesLocation())
        && Objects.equals(getNetworkTraceLocation(), that.getNetworkTraceLocation())
        && Objects.equals(getAnnotations(), that.getAnnotations())
        && Objects.equals(getDetectedLandingPageUrl(), that.getDetectedLandingPageUrl())
        && Objects.equals(getSuccessfulRenderArtifacts(), that.getSuccessfulRenderArtifacts())
        && Objects.equals(getFailedRenderArtifacts(), that.getFailedRenderArtifacts());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("GetRenderDataResponse(");

    ret.append("creativeDetails=");
    ret.append(String.valueOf(creativeDetails));
    ret.append(", ");

    ret.append("renderDetails=");
    ret.append(String.valueOf(renderDetails));
    ret.append(", ");

    ret.append("screenshots=");
    ret.append(String.valueOf(screenshots));
    ret.append(", ");

    ret.append("screencastFrames=");
    ret.append(String.valueOf(screencastFrames));
    ret.append(", ");

    ret.append("screenCaptureVideos=");
    ret.append(String.valueOf(screenCaptureVideos));
    ret.append(", ");

    ret.append("consoleLogLinesLocation=");
    ret.append(String.valueOf(consoleLogLinesLocation));
    ret.append(", ");

    ret.append("networkTraceLocation=");
    ret.append(String.valueOf(networkTraceLocation));
    ret.append(", ");

    ret.append("annotations=");
    ret.append(String.valueOf(annotations));
    ret.append(", ");

    ret.append("detectedLandingPageUrl=");
    ret.append(String.valueOf(detectedLandingPageUrl));
    ret.append(", ");

    ret.append("successfulRenderArtifacts=");
    ret.append(String.valueOf(successfulRenderArtifacts));
    ret.append(", ");

    ret.append("failedRenderArtifacts=");
    ret.append(String.valueOf(failedRenderArtifacts));
    ret.append(")");

    return ret.toString();
  }

}
