package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="SignatureListItem")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SignatureListItem extends java.lang.Object  {

  /**
   * Statically creates a builder instance for SignatureListItem.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of SignatureListItem.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String signatureId;
    /**
     * Sets the value of the field "signatureId" to be used for the constructed object.
     * @param signatureId
     *   The value of the "signatureId" field.
     * @return
     *   This builder.
     */
    public Builder withSignatureId(String signatureId) {
      this.signatureId = signatureId;
      return this;
    }

    protected String signatureDecisionType;
    /**
     * Sets the value of the field "signatureDecisionType" to be used for the constructed object.
     * @param signatureDecisionType
     *   The value of the "signatureDecisionType" field.
     * @return
     *   This builder.
     */
    public Builder withSignatureDecisionType(String signatureDecisionType) {
      this.signatureDecisionType = signatureDecisionType;
      return this;
    }

    protected String status;
    /**
     * Sets the value of the field "status" to be used for the constructed object.
     * @param status
     *   The value of the "status" field.
     * @return
     *   This builder.
     */
    public Builder withStatus(String status) {
      this.status = status;
      return this;
    }

    protected String creator;
    /**
     * Sets the value of the field "creator" to be used for the constructed object.
     * @param creator
     *   The value of the "creator" field.
     * @return
     *   This builder.
     */
    public Builder withCreator(String creator) {
      this.creator = creator;
      return this;
    }

    protected String auditor;
    /**
     * Sets the value of the field "auditor" to be used for the constructed object.
     * @param auditor
     *   The value of the "auditor" field.
     * @return
     *   This builder.
     */
    public Builder withAuditor(String auditor) {
      this.auditor = auditor;
      return this;
    }

    protected String source;
    /**
     * Sets the value of the field "source" to be used for the constructed object.
     * @param source
     *   The value of the "source" field.
     * @return
     *   This builder.
     */
    public Builder withSource(String source) {
      this.source = source;
      return this;
    }

    protected String description;
    /**
     * Sets the value of the field "description" to be used for the constructed object.
     * @param description
     *   The value of the "description" field.
     * @return
     *   This builder.
     */
    public Builder withDescription(String description) {
      this.description = description;
      return this;
    }

    protected String creationTime;
    /**
     * Sets the value of the field "creationTime" to be used for the constructed object.
     * @param creationTime
     *   The value of the "creationTime" field.
     * @return
     *   This builder.
     */
    public Builder withCreationTime(String creationTime) {
      this.creationTime = creationTime;
      return this;
    }

    protected String content;
    /**
     * Sets the value of the field "content" to be used for the constructed object.
     * @param content
     *   The value of the "content" field.
     * @return
     *   This builder.
     */
    public Builder withContent(String content) {
      this.content = content;
      return this;
    }

    protected String lineage;
    /**
     * Sets the value of the field "lineage" to be used for the constructed object.
     * @param lineage
     *   The value of the "lineage" field.
     * @return
     *   This builder.
     */
    public Builder withLineage(String lineage) {
      this.lineage = lineage;
      return this;
    }

    protected String threatClassification;
    /**
     * Sets the value of the field "threatClassification" to be used for the constructed object.
     * @param threatClassification
     *   The value of the "threatClassification" field.
     * @return
     *   This builder.
     */
    public Builder withThreatClassification(String threatClassification) {
      this.threatClassification = threatClassification;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(SignatureListItem instance) {
      instance.setSignatureId(this.signatureId);
      instance.setSignatureDecisionType(this.signatureDecisionType);
      instance.setStatus(this.status);
      instance.setCreator(this.creator);
      instance.setAuditor(this.auditor);
      instance.setSource(this.source);
      instance.setDescription(this.description);
      instance.setCreationTime(this.creationTime);
      instance.setContent(this.content);
      instance.setLineage(this.lineage);
      instance.setThreatClassification(this.threatClassification);
    }

    /**
     * Builds an instance of SignatureListItem.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public SignatureListItem build() {
      SignatureListItem instance = new SignatureListItem();

      populate(instance);

      return instance;
    }
  };

  private String signatureId;
  private String signatureDecisionType;
  private String status;
  private String creator;
  private String auditor;
  private String source;
  private String description;
  private String creationTime;
  private String content;
  private String lineage;
  private String threatClassification;

  @Required()
  public String getSignatureId() {
    return this.signatureId;
  }

  public void setSignatureId(String signatureId) {
    this.signatureId = signatureId;
  }

  @Required()
  @EnumValues(value={"THREAT","SAFETY"})
  public String getSignatureDecisionType() {
    return this.signatureDecisionType;
  }

  public void setSignatureDecisionType(String signatureDecisionType) {
    this.signatureDecisionType = signatureDecisionType;
  }

  @Required()
  @EnumValues(value={"SHADOW","LIVE","DELETED","PROPOSED","PENDING_AUTO_VERIFICATION","REJECTED","PENDING_MANUAL_REVIEW","LIVE_REFERRAL"})
  public String getStatus() {
    return this.status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  @Required()
  public String getCreator() {
    return this.creator;
  }

  public void setCreator(String creator) {
    this.creator = creator;
  }

  @Required()
  public String getAuditor() {
    return this.auditor;
  }

  public void setAuditor(String auditor) {
    this.auditor = auditor;
  }

  @Required()
  public String getSource() {
    return this.source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  @Required()
  public String getDescription() {
    return this.description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  @Required()
  public String getCreationTime() {
    return this.creationTime;
  }

  public void setCreationTime(String creationTime) {
    this.creationTime = creationTime;
  }

  @Required()
  public String getContent() {
    return this.content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getLineage() {
    return this.lineage;
  }

  public void setLineage(String lineage) {
    this.lineage = lineage;
  }

  public String getThreatClassification() {
    return this.threatClassification;
  }

  public void setThreatClassification(String threatClassification) {
    this.threatClassification = threatClassification;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.SignatureListItem");

  /**
   * HashCode implementation for SignatureListItem
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSignatureId(),
        getSignatureDecisionType(),
        getStatus(),
        getCreator(),
        getAuditor(),
        getSource(),
        getDescription(),
        getCreationTime(),
        getContent(),
        getLineage(),
        getThreatClassification());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for SignatureListItem
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof SignatureListItem)) {
      return false;
    }

    SignatureListItem that = (SignatureListItem) other;

    return
        Objects.equals(getSignatureId(), that.getSignatureId())
        && Objects.equals(getSignatureDecisionType(), that.getSignatureDecisionType())
        && Objects.equals(getStatus(), that.getStatus())
        && Objects.equals(getCreator(), that.getCreator())
        && Objects.equals(getAuditor(), that.getAuditor())
        && Objects.equals(getSource(), that.getSource())
        && Objects.equals(getDescription(), that.getDescription())
        && Objects.equals(getCreationTime(), that.getCreationTime())
        && Objects.equals(getContent(), that.getContent())
        && Objects.equals(getLineage(), that.getLineage())
        && Objects.equals(getThreatClassification(), that.getThreatClassification());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("SignatureListItem(");

    ret.append("signatureId=");
    ret.append(String.valueOf(signatureId));
    ret.append(", ");

    ret.append("signatureDecisionType=");
    ret.append(String.valueOf(signatureDecisionType));
    ret.append(", ");

    ret.append("status=");
    ret.append(String.valueOf(status));
    ret.append(", ");

    ret.append("creator=");
    ret.append(String.valueOf(creator));
    ret.append(", ");

    ret.append("auditor=");
    ret.append(String.valueOf(auditor));
    ret.append(", ");

    ret.append("source=");
    ret.append(String.valueOf(source));
    ret.append(", ");

    ret.append("description=");
    ret.append(String.valueOf(description));
    ret.append(", ");

    ret.append("creationTime=");
    ret.append(String.valueOf(creationTime));
    ret.append(", ");

    ret.append("content=");
    ret.append(String.valueOf(content));
    ret.append(", ");

    ret.append("lineage=");
    ret.append(String.valueOf(lineage));
    ret.append(", ");

    ret.append("threatClassification=");
    ret.append(String.valueOf(threatClassification));
    ret.append(")");

    return ret.toString();
  }

}
