package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import java.util.List;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="VastRenderedRenderArtifacts")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class VastRenderedRenderArtifacts extends StandardRenderArtifacts  {

  /**
   * Statically creates a builder instance for VastRenderedRenderArtifacts.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of VastRenderedRenderArtifacts.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder extends StandardRenderArtifacts.Builder {

    protected List<MediaFile> mediaFiles;
    /**
     * Sets the value of the field "mediaFiles" to be used for the constructed object.
     * @param mediaFiles
     *   The value of the "mediaFiles" field.
     * @return
     *   This builder.
     */
    public Builder withMediaFiles(List<MediaFile> mediaFiles) {
      this.mediaFiles = mediaFiles;
      return this;
    }

    @Override
    public Builder withScreenshots(List<Screenshot> screenshots) {
      super.withScreenshots(screenshots);
      return this;
    }

    @Override
    public Builder withScreencastFrames(List<ScreencastFrame> screencastFrames) {
      super.withScreencastFrames(screencastFrames);
      return this;
    }

    @Override
    public Builder withScreenCaptureVideos(List<ScreenCaptureVideo> screenCaptureVideos) {
      super.withScreenCaptureVideos(screenCaptureVideos);
      return this;
    }

    @Override
    public Builder withConsoleLogLinesLocation(String consoleLogLinesLocation) {
      super.withConsoleLogLinesLocation(consoleLogLinesLocation);
      return this;
    }

    @Override
    public Builder withNetworkTraceLocation(String networkTraceLocation) {
      super.withNetworkTraceLocation(networkTraceLocation);
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(VastRenderedRenderArtifacts instance) {
      super.populate(instance);

      instance.setMediaFiles(this.mediaFiles);
    }

    /**
     * Builds an instance of VastRenderedRenderArtifacts.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public VastRenderedRenderArtifacts build() {
      VastRenderedRenderArtifacts instance = new VastRenderedRenderArtifacts();

      populate(instance);

      return instance;
    }
  };

  private List<MediaFile> mediaFiles;

  public List<MediaFile> getMediaFiles() {
    return this.mediaFiles;
  }

  public void setMediaFiles(List<MediaFile> mediaFiles) {
    this.mediaFiles = mediaFiles;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.VastRenderedRenderArtifacts");

  /**
   * HashCode implementation for VastRenderedRenderArtifacts
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        super.hashCode(),
        classNameHashCode,
        getMediaFiles());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for VastRenderedRenderArtifacts
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof VastRenderedRenderArtifacts)) {
      return false;
    }

    VastRenderedRenderArtifacts that = (VastRenderedRenderArtifacts) other;

    return
        super.equals(other)
        && Objects.equals(getMediaFiles(), that.getMediaFiles());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("VastRenderedRenderArtifacts(");

    ret.append("super=");
    ret.append(super.toString());
    ret.append(", ");

    ret.append("mediaFiles=");
    ret.append(String.valueOf(mediaFiles));
    ret.append(")");

    return ret.toString();
  }

}
