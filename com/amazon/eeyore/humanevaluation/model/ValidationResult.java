package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="ValidationResult")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ValidationResult extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ValidationResult.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ValidationResult.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String domain;
    /**
     * Sets the value of the field "domain" to be used for the constructed object.
     * @param domain
     *   The value of the "domain" field.
     * @return
     *   This builder.
     */
    public Builder withDomain(String domain) {
      this.domain = domain;
      return this;
    }

    protected String status;
    /**
     * Sets the value of the field "status" to be used for the constructed object.
     * @param status
     *   The value of the "status" field.
     * @return
     *   This builder.
     */
    public Builder withStatus(String status) {
      this.status = status;
      return this;
    }

    protected String reason;
    /**
     * Sets the value of the field "reason" to be used for the constructed object.
     * @param reason
     *   The value of the "reason" field.
     * @return
     *   This builder.
     */
    public Builder withReason(String reason) {
      this.reason = reason;
      return this;
    }

    protected ImpactAssessment impactAssessment;
    /**
     * Sets the value of the field "impactAssessment" to be used for the constructed object.
     * @param impactAssessment
     *   The value of the "impactAssessment" field.
     * @return
     *   This builder.
     */
    public Builder withImpactAssessment(ImpactAssessment impactAssessment) {
      this.impactAssessment = impactAssessment;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ValidationResult instance) {
      instance.setDomain(this.domain);
      instance.setStatus(this.status);
      instance.setReason(this.reason);
      instance.setImpactAssessment(this.impactAssessment);
    }

    /**
     * Builds an instance of ValidationResult.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ValidationResult build() {
      ValidationResult instance = new ValidationResult();

      populate(instance);

      return instance;
    }
  };

  private String domain;
  private String status;
  private String reason;
  private ImpactAssessment impactAssessment;

  @Required()
  public String getDomain() {
    return this.domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  @EnumValues(value={"APPROVED","REJECTED","N/A"})
  @Required()
  public String getStatus() {
    return this.status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getReason() {
    return this.reason;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public ImpactAssessment getImpactAssessment() {
    return this.impactAssessment;
  }

  public void setImpactAssessment(ImpactAssessment impactAssessment) {
    this.impactAssessment = impactAssessment;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ValidationResult");

  /**
   * HashCode implementation for ValidationResult
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getDomain(),
        getStatus(),
        getReason(),
        getImpactAssessment());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ValidationResult
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ValidationResult)) {
      return false;
    }

    ValidationResult that = (ValidationResult) other;

    return
        Objects.equals(getDomain(), that.getDomain())
        && Objects.equals(getStatus(), that.getStatus())
        && Objects.equals(getReason(), that.getReason())
        && Objects.equals(getImpactAssessment(), that.getImpactAssessment());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ValidationResult(");

    ret.append("domain=");
    ret.append(String.valueOf(domain));
    ret.append(", ");

    ret.append("status=");
    ret.append(String.valueOf(status));
    ret.append(", ");

    ret.append("reason=");
    ret.append(String.valueOf(reason));
    ret.append(", ");

    ret.append("impactAssessment=");
    ret.append(String.valueOf(impactAssessment));
    ret.append(")");

    return ret.toString();
  }

}
