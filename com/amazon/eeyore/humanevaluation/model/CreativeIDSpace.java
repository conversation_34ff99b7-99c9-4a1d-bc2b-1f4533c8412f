package com.amazon.eeyore.humanevaluation.model;

@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class CreativeIDSpace {

  private CreativeIDSpace() { throw new UnsupportedOperationException(); }

  public static final String ERISKAY_ID = "eriskayCreativeIDSpace";
  public static final String RODEO_CFID = "eriskayRodeoCfidSpace";
  public static final String AXIOM_ID = "eriskayAxiomIDSpace";
  public static final String AAX_CRID = "eriskayAaxCridSpace";
  public static final String CANARY_ID = "eriskayCanaryIDSpace";
  public static final String INTEG_ID = "eriskayIntegIDSpace";
  public static final String LOADTEST_ID = "eriskayLoadTestIDSpace";

  private static final String[] values = {
    ERISKAY_ID, RODEO_CFID, AXIOM_ID, AAX_CRID, CANARY_ID, INTEG_ID, LOADTEST_ID
  };

  public static String[] values() {
    return values;
  }
}
