package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="RenderTargetingConfiguration")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderTargetingConfiguration extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderTargetingConfiguration.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderTargetingConfiguration.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected RenderGeoTargeting geoLocation;
    /**
     * Sets the value of the field "geoLocation" to be used for the constructed object.
     * @param geoLocation
     *   The value of the "geoLocation" field.
     * @return
     *   This builder.
     */
    public Builder withGeoLocation(RenderGeoTargeting geoLocation) {
      this.geoLocation = geoLocation;
      return this;
    }

    protected String browser;
    /**
     * Sets the value of the field "browser" to be used for the constructed object.
     * @param browser
     *   The value of the "browser" field.
     * @return
     *   This builder.
     */
    public Builder withBrowser(String browser) {
      this.browser = browser;
      return this;
    }

    protected String operatingSystem;
    /**
     * Sets the value of the field "operatingSystem" to be used for the constructed object.
     * @param operatingSystem
     *   The value of the "operatingSystem" field.
     * @return
     *   This builder.
     */
    public Builder withOperatingSystem(String operatingSystem) {
      this.operatingSystem = operatingSystem;
      return this;
    }

    protected String deviceLayout;
    /**
     * Sets the value of the field "deviceLayout" to be used for the constructed object.
     * @param deviceLayout
     *   The value of the "deviceLayout" field.
     * @return
     *   This builder.
     */
    public Builder withDeviceLayout(String deviceLayout) {
      this.deviceLayout = deviceLayout;
      return this;
    }

    protected String deviceName;
    /**
     * Sets the value of the field "deviceName" to be used for the constructed object.
     * @param deviceName
     *   The value of the "deviceName" field.
     * @return
     *   This builder.
     */
    public Builder withDeviceName(String deviceName) {
      this.deviceName = deviceName;
      return this;
    }

    protected String userAgent;
    /**
     * Sets the value of the field "userAgent" to be used for the constructed object.
     * @param userAgent
     *   The value of the "userAgent" field.
     * @return
     *   This builder.
     */
    public Builder withUserAgent(String userAgent) {
      this.userAgent = userAgent;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderTargetingConfiguration instance) {
      instance.setGeoLocation(this.geoLocation);
      instance.setBrowser(this.browser);
      instance.setOperatingSystem(this.operatingSystem);
      instance.setDeviceLayout(this.deviceLayout);
      instance.setDeviceName(this.deviceName);
      instance.setUserAgent(this.userAgent);
    }

    /**
     * Builds an instance of RenderTargetingConfiguration.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderTargetingConfiguration build() {
      RenderTargetingConfiguration instance = new RenderTargetingConfiguration();

      populate(instance);

      return instance;
    }
  };

  private RenderGeoTargeting geoLocation;
  private String browser;
  private String operatingSystem;
  private String deviceLayout;
  private String deviceName;
  private String userAgent;

  @Required()
  public RenderGeoTargeting getGeoLocation() {
    return this.geoLocation;
  }

  public void setGeoLocation(RenderGeoTargeting geoLocation) {
    this.geoLocation = geoLocation;
  }

  @Required()
  public String getBrowser() {
    return this.browser;
  }

  public void setBrowser(String browser) {
    this.browser = browser;
  }

  @Required()
  public String getOperatingSystem() {
    return this.operatingSystem;
  }

  public void setOperatingSystem(String operatingSystem) {
    this.operatingSystem = operatingSystem;
  }

  public String getDeviceLayout() {
    return this.deviceLayout;
  }

  public void setDeviceLayout(String deviceLayout) {
    this.deviceLayout = deviceLayout;
  }

  public String getDeviceName() {
    return this.deviceName;
  }

  public void setDeviceName(String deviceName) {
    this.deviceName = deviceName;
  }

  public String getUserAgent() {
    return this.userAgent;
  }

  public void setUserAgent(String userAgent) {
    this.userAgent = userAgent;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderTargetingConfiguration");

  /**
   * HashCode implementation for RenderTargetingConfiguration
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getGeoLocation(),
        getBrowser(),
        getOperatingSystem(),
        getDeviceLayout(),
        getDeviceName(),
        getUserAgent());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderTargetingConfiguration
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderTargetingConfiguration)) {
      return false;
    }

    RenderTargetingConfiguration that = (RenderTargetingConfiguration) other;

    return
        Objects.equals(getGeoLocation(), that.getGeoLocation())
        && Objects.equals(getBrowser(), that.getBrowser())
        && Objects.equals(getOperatingSystem(), that.getOperatingSystem())
        && Objects.equals(getDeviceLayout(), that.getDeviceLayout())
        && Objects.equals(getDeviceName(), that.getDeviceName())
        && Objects.equals(getUserAgent(), that.getUserAgent());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderTargetingConfiguration(");

    ret.append("geoLocation=");
    ret.append(String.valueOf(geoLocation));
    ret.append(", ");

    ret.append("browser=");
    ret.append(String.valueOf(browser));
    ret.append(", ");

    ret.append("operatingSystem=");
    ret.append(String.valueOf(operatingSystem));
    ret.append(", ");

    ret.append("deviceLayout=");
    ret.append(String.valueOf(deviceLayout));
    ret.append(", ");

    ret.append("deviceName=");
    ret.append(String.valueOf(deviceName));
    ret.append(", ");

    ret.append("userAgent=");
    ret.append(String.valueOf(userAgent));
    ret.append(")");

    return ret.toString();
  }

}
