package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="RenderCreativeConfiguration")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderCreativeConfiguration extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderCreativeConfiguration.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderCreativeConfiguration.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String creativeType;
    /**
     * Sets the value of the field "creativeType" to be used for the constructed object.
     * @param creativeType
     *   The value of the "creativeType" field.
     * @return
     *   This builder.
     */
    public Builder withCreativeType(String creativeType) {
      this.creativeType = creativeType;
      return this;
    }

    protected String markupUrl;
    /**
     * Sets the value of the field "markupUrl" to be used for the constructed object.
     * @param markupUrl
     *   The value of the "markupUrl" field.
     * @return
     *   This builder.
     */
    public Builder withMarkupUrl(String markupUrl) {
      this.markupUrl = markupUrl;
      return this;
    }

    protected String protocol;
    /**
     * Sets the value of the field "protocol" to be used for the constructed object.
     * @param protocol
     *   The value of the "protocol" field.
     * @return
     *   This builder.
     */
    public Builder withProtocol(String protocol) {
      this.protocol = protocol;
      return this;
    }

    protected String publisherPageUrl;
    /**
     * Sets the value of the field "publisherPageUrl" to be used for the constructed object.
     * @param publisherPageUrl
     *   The value of the "publisherPageUrl" field.
     * @return
     *   This builder.
     */
    public Builder withPublisherPageUrl(String publisherPageUrl) {
      this.publisherPageUrl = publisherPageUrl;
      return this;
    }

    protected String containerType;
    /**
     * Sets the value of the field "containerType" to be used for the constructed object.
     * @param containerType
     *   The value of the "containerType" field.
     * @return
     *   This builder.
     */
    public Builder withContainerType(String containerType) {
      this.containerType = containerType;
      return this;
    }

    protected ViewportSize viewportSize;
    /**
     * Sets the value of the field "viewportSize" to be used for the constructed object.
     * @param viewportSize
     *   The value of the "viewportSize" field.
     * @return
     *   This builder.
     */
    public Builder withViewportSize(ViewportSize viewportSize) {
      this.viewportSize = viewportSize;
      return this;
    }

    protected String tcString;
    /**
     * Sets the value of the field "tcString" to be used for the constructed object.
     * @param tcString
     *   The value of the "tcString" field.
     * @return
     *   This builder.
     */
    public Builder withTcString(String tcString) {
      this.tcString = tcString;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderCreativeConfiguration instance) {
      instance.setCreativeType(this.creativeType);
      instance.setMarkupUrl(this.markupUrl);
      instance.setProtocol(this.protocol);
      instance.setPublisherPageUrl(this.publisherPageUrl);
      instance.setContainerType(this.containerType);
      instance.setViewportSize(this.viewportSize);
      instance.setTcString(this.tcString);
    }

    /**
     * Builds an instance of RenderCreativeConfiguration.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderCreativeConfiguration build() {
      RenderCreativeConfiguration instance = new RenderCreativeConfiguration();

      populate(instance);

      return instance;
    }
  };

  private String creativeType;
  private String markupUrl;
  private String protocol;
  private String publisherPageUrl;
  private String containerType;
  private ViewportSize viewportSize;
  private String tcString;

  @Required()
  public String getCreativeType() {
    return this.creativeType;
  }

  public void setCreativeType(String creativeType) {
    this.creativeType = creativeType;
  }

  @Required()
  public String getMarkupUrl() {
    return this.markupUrl;
  }

  public void setMarkupUrl(String markupUrl) {
    this.markupUrl = markupUrl;
  }

  @Required()
  public String getProtocol() {
    return this.protocol;
  }

  public void setProtocol(String protocol) {
    this.protocol = protocol;
  }

  public String getPublisherPageUrl() {
    return this.publisherPageUrl;
  }

  public void setPublisherPageUrl(String publisherPageUrl) {
    this.publisherPageUrl = publisherPageUrl;
  }

  public String getContainerType() {
    return this.containerType;
  }

  public void setContainerType(String containerType) {
    this.containerType = containerType;
  }

  public ViewportSize getViewportSize() {
    return this.viewportSize;
  }

  public void setViewportSize(ViewportSize viewportSize) {
    this.viewportSize = viewportSize;
  }

  public String getTcString() {
    return this.tcString;
  }

  public void setTcString(String tcString) {
    this.tcString = tcString;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderCreativeConfiguration");

  /**
   * HashCode implementation for RenderCreativeConfiguration
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCreativeType(),
        getMarkupUrl(),
        getProtocol(),
        getPublisherPageUrl(),
        getContainerType(),
        getViewportSize(),
        getTcString());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderCreativeConfiguration
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderCreativeConfiguration)) {
      return false;
    }

    RenderCreativeConfiguration that = (RenderCreativeConfiguration) other;

    return
        Objects.equals(getCreativeType(), that.getCreativeType())
        && Objects.equals(getMarkupUrl(), that.getMarkupUrl())
        && Objects.equals(getProtocol(), that.getProtocol())
        && Objects.equals(getPublisherPageUrl(), that.getPublisherPageUrl())
        && Objects.equals(getContainerType(), that.getContainerType())
        && Objects.equals(getViewportSize(), that.getViewportSize())
        && Objects.equals(getTcString(), that.getTcString());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderCreativeConfiguration(");

    ret.append("creativeType=");
    ret.append(String.valueOf(creativeType));
    ret.append(", ");

    ret.append("markupUrl=");
    ret.append(String.valueOf(markupUrl));
    ret.append(", ");

    ret.append("protocol=");
    ret.append(String.valueOf(protocol));
    ret.append(", ");

    ret.append("publisherPageUrl=");
    ret.append(String.valueOf(publisherPageUrl));
    ret.append(", ");

    ret.append("containerType=");
    ret.append(String.valueOf(containerType));
    ret.append(", ");

    ret.append("viewportSize=");
    ret.append(String.valueOf(viewportSize));
    ret.append(", ");

    ret.append("tcString=");
    ret.append(String.valueOf(tcString));
    ret.append(")");

    return ret.toString();
  }

}
