package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlName(value="RenderGeoTargeting")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderGeoTargeting extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderGeoTargeting.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderGeoTargeting.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String countryCode;
    /**
     * Sets the value of the field "countryCode" to be used for the constructed object.
     * @param countryCode
     *   The value of the "countryCode" field.
     * @return
     *   This builder.
     */
    public Builder withCountryCode(String countryCode) {
      this.countryCode = countryCode;
      return this;
    }

    protected String zipCode;
    /**
     * Sets the value of the field "zipCode" to be used for the constructed object.
     * @param zipCode
     *   The value of the "zipCode" field.
     * @return
     *   This builder.
     */
    public Builder withZipCode(String zipCode) {
      this.zipCode = zipCode;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderGeoTargeting instance) {
      instance.setCountryCode(this.countryCode);
      instance.setZipCode(this.zipCode);
    }

    /**
     * Builds an instance of RenderGeoTargeting.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderGeoTargeting build() {
      RenderGeoTargeting instance = new RenderGeoTargeting();

      populate(instance);

      return instance;
    }
  };

  private String countryCode;
  private String zipCode;

  @Required()
  public String getCountryCode() {
    return this.countryCode;
  }

  public void setCountryCode(String countryCode) {
    this.countryCode = countryCode;
  }

  public String getZipCode() {
    return this.zipCode;
  }

  public void setZipCode(String zipCode) {
    this.zipCode = zipCode;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderGeoTargeting");

  /**
   * HashCode implementation for RenderGeoTargeting
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getCountryCode(),
        getZipCode());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderGeoTargeting
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderGeoTargeting)) {
      return false;
    }

    RenderGeoTargeting that = (RenderGeoTargeting) other;

    return
        Objects.equals(getCountryCode(), that.getCountryCode())
        && Objects.equals(getZipCode(), that.getZipCode());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderGeoTargeting(");

    ret.append("countryCode=");
    ret.append(String.valueOf(countryCode));
    ret.append(", ");

    ret.append("zipCode=");
    ret.append(String.valueOf(zipCode));
    ret.append(")");

    return ret.toString();
  }

}
