package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@XmlName(value="RenderClickConfiguration")
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class RenderClickConfiguration extends java.lang.Object  {

  /**
   * Statically creates a builder instance for RenderClickConfiguration.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of RenderClickConfiguration.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected boolean performClick;
    /**
     * Sets the value of the field "performClick" to be used for the constructed object.
     * @param performClick
     *   The value of the "performClick" field.
     * @return
     *   This builder.
     */
    public Builder withPerformClick(boolean performClick) {
      this.performClick = performClick;
      return this;
    }

    protected int clickDelay;
    /**
     * Sets the value of the field "clickDelay" to be used for the constructed object.
     * @param clickDelay
     *   The value of the "clickDelay" field.
     * @return
     *   This builder.
     */
    public Builder withClickDelay(int clickDelay) {
      this.clickDelay = clickDelay;
      return this;
    }

    protected RenderClickLocation clickLocation;
    /**
     * Sets the value of the field "clickLocation" to be used for the constructed object.
     * @param clickLocation
     *   The value of the "clickLocation" field.
     * @return
     *   This builder.
     */
    public Builder withClickLocation(RenderClickLocation clickLocation) {
      this.clickLocation = clickLocation;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(RenderClickConfiguration instance) {
      instance.setPerformClick(this.performClick);
      instance.setClickDelay(this.clickDelay);
      instance.setClickLocation(this.clickLocation);
    }

    /**
     * Builds an instance of RenderClickConfiguration.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public RenderClickConfiguration build() {
      RenderClickConfiguration instance = new RenderClickConfiguration();

      populate(instance);

      return instance;
    }
  };

  private boolean performClick;
  private int clickDelay;
  private RenderClickLocation clickLocation;

  public boolean isPerformClick() {
    return this.performClick;
  }

  public void setPerformClick(boolean performClick) {
    this.performClick = performClick;
  }

  public int getClickDelay() {
    return this.clickDelay;
  }

  public void setClickDelay(int clickDelay) {
    this.clickDelay = clickDelay;
  }

  public RenderClickLocation getClickLocation() {
    return this.clickLocation;
  }

  public void setClickLocation(RenderClickLocation clickLocation) {
    this.clickLocation = clickLocation;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.RenderClickConfiguration");

  /**
   * HashCode implementation for RenderClickConfiguration
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        isPerformClick(),
        getClickDelay(),
        getClickLocation());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for RenderClickConfiguration
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof RenderClickConfiguration)) {
      return false;
    }

    RenderClickConfiguration that = (RenderClickConfiguration) other;

    return
        Objects.equals(isPerformClick(), that.isPerformClick())
        && Objects.equals(getClickDelay(), that.getClickDelay())
        && Objects.equals(getClickLocation(), that.getClickLocation());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("RenderClickConfiguration(");

    ret.append("performClick=");
    ret.append(String.valueOf(performClick));
    ret.append(", ");

    ret.append("clickDelay=");
    ret.append(String.valueOf(clickDelay));
    ret.append(", ");

    ret.append("clickLocation=");
    ret.append(String.valueOf(clickLocation));
    ret.append(")");

    return ret.toString();
  }

}
