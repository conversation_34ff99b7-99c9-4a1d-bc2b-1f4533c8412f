package com.amazon.eeyore.humanevaluation.model;

@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ValidationStatus {

  private ValidationStatus() { throw new UnsupportedOperationException(); }

  public static final String APPROVED = "APPROVED";
  public static final String REJECTED = "REJECTED";
  public static final String N_A = "N/A";

  private static final String[] values = {
    APPROVED, REJECTED, N_A
  };

  public static String[] values() {
    return values;
  }
}
