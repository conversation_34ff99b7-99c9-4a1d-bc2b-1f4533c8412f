package com.amazon.eeyore.humanevaluation.model;

@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class SignatureStatus {

  private SignatureStatus() { throw new UnsupportedOperationException(); }

  public static final String SHADOW = "SHADOW";
  public static final String LIVE = "LIVE";
  public static final String DELETED = "DELETED";
  public static final String PROPOSED = "PROPOSED";
  public static final String PENDING_AUTO_VERIFICATION = "PENDING_AUTO_VERIFICATION";
  public static final String REJECTED = "REJECTED";
  public static final String PENDING_MANUAL_REVIEW = "PENDING_MANUAL_REVIEW";
  public static final String LIVE_REFERRAL = "LIVE_REFERRAL";

  private static final String[] values = {
    SHADOW, LIVE, DELETED, PROPOSED, PENDING_AUTO_VERIFICATION, REJECTED, PENDING_MANUAL_REVIEW, LIVE_REFERRAL
  };

  public static String[] values() {
    return values;
  }
}
