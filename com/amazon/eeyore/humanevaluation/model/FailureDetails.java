package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.EnumValues;
import com.amazon.coral.annotation.Required;
import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="FailureDetails")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class FailureDetails extends java.lang.Object  {

  /**
   * Statically creates a builder instance for FailureDetails.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of FailureDetails.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected String failureMessage;
    /**
     * Sets the value of the field "failureMessage" to be used for the constructed object.
     * @param failureMessage
     *   The value of the "failureMessage" field.
     * @return
     *   This builder.
     */
    public Builder withFailureMessage(String failureMessage) {
      this.failureMessage = failureMessage;
      return this;
    }

    protected String failureType;
    /**
     * Sets the value of the field "failureType" to be used for the constructed object.
     * @param failureType
     *   The value of the "failureType" field.
     * @return
     *   This builder.
     */
    public Builder withFailureType(String failureType) {
      this.failureType = failureType;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(FailureDetails instance) {
      instance.setFailureMessage(this.failureMessage);
      instance.setFailureType(this.failureType);
    }

    /**
     * Builds an instance of FailureDetails.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public FailureDetails build() {
      FailureDetails instance = new FailureDetails();

      populate(instance);

      return instance;
    }
  };

  private String failureMessage;
  private String failureType;

  @Required()
  public String getFailureMessage() {
    return this.failureMessage;
  }

  public void setFailureMessage(String failureMessage) {
    this.failureMessage = failureMessage;
  }

  @Required()
  @EnumValues(value={"BLANK","AREA_TOO_SMALL","MISSING_SCREENSHOTS","TOO_FEW_REQUESTS","CLOUDFLARE_BLOCKED","RENDER_DATA_LIMIT_BREACHED","UNAVAILABLE_PROXY","TRUNCATED_RENDER","INTERNAL_SERVICE_ERROR","UNKNOWN_LANDING_PAGE_PROTOCOL","UNKNOWN_EXCEPTION","MISSING_SCREENCAST_FRAMES"})
  public String getFailureType() {
    return this.failureType;
  }

  public void setFailureType(String failureType) {
    this.failureType = failureType;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.FailureDetails");

  /**
   * HashCode implementation for FailureDetails
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getFailureMessage(),
        getFailureType());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for FailureDetails
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof FailureDetails)) {
      return false;
    }

    FailureDetails that = (FailureDetails) other;

    return
        Objects.equals(getFailureMessage(), that.getFailureMessage())
        && Objects.equals(getFailureType(), that.getFailureType());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("FailureDetails(");

    ret.append("failureMessage=");
    ret.append(String.valueOf(failureMessage));
    ret.append(", ");

    ret.append("failureType=");
    ret.append(String.valueOf(failureType));
    ret.append(")");

    return ret.toString();
  }

}
