package com.amazon.eeyore.humanevaluation.model;

@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class PermissionID {

  private PermissionID() { throw new UnsupportedOperationException(); }

  public static final String SIGNATURE_READ = "SIGNATURE_READ";
  public static final String SIGNATURE_UPDATE = "SIGNATURE_UPDATE";
  public static final String SIGNATURE_OVERRIDE = "SIGNATURE_OVERRIDE";
  public static final String DETECTION_READ = "DETECTION_READ";

  private static final String[] values = {
    SIGNATURE_READ, SIGNATURE_UPDATE, SIGNATURE_OVERRIDE, DETECTION_READ
  };

  public static String[] values() {
    return values;
  }
}
