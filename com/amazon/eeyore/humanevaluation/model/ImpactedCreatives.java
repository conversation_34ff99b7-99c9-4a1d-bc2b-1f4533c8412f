package com.amazon.eeyore.humanevaluation.model;

import com.amazon.coral.annotation.Shape;
import com.amazon.coral.annotation.Wrapper;
import com.amazon.coral.annotation.XmlName;
import com.amazon.coral.annotation.XmlNamespace;
import com.amazon.coral.annotation.*;
import java.util.Arrays;
import java.util.Objects;

@Shape
@Wrapper(value={com.amazon.coral.annotation.WrapperType.INPUT,com.amazon.coral.annotation.WrapperType.OUTPUT})
@XmlName(value="ImpactedCreatives")
@XmlNamespace(value="http://internal.amazon.com/coral/com.amazon.eeyore.humanevaluation.model/")
@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class ImpactedCreatives extends java.lang.Object  {

  /**
   * Statically creates a builder instance for ImpactedCreatives.
   */
  public static Builder builder() {
    return new Builder();
  }

  /**
   * Fluent builder for instances of ImpactedCreatives.
   */
  @com.amazon.coral.annotation.Generated
  public static class Builder {

    protected int safeCreativesCount;
    /**
     * Sets the value of the field "safeCreativesCount" to be used for the constructed object.
     * @param safeCreativesCount
     *   The value of the "safeCreativesCount" field.
     * @return
     *   This builder.
     */
    public Builder withSafeCreativesCount(int safeCreativesCount) {
      this.safeCreativesCount = safeCreativesCount;
      return this;
    }

    protected int unsafeCreativesCount;
    /**
     * Sets the value of the field "unsafeCreativesCount" to be used for the constructed object.
     * @param unsafeCreativesCount
     *   The value of the "unsafeCreativesCount" field.
     * @return
     *   This builder.
     */
    public Builder withUnsafeCreativesCount(int unsafeCreativesCount) {
      this.unsafeCreativesCount = unsafeCreativesCount;
      return this;
    }

    protected int clicks;
    /**
     * Sets the value of the field "clicks" to be used for the constructed object.
     * @param clicks
     *   The value of the "clicks" field.
     * @return
     *   This builder.
     */
    public Builder withClicks(int clicks) {
      this.clicks = clicks;
      return this;
    }

    protected int impressions;
    /**
     * Sets the value of the field "impressions" to be used for the constructed object.
     * @param impressions
     *   The value of the "impressions" field.
     * @return
     *   This builder.
     */
    public Builder withImpressions(int impressions) {
      this.impressions = impressions;
      return this;
    }

    /**
     * Sets the fields of the given instances to the corresponding values recorded when calling the "with*" methods.
     * @param instance
     *   The instance to be populated.
     */
    protected void populate(ImpactedCreatives instance) {
      instance.setSafeCreativesCount(this.safeCreativesCount);
      instance.setUnsafeCreativesCount(this.unsafeCreativesCount);
      instance.setClicks(this.clicks);
      instance.setImpressions(this.impressions);
    }

    /**
     * Builds an instance of ImpactedCreatives.
     * <p>
     * The built object has its fields set to the values given when calling the "with*" methods of this builder.
     * </p>
     */
    public ImpactedCreatives build() {
      ImpactedCreatives instance = new ImpactedCreatives();

      populate(instance);

      return instance;
    }
  };

  private int safeCreativesCount;
  private int unsafeCreativesCount;
  private int clicks;
  private int impressions;

  public int getSafeCreativesCount() {
    return this.safeCreativesCount;
  }

  public void setSafeCreativesCount(int safeCreativesCount) {
    this.safeCreativesCount = safeCreativesCount;
  }

  public int getUnsafeCreativesCount() {
    return this.unsafeCreativesCount;
  }

  public void setUnsafeCreativesCount(int unsafeCreativesCount) {
    this.unsafeCreativesCount = unsafeCreativesCount;
  }

  public int getClicks() {
    return this.clicks;
  }

  public void setClicks(int clicks) {
    this.clicks = clicks;
  }

  public int getImpressions() {
    return this.impressions;
  }

  public void setImpressions(int impressions) {
    this.impressions = impressions;
  }

  private static final int classNameHashCode =
      internalHashCodeCompute("com.amazon.eeyore.humanevaluation.model.ImpactedCreatives");

  /**
   * HashCode implementation for ImpactedCreatives
   * based on java.util.Arrays.hashCode
   */
  @Override
  public int hashCode() {
    return internalHashCodeCompute(
        classNameHashCode,
        getSafeCreativesCount(),
        getUnsafeCreativesCount(),
        getClicks(),
        getImpressions());
  }

  private static int internalHashCodeCompute(java.lang.Object... objects) {
    return Arrays.hashCode(objects);
  }

  /**
   * Equals implementation for ImpactedCreatives
   * based on instanceof and Object.equals().
   */
  @Override
  public boolean equals(final java.lang.Object other) {
    if (this == other) {
      return true;
    }

    if (!(other instanceof ImpactedCreatives)) {
      return false;
    }

    ImpactedCreatives that = (ImpactedCreatives) other;

    return
        Objects.equals(getSafeCreativesCount(), that.getSafeCreativesCount())
        && Objects.equals(getUnsafeCreativesCount(), that.getUnsafeCreativesCount())
        && Objects.equals(getClicks(), that.getClicks())
        && Objects.equals(getImpressions(), that.getImpressions());
  }

  /**
   * Returns a string representation of this object. The content of any types marked with the
   * <a href="https://w.amazon.com/index.php/Coral/Model/XML/Traits#Sensitive">sensitive</a>
   * trait will be redacted.
   * <p/>
   * <b>Do not attempt to parse the string returned by this method.</b> Coral's <tt>toString</tt>
   * format is undefined and subject to change. To obtain a proper machine-readable representation
   * of this object, use Coral Serialize directly.
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/Manual">Coral Serialize Manual</a>
   * @see <a href="https://w.amazon.com/index.php/Coral/Serialize/FAQ">Coral Serialize FAQ</a>
   */
  @Override
  public String toString() {
    StringBuilder ret = new StringBuilder();
    ret.append("ImpactedCreatives(");

    ret.append("safeCreativesCount=");
    ret.append(String.valueOf(safeCreativesCount));
    ret.append(", ");

    ret.append("unsafeCreativesCount=");
    ret.append(String.valueOf(unsafeCreativesCount));
    ret.append(", ");

    ret.append("clicks=");
    ret.append(String.valueOf(clicks));
    ret.append(", ");

    ret.append("impressions=");
    ret.append(String.valueOf(impressions));
    ret.append(")");

    return ret.toString();
  }

}
