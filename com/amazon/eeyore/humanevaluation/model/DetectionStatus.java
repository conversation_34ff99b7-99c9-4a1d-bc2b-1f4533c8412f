package com.amazon.eeyore.humanevaluation.model;

@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class DetectionStatus {

  private DetectionStatus() { throw new UnsupportedOperationException(); }

  public static final String PENDING_VERIFICATION = "PENDING_VERIFICATION";
  public static final String UNSAFE = "UNSAFE";
  public static final String FALSE_POSITIVE = "FALSE_POSITIVE";
  public static final String SHADOW = "SHADOW";

  private static final String[] values = {
    PENDING_VERIFICATION, UNSAFE, FALSE_POSITIVE, SHADOW
  };

  public static String[] values() {
    return values;
  }
}
