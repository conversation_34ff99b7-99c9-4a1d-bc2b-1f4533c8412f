package com.amazon.eeyore.humanevaluation.model;

@com.amazon.coral.annotation.Generated
@com.amazon.coral.annotation.CoralSuppressWarnings
public class FailureType {

  private FailureType() { throw new UnsupportedOperationException(); }

  public static final String BLANK = "BLANK";
  public static final String AREA_TOO_SMALL = "AREA_TOO_SMALL";
  public static final String MISSING_SCREENSHOTS = "MISSING_SCREENSHOTS";
  public static final String TOO_FEW_REQUESTS = "TOO_FEW_REQUESTS";
  public static final String CLOUDFLARE_BLOCKED = "CLOUDFLARE_BLOCKED";
  public static final String RENDER_DATA_LIMIT_BREACHED = "RENDER_DATA_LIMIT_BREACHED";
  public static final String UNAVAILABLE_PROXY = "UNAVAILABLE_PROXY";
  public static final String TRUNCATED_RENDER = "TRUNCATED_RENDER";
  public static final String INTERNAL_SERVICE_ERROR = "INTERNAL_SERVICE_ERROR";
  public static final String UNKNOWN_LANDING_PAGE_PROTOCOL = "UNKNOWN_LANDING_PAGE_PROTOCOL";
  public static final String UNKNOWN_EXCEPTION = "UNKNOWN_EXCEPTION";
  public static final String MISSING_SCREENCAST_FRAMES = "MISSING_SCREENCAST_FRAMES";

  private static final String[] values = {
    BLANK, AREA_TOO_SMALL, MISSING_SCREENSHOTS, TOO_FEW_REQUESTS, CLOUDFLARE_BLOCKED, RENDER_DATA_LIMIT_BREACHED, UNAVAILABLE_PROXY, TRUNCATED_RENDER, INTERNAL_SERVICE_ERROR, UNKNOWN_LANDING_PAGE_PROTOCOL, UNKNOWN_EXCEPTION, MISSING_SCREENCAST_FRAMES
  };

  public static String[] values() {
    return values;
  }
}
