package com.amazon.eeyore;

import com.amazon.coral.annotation.Generated;
import com.amazon.coral.model.DefaultModelIndexFactory;
import com.amazon.coral.model.ModelIndex;
import com.amazon.coral.model.ModelIndexFactory;
import com.amazon.coral.model.xml.SourceReader;
import com.amazon.coral.model.xml.XmlModelIndexFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;

import static java.nio.charset.StandardCharsets.UTF_8;

@Generated
public final class GeneratedModelIndexFactory implements ModelIndexFactory {
    private final ModelIndex modelIndex;

    public ModelIndex newModelIndex() {
        return modelIndex;
    }

    public GeneratedModelIndexFactory() throws IOException {
        String xmlFileName = "GeneratedModelIndexFactory.xml";
        URL resource = GeneratedModelIndexFactory.class.getResource(xmlFileName);
        if (resource == null) {
            throw new NullPointerException("Unable to load XML model " + xmlFileName);
        }
        try (InputStream inputStream = resource.openStream()) {
            SourceReader reader = new SourceReader(xmlFileName, new InputStreamReader(inputStream, UTF_8));
            this.modelIndex = new DefaultModelIndexFactory(new XmlModelIndexFactory(reader)).newModelIndex();
        }
    }
}

