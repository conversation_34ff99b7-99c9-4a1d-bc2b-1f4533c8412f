'use strict';
const fs = require("fs-extra");
const path = require("path");

console.log("Copying coverage");

fs.mkdirpSync(path.resolve(__dirname, "../build/brazil-documentation/coverage"));
fs.mkdirpSync(path.resolve(__dirname, "../build/generated-make"));

// Coverage for coverlay https://w.amazon.com/index.php/Coverlay/FAQ
fs.copySync(path.resolve(__dirname, "../build/coverage/cobertura-coverage.xml"), path.resolve(__dirname, "../build/brazil-documentation/coverage/coverage.xml"));

// Coverage HTML output
fs.copySync(path.resolve(__dirname, "../build/coverage/lcov-report"), path.resolve(__dirname, "../build/brazil-unit-tests"));

// Generate coverage-data file for PackageBuilder: https://w.amazon.com/index.php/PackageBuilder/CodeCoverage
const coverage = require("../build/coverage/coverage-summary.json");
const coverageData = "typescript:line:" + coverage.total.lines.pct + "\n" +
                     "typescript:branch:" + coverage.total.branches.pct + "\n"; 
fs.writeFileSync(path.resolve(__dirname, "../build/generated-make/coverage-data.txt"), coverageData);

console.log("Done!");
