'use strict';

console.log('Running copy-artifacts build step...');
const path = require('path'); // https://www.w3schools.com/nodejs/ref_path.asp
const fse = require('fs-extra'); // https://github.com/jprichardson/node-fs-extra

const appDir = fse.realpathSync(process.cwd());
const publicDir = path.resolve(appDir, 'public');
const staticDir = path.resolve(appDir, 'build/static');
const assetManifestFile = path.resolve(appDir, 'build/asset-manifest.json');
const indexFile = path.resolve(appDir, 'build/index.html');
const targetBaseDir = path.resolve(appDir, 'build/packaging_additional_published_artifacts');
const targetAssetManifestFile = path.resolve(targetBaseDir, 'asset-manifest.json');
const targetIndexFile = path.resolve(targetBaseDir, 'index.html');
const targetStaticDir = path.resolve(targetBaseDir, 'static');

const configurationDir = path.resolve(appDir, 'configuration/');
const buildDir = path.resolve(appDir, 'build/');

function copySync(source, destination) {
    console.log('Copy ' + source + ' to ' + destination);
    fse.copySync(source, destination, {
        dereference: true
    });
}

console.log('Empty the target dir: ' + targetBaseDir);
fse.emptyDirSync(targetBaseDir);

copySync(publicDir, targetBaseDir);
copySync(indexFile, targetIndexFile);
copySync(assetManifestFile, targetAssetManifestFile);
copySync(staticDir, targetStaticDir);
copySync(configurationDir, buildDir);

console.log('Completed copy-artifacts build step');
