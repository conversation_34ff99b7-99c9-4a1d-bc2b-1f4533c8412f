# -*-perl-*-

package.EeyoreHumanEvaluationLambdaWebsite = {
    interfaces = (1.1);

    deploy = {
        generic = true;
    };

    build-environment = {
        chroot = basic;
        network-access = blocked;
    };

    build-system = npm-pretty-much;

    build-tools = {
        1.1 = {
            NpmPrettyMuch = 1.1;
            NodeJS = 18.x;
        };
    };

    dependencies = {
        1.1 = {
            NodeJS = 18.x;

            MidwayIdentityCredentialProvider = 3.0;

            EeyoreHumanEvaluationJavascriptClient = 1;
        };
    };
};
