package.EeyoreHumanEvaluation = {
    interfaces = (1.0);
    build-system = brazil-gradle;

    build-tools = {
        1.0 = {
            JDK17 = 1.0;

            # BrazilGradle
            BrazilGradle = 8.x;
            BrazilGradleJavaPresets = 8.x;
            BrazilGradleKotlinPresets = 8.x;
            BrazilGradleJavaWrapperGenerator = 8.x;
            BrazilGradleValidateClasspathPlugin = 8.x;

            # Kotlin
            KotlinGradlePlugin = 2.x;
            KotlinTrailsGradle = 2.x;

            EeyoreHumanEvaluationLambdaModel = 1.0;
            CoralGenerator = 1.2;
            CopyConfigurationGradlePlugin = 1.0;
        };
    };

    dependencies = {
        1.0 = {
            KotlinStdlib = 2.x;
            KotlinStdlibJdk8 = 2.x;
            AmazonAppConfigJava = 2.0;
            AmazonCoralEmfReporter = 1.4;
            ApolloShimOpConfigHelpers = 2.1;
            ApolloShimSetup = 7.0;
            Bobcat = 3.0;
            CoralGuice = 1.1_5.x;
            CoralOrchestrator = 1.1;
            CoralJsonSupport = 1.1;
            CoralService = 1.1;

            Maven-com-google-guava_guava = 32.x;
            Maven-com-google-inject_guice = 5.x;
            Maven-io-github-oshai_kotlin-logging-jvm = 6.x;
            AdRiskServiceCommon = 2.0;

            # AWS SDK v1.12 needed for CloudAuth Credentials only
            AWSSecurityTokenServiceJavaClient = 1.12.x;

            # BRASS
            AmazonCoralAwsV4SigningCallVisitor = 2.0;
            BrassServiceJavaClient = 1.1_awsauth;

            EriskayJavaClient = 1.0;
            EeyoreHumanEvaluationLambdaModel = 1.0;

            # Required for resolves
            JakartaCommons-cli = 1.x;

            # Woozle
            WoozleThreatStoreJavaClient = 1.0;


            CoralAnnotation = 1.1;
            CoralEmbeddedMetricsSupport = 1.0;
            CoralModel = 1.1;
        };
    };

    runtime-dependencies = {
        1.0 = {
            AmazonCACerts = 1.0;
            JDK17 = 1.0;
            log4j = 2.x;

            # a JSON-P implementation required for CloudAuth
            Maven-org-glassfish_jakarta_json = 1.x;

            # Coral CloudAuth
            CoralClientBuilder = 1.1;
            CoralClientCloudAuthSupport = 1.1;
            CoralClientHttp = 1.1;
            CoralRpcSupport = 1.1;
            WoozleThreatStoreClientConfiguration = 1.0;

            # Eriskay
            EriskayClientConfig = 1.0;
        };
    };


    test-dependencies = {
        1.0 = {
            AdRiskTestFramework = 2.0;
            JUnit5 = 5.x;
            Maven-org-hamcrest_java-hamcrest = 2.x;
            Mockito-junit-jupiter = 5.x;
            Mockito-kotlin = 5.x;
        };
    };

    resolves-conflict-dependencies = {
        1.0 = {
            KotlinStdlib = 2.x;
            KotlinStdlibJdk8 = 2.x;

            AdRiskCommon = 2.0;
        };
    };

    remove-dependencies = {
        1.0 = {
            # This comes from AdRiskServiceCommon, and doesn't work because we don't manage the Human Eval service using
            # Process Manager. Eriskay uses Odin, which registers with PM and so PM works correctly, but with no
            # services to manage, PM thinks it's misconfigured and FATALs.
            MinimalProcessManager = 3.0;
            Maven-javax-annotation_javax_annotation-api = 1.x;
        };
    };

    targets = {
        EeyoreHumanEvaluation-1.0 = {
            type = javalibrary;
        };
    };
};
