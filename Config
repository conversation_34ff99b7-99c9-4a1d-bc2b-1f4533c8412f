# -*-perl-*-

package.EeyoreHumanEvaluationLambdaJavaClient = {
    build-system = happytrails;

    interfaces = (1.0);

    deploy = {
        generic = true;
        map = (default,
                 "-generated-test/**",
                 "-copied-tst/**",
                 "-smoketests/**",
                 "-src-files/**");
    };

    build-environment = {
        chroot = basic;
        network-access = blocked;
    };

    scope = interface;

    test-dependencies = {
        1.0 = {
          junit = 4.13;
        };
    };
    dependencies = {
        1.0 = {
          AWSJavaClientRuntime = 1.12.x;
        };
    };
    runtime-dependencies = {
        1.0 = {
            AmazonCACerts = 1.0;
        };
    };

    build-tools = {
        1.0 = {
            JDK8 = 1.0;
            HappierTrails = 3.6;
            EeyoreHumanEvaluationLambdaModel = 1.0;
            AWSJavaClientC2jCodeGenerator = 1.12.x;
        };
    };

    targets = {
        EeyoreHumanEvaluationLambdaJavaClient-1.0 = {
            type = javalibrary;
        };
    };
};
