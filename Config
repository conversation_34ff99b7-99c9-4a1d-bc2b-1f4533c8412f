# -*-perl-*- 

package.EeyoreHumanEvaluationLambdaModel = {
    build-system = happytrails-and-coral2swagger;
    interfaces = (1.0);

    flavors = {
        map = single;
        generation = 1;
    };
    scope = interface;

    deploy = {
        generic = true;
    };

    build-environment = {
        chroot = basic;
        network-access = blocked;
    };

    dependencies = {
        1.0 = {
        };
    };

    build-tools = {
        1.0 = {
            JDK8 = 1.0;
            HappierTrails = 3.6;
            CoralGenerator = 1.2;
            Coral2Swagger = 1.0;
        };
    };

    targets = {
        model = {
            type = coralmodel;
        };
    };
};
