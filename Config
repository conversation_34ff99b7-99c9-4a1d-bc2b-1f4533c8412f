package.EeyoreHumanEvaluationCDK = {
    interfaces = (1.0);

    deploy = {
        generic = true;
    };

    build-environment = {
        chroot = basic;
        network-access = blocked;
    };

    build-system = cdk-build;
    build-tools = {
        1.0 = {
            CDKBuild = 4.x;
            NodeJS = 18.x;
            NpmPrettyMuch = 1.1;
        };
    };

    dependencies = {
        1.0 = {
            AdRiskCommonCDKConstructs = 3.0;
            AdRiskCommonCDKSupport = 1.0;
            BrassOnboardingCDKConstructs = 1.0;
        };
    };

    runtime-dependencies = {
        1.0 = {
            NodeJS = 18.x;
            EeyoreHumanEvaluationLambdaModel = 1.0;
        };
    };

    test-dependencies = {
        1.0 = {
            EeyoreHumanEvaluationLambdaWebsite = 1.1;
            EeyoreHumanEvaluation = 1.0;
            EeyoreHumanEvaluationImageBuild = 1.0;
            EeyoreHumanEvaluationLogImageBuild = 1.0;
            BrassOnboardingLambda = 1.0;
        };
    };
};
